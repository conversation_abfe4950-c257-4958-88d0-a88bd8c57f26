from typing import Any, Optional, List
from fastapi import APIRouter, Depends, HTTPException, Request, Query
from sqlalchemy import and_, or_, func
from sqlalchemy.orm import Session, joinedload
from database import get_db
from models import UserPermissionFrontend, RulesOpenseas, SalaCrm, ClueSheet, SalaCrmOpenseas, SalaCrmToOpenseas, User
from datetime import datetime, timedelta
from pydantic import BaseModel
from auth import get_current_user, base64_decrypt_data
import json
import logging

logger = logging.getLogger(__name__)

router = APIRouter()

# 公海转换规则配置

# Pydantic 模型定义
class RulesOpenseasCreate(BaseModel):
    """创建公海转换规则的请求模型"""
    weigh: Optional[int] = 99  # 优先级
    rule_type: Optional[int] = 0  # 规则类型（0：默认条件，1：特定条件，2：排除条件）
    channel: Optional[str] = None  # 渠道
    queue: Optional[int] = None  # 线索类型 0付费店铺，1免费店铺
    fname: Optional[str] = None  # 字段名称
    tip: Optional[str] = None  # 条件符号
    fvalue: Optional[str] = None  # 字段值
    hours: Optional[int] = 0  # 时间(H)
    switch_stages: Optional[int] = None  # 转换策略 (0:组内转换，1:跨组转换)
    cf_contact_person: Optional[int] = 0  # 对接人重复 0:不能，1:可以
    remarks: Optional[str] = None  # 说明

class RulesOpenseasUpdate(BaseModel):
    """更新公海转换规则的请求模型"""
    weigh: Optional[int] = None
    rule_type: Optional[int] = None
    channel: Optional[str] = None
    queue: Optional[int] = None
    fname: Optional[str] = None
    fvalue: Optional[str] = None
    tip: Optional[str] = None
    hours: Optional[int] = None
    switch_stages: Optional[int] = None
    cf_contact_person: Optional[int] = None
    remarks: Optional[str] = None

class BatchRulesOpenseasCreate(BaseModel):
    """批量创建公海转换规则的请求模型"""
    rules: List[RulesOpenseasCreate]

class RulesOpenseasUpdateWithId(BaseModel):
    """带ID的更新公海转换规则的请求模型"""
    id: int  # 必需的ID字段
    weigh: Optional[int] = None
    rule_type: Optional[int] = None
    channel: Optional[str] = None
    queue: Optional[int] = None
    fname: Optional[str] = None
    fvalue: Optional[str] = None
    tip: Optional[str] = None
    hours: Optional[int] = None
    switch_stages: Optional[int] = None
    cf_contact_person: Optional[int] = None
    remarks: Optional[str] = None

class BatchRulesOpenseasUpdate(BaseModel):
    """批量更新公海转换规则的请求模型"""
    rules: List[RulesOpenseasUpdateWithId]



# 1. 获取公海转换规则列表（支持分页和筛选）
@router.get("/api/rules_openseas/list")
def get_rules_openseas_list(request: Request,db: Session = Depends(get_db)):
    try:
        # 用户权限验证
        user = get_current_user(request, db)
        if not user:
            raise HTTPException(status_code=400, detail="未登录")
        # 构建查询条件
        query = db.query(RulesOpenseas).filter(RulesOpenseas.is_delete == 0)

        # 获取总数
        total = query.count()

        # 分页查询，按优先级排序
        rules = query.order_by(RulesOpenseas.weigh.asc()).all()

        # 转换为字典格式
        rules_data = [rule.to_dict() for rule in rules]

        return {
            "success": True,
            "data": rules_data,
            "total": total
        }

    except Exception as e:
        return HTTPException(status_code=400, detail=f"获取规则列表失败: {str(e)}")
    


# 2. 根据ID获取单个公海转换规则
# @router.get("/api/rules_openseas/{rule_id}")
# def get_rules_openseas_by_id(rule_id: int,request: Request,db: Session = Depends(get_db)):
#     try:
#         # 用户权限验证
#         user = get_current_user(request, db)
#         if not user:
#             return HTTPException(status_code=400, detail="未登录")
#         rule = db.query(RulesOpenseas).filter(RulesOpenseas.id == rule_id,RulesOpenseas.is_delete == 0).first()

#         if not rule:
#             return HTTPException(status_code=400, detail="规则不存在")

#         return {
#             "success": True,
#             "data": rule.to_dict()
#         }

#     except HTTPException:
#         raise
#     except Exception as e:
#         return HTTPException(status_code=400, detail=f"获取规则详情失败: {str(e)}")




# 3. 创建新的公海转换规则（简化版）
@router.post("/api/rules_openseas/create")
def create_rules_openseas(
    rule_data: RulesOpenseasCreate,
    request: Request,
    db: Session = Depends(get_db)
):
    try:
        # 创建新规则实例，使用默认值填充未提供的字段
        new_rule = RulesOpenseas(
            weigh=rule_data.weigh if rule_data.weigh is not None else 99,
            rule_type=rule_data.rule_type if rule_data.rule_type is not None else 0,
            channel=rule_data.channel,
            queue=rule_data.queue if rule_data.queue is not None else None,
            fname=rule_data.fname,
            fvalue=rule_data.fvalue,
            tip=rule_data.tip,
            hours=rule_data.hours if rule_data.hours is not None else 0,
            switch_stages=rule_data.switch_stages if rule_data.switch_stages is not None else None,
            cf_contact_person=rule_data.cf_contact_person if rule_data.cf_contact_person is not None else 0,
            remarks=rule_data.remarks,
            is_delete=0
        )

        # 保存到数据库
        db.add(new_rule)
        db.commit()
        db.refresh(new_rule)

        # 只返回ID
        return {
            "success": True,
            "id": new_rule.id
        }

    except Exception as e:
        db.rollback()
        raise HTTPException(status_code=500, detail=f"创建规则失败: {str(e)}")
    


# # 4. 更新公海转换规则
# @router.put("/api/rules_openseas/{rule_id}")
# def update_rules_openseas(rule_id: int,rule_data: RulesOpenseasUpdate,request: Request,db: Session = Depends(get_db)):
#     try:
#         # 用户权限验证
#         user = get_current_user(request, db)
#         if not user:
#             return HTTPException(status_code=400, detail="未登录")
#         # 查找要更新的规则
#         rule = db.query(RulesOpenseas).filter(RulesOpenseas.id == rule_id,RulesOpenseas.is_delete == 0).first()
#         if not rule:
#             return HTTPException(status_code=400, detail="规则不存在")

#         # 更新字段（只更新非None的字段）
#         update_data = rule_data.model_dump(exclude_unset=True)
#         for field, value in update_data.items():
#             if hasattr(rule, field):
#                 setattr(rule, field, value)

#         # 保存更改
#         db.commit()
#         db.refresh(rule)

#         return {
#             "success": True,
#             "message": "规则更新成功",
#             "data": rule.to_dict()
#         }

#     except HTTPException:
#         raise
#     except Exception as e:
#         db.rollback()
#         raise HTTPException(status_code=500, detail=f"更新规则失败: {str(e)}")


    
# 4.1 批量更新公海转换规则
@router.put("/api/rules_openseas/batch_update")
def batch_update_rules_openseas(
    rules_data: List[RulesOpenseasUpdateWithId],
    request: Request,
    db: Session = Depends(get_db)
):
    """
    批量更新公海转换规则

    Args:
        rules_data: 规则更新数据列表，每个元素必须包含id字段
        request: 请求对象
        db: 数据库会话

    Returns:
        更新结果，包含成功和失败的规则信息
    """
    try:
        # 用户权限验证
        user = get_current_user(request, db)
        if not user:
            return HTTPException(status_code=400, detail="未登录")
        if not rules_data:
            return HTTPException(status_code=400, detail="规则数据不能为空")

        updated_rules = []
        failed_rules = []

        # 批量更新规则
        for index, rule_data in enumerate(rules_data):
            try:
               
                # 获取更新数据
                update_data = rule_data.model_dump(exclude_unset=True)
                rule_id = update_data.pop('id')  # 移除id字段，避免更新id
                #return rule_id
                # 查找要更新的规则
                rule = db.query(RulesOpenseas).filter(
                    RulesOpenseas.id == rule_id,
                    RulesOpenseas.is_delete == 0
                ).first()

                if not rule:
                    failed_rules.append({
                        "index": index,
                        "error": f"规则ID {rule_id} 不存在",
                        "data": update_data
                    })
                    continue

                # 更新字段（只更新非None的字段）
                for field, value in update_data.items():
                    if hasattr(rule, field):
                        setattr(rule, field, value)

                updated_rules.append({
                    "index": index,
                    "id": rule.id,
                    "data": rule.to_dict()
                })

            except Exception as e:
                failed_rules.append({
                    "index": index,
                    "error": str(e),
                    "data": rule_data.model_dump()
                })

        # 统一提交所有成功的更新
        if updated_rules:
            db.commit()
        else:
            db.rollback()

        return {
            "success": len(updated_rules) > 0,
            "message": f"成功更新 {len(updated_rules)} 条规则，失败 {len(failed_rules)} 条",
            "updated_count": len(updated_rules),
            "failed_count": len(failed_rules),
            "updated_rules": updated_rules,
            "failed_rules": failed_rules
        }

    except Exception as e:
        db.rollback()
        raise HTTPException(status_code=500, detail=f"批量更新规则失败: {str(e)}")


# 5. 删除公海转换规则（软删除）
@router.delete("/api/rules_openseas/{rule_id}")
def delete_rules_openseas(rule_id: int,request: Request,db: Session = Depends(get_db)):
    try:
        # 用户权限验证
        user = get_current_user(request, db)
        if not user:
            return HTTPException(status_code=400, detail="未登录")
        # 查找要删除的规则
        rule = db.query(RulesOpenseas).filter(RulesOpenseas.id == rule_id,RulesOpenseas.is_delete == 0).first()
        if not rule:
            return HTTPException(status_code=400, detail="规则不存在")

        # 软删除：设置is_delete为1
        rule.is_delete = 1

        # 保存更改
        db.commit()

        return {
            "success": True,
            "message": "规则删除成功"
        }

    except HTTPException:
        raise
    except Exception as e:
        db.rollback()
        return HTTPException(status_code=400, detail=f"删除规则失败: {str(e)}")
    



# 辅助方法：获取用户组信息
def get_user_group_info(contact_person: str, db: Session) -> dict:
    """
    根据对接人获取用户组信息

    Args:
        contact_person: 对接人姓名
        db: 数据库会话

    Returns:
        dict: 包含用户组信息的字典
    """
    try:
        user = db.query(User).filter(User.name == contact_person).first()
        if not user:
            return {"group_name": None, "identity": None}

        return {
            "group_name": user.group_name,
            "identity": user.identity,
            "department": user.department
        }
    except Exception:
        return {"group_name": None, "identity": None}


# 辅助方法：检查条件匹配
def check_condition_match(field_value, condition_operator: str, target_value: str) -> bool:
    """
    检查字段值是否满足条件

    Args:
        field_value: 字段值
        condition_operator: 条件符号 (=, !=, >, <, 等)
        target_value: 目标值

    Returns:
        bool: 是否匹配
    """
    if field_value is None:
        return False

    try:
        # 转换为字符串进行比较
        field_str = str(field_value)
        target_str = str(target_value)

        if condition_operator == "=" or condition_operator == "等于":
            return field_str == target_str
        elif condition_operator == "!=" or condition_operator == "不等于":
            return field_str != target_str
        elif condition_operator == ">" or condition_operator == "大于":
            try:
                return float(field_str) > float(target_str)
            except ValueError:
                return field_str > target_str
        elif condition_operator == "<" or condition_operator == "小于":
            try:
                return float(field_str) < float(target_str)
            except ValueError:
                return field_str < target_str
        elif condition_operator == ">=" or condition_operator == "大于等于":
            try:
                return float(field_str) >= float(target_str)
            except ValueError:
                return field_str >= target_str
        elif condition_operator == "<=" or condition_operator == "小于等于":
            try:
                return float(field_str) <= float(target_str)
            except ValueError:
                return field_str <= target_str
        elif condition_operator == "包含":
            return target_str in field_str
        elif condition_operator == "不包含":
            return target_str not in field_str
        else:
            return False
    except Exception:
        return False



# 6. 公海转换主接口
@router.get("/api/rules_openseas/zhuanhua")
def zhuanhuan_rules_openseas(request: Request, db: Session = Depends(get_db)):
    
    try:

        # 1. 查询所有有效的转换规则
        rules = db.query(RulesOpenseas).filter(RulesOpenseas.is_delete == 0).order_by(RulesOpenseas.weigh.asc()).all()

        if not rules:
            return {
                "success": True,
                "message": "没有找到有效的转换规则",
                "processed_count": 0
            }
        return rules
        processed_ids = set()  # 用于记录已处理的ID，避免重复
        total_processed = 0
        rule_results = []

        # 2. 遍历每个规则进行匹配
        for rule in rules:
            rule_matched_ids = []
            return rule

            try:
                # 根据规则类型进行不同的数据匹配
                if rule.rule_type == 1:  # 特定条件：在SalaCrmToOpenseas中匹配
                    matched_ids = process_specific_condition_rule(rule, db)
                elif rule.rule_type in [0, 2]:  # 默认条件和排除条件：在SalaCrm和ClueSheet中匹配
                    matched_ids = process_default_exclude_condition_rule(rule, db)
                else:
                    continue

                # 过滤掉已处理的ID
                new_matched_ids = [id for id in matched_ids if id not in processed_ids]

                if new_matched_ids:
                    # 将匹配的数据添加到SalaCrmOpenseas表
                    success_count = add_to_openseas_table(new_matched_ids, rule, db)

                    # 更新状态
                    update_status_after_transfer(new_matched_ids, rule.rule_type, db)

                    # 记录已处理的ID
                    processed_ids.update(new_matched_ids)
                    total_processed += success_count

                    rule_results.append({
                        "rule_id": rule.id,
                        "rule_type": rule.rule_type,
                        "matched_count": len(new_matched_ids),
                        "success_count": success_count
                    })

            except Exception as e:
                logger.error(f"处理规则 {rule.id} 时出错: {str(e)}")
                rule_results.append({
                    "rule_id": rule.id,
                    "rule_type": rule.rule_type,
                    "error": str(e)
                })

        # 提交所有更改
        db.commit()

        return {
            "success": True,
            "message": f"公海转换完成，共处理 {total_processed} 条数据",
            "total_processed": total_processed,
            "rule_results": rule_results
        }

    except Exception as e:
        db.rollback()
        logger.error(f"公海转换失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"公海转换失败: {str(e)}")


# 处理特定条件规则（rule_type=1）
def process_specific_condition_rule(rule: RulesOpenseas, db: Session) -> List[str]:
    """
    处理特定条件规则，在SalaCrmToOpenseas表中匹配数据
    """
    try:
        # 基础查询条件
        query = db.query(SalaCrmToOpenseas.lead_id).filter(
            SalaCrmToOpenseas.is_delete == 0,
            SalaCrmToOpenseas.is_trans == 0
        )

        # 添加时间条件（以ftime为开始时间）
        if rule.hours and rule.hours > 0:
            time_threshold = datetime.now() - timedelta(hours=rule.hours)
            query = query.filter(SalaCrmToOpenseas.ftime <= time_threshold)

        # 添加字段名称条件
        if rule.fname:
            query = query.filter(SalaCrmToOpenseas.fname == rule.fname)

        # 获取候选数据
        candidates = query.all()
        matched_ids = []

        # 对每个候选数据进行详细匹配
        for candidate in candidates:
            lead_id = candidate.lead_id

            # 获取关联的ClueSheet和SalaCrm数据
            clue_sheet = db.query(ClueSheet).filter(ClueSheet.ID == lead_id).first()
            sala_crm = db.query(SalaCrm).filter(SalaCrm.ID == lead_id).first()

            if not clue_sheet or not sala_crm:
                continue

            # 检查渠道匹配
            if rule.channel and clue_sheet.channel != rule.channel:
                continue

            # 检查队列类型匹配
            if rule.queue != -1 and int(clue_sheet.queue) != rule.queue:
                continue

            # 检查字段值匹配
            if rule.fname and rule.fvalue and rule.tip:
                field_value = get_field_value_from_tables(rule.fname, sala_crm, clue_sheet)
                if not check_condition_match(field_value, rule.tip, rule.fvalue):
                    continue

            matched_ids.append(lead_id)

        return matched_ids

    except Exception as e:
        logger.error(f"处理特定条件规则失败: {str(e)}")
        return []


# 处理默认条件和排除条件规则（rule_type=0,2）
def process_default_exclude_condition_rule(rule: RulesOpenseas, db: Session) -> List[str]:
    """
    处理默认条件和排除条件规则，在SalaCrm和ClueSheet表中匹配数据
    """
    try:
        # 基础查询：SalaCrm和ClueSheet联查，条件：os_status==0
        query = db.query(SalaCrm.ID).join(
            ClueSheet, SalaCrm.ID == ClueSheet.ID
        ).filter(
            SalaCrm.os_status == 0
        )

        # 添加渠道条件
        if rule.channel:
            query = query.filter(ClueSheet.channel == rule.channel)

        # 添加队列类型条件
        if rule.queue != -1:
            query = query.filter(ClueSheet.queue == str(rule.queue))

        # 添加时间条件（以allocation_date为开始时间）
        if rule.hours and rule.hours > 0:
            time_threshold = datetime.now() - timedelta(hours=rule.hours)
            query = query.filter(SalaCrm.allocation_date <= time_threshold)

        # 获取候选数据
        candidates = query.all()
        matched_ids = []

        # 对每个候选数据进行详细匹配
        for candidate in candidates:
            lead_id = candidate.ID

            # 获取完整的数据
            sala_crm = db.query(SalaCrm).filter(SalaCrm.ID == lead_id).first()
            clue_sheet = db.query(ClueSheet).filter(ClueSheet.ID == lead_id).first()

            if not sala_crm or not clue_sheet:
                continue

            # 检查字段值匹配
            if rule.fname and rule.fvalue and rule.tip:
                field_value = get_field_value_from_tables(rule.fname, sala_crm, clue_sheet)
                condition_matched = check_condition_match(field_value, rule.tip, rule.fvalue)

                # 排除条件：匹配则排除，默认条件：匹配则包含
                if rule.rule_type == 2:  # 排除条件
                    if condition_matched:
                        continue  # 匹配则排除
                elif rule.rule_type == 0:  # 默认条件
                    if not condition_matched:
                        continue  # 不匹配则排除

            matched_ids.append(lead_id)

        return matched_ids

    except Exception as e:
        logger.error(f"处理默认/排除条件规则失败: {str(e)}")
        return []


# 获取字段值的辅助方法
def get_field_value_from_tables(field_name: str, sala_crm: SalaCrm, clue_sheet: ClueSheet):
    """
    从SalaCrm或ClueSheet表中获取指定字段的值
    """
    try:
        # 首先尝试从SalaCrm表获取
        if hasattr(sala_crm, field_name):
            return getattr(sala_crm, field_name)

        # 然后尝试从ClueSheet表获取
        if hasattr(clue_sheet, field_name):
            return getattr(clue_sheet, field_name)

        return None
    except Exception:
        return None


# 将匹配的数据添加到SalaCrmOpenseas表
def add_to_openseas_table(lead_ids: List[str], rule: RulesOpenseas, db: Session) -> int:
    """
    将匹配的线索ID添加到SalaCrmOpenseas表
    """
    success_count = 0

    try:
        for lead_id in lead_ids:
            # 检查是否已存在
            existing = db.query(SalaCrmOpenseas).filter(SalaCrmOpenseas.ID == lead_id).first()
            if existing:
                continue

            # 获取原始数据
            sala_crm = db.query(SalaCrm).filter(SalaCrm.ID == lead_id).first()
            if not sala_crm:
                continue

            # 创建SalaCrmOpenseas记录
            openseas_record = SalaCrmOpenseas(
                ID=lead_id,
                SN=sala_crm.SN,
                is_add=sala_crm.is_add,
                add_date=sala_crm.add_date,
                reason_failure=sala_crm.reason_failure,
                allocation_date=datetime.now(),  # 设置为当前时间
                wechat_name=sala_crm.wechat_name,
                customer_name=sala_crm.customer_name,
                contact_person_openseas=None,  # 公海中暂时没有对接人
                is_billed=sala_crm.is_billed,
                bill_number=sala_crm.bill_number,
                bill_riqi=sala_crm.bill_riqi,
                bill_use_time=sala_crm.bill_use_time,
                customer_record=sala_crm.customer_record,
                customer_record_images=sala_crm.customer_record_images,
                keyword_tips=sala_crm.keyword_tips,
                clue_stage=sala_crm.clue_stage,
                self_reminder_flag=sala_crm.self_reminder_flag,
                last_followup_time=sala_crm.last_followup_time,
                clue_basic_tag=sala_crm.clue_basic_tag,
                last_followup_record=sala_crm.last_followup_record,
                failure_analysis=sala_crm.failure_analysis,
                is_read=0,  # 重置为未读
                is_deleted=0,
                clue_stage_follow_time=sala_crm.clue_stage_follow_time,
                clue_stage_follow_seeded=sala_crm.clue_stage_follow_seeded,
                chat_image=sala_crm.chat_image,
                zu_customer_name=sala_crm.zu_customer_name,
                s_demand_feature=sala_crm.s_demand_feature,
                a_demand_feature=sala_crm.a_demand_feature,
                b_demand_feature=sala_crm.b_demand_feature,
                c_demand_feature=sala_crm.c_demand_feature,
                s_zu_demand_feature=sala_crm.s_zu_demand_feature,
                a_zu_demand_feature=sala_crm.a_zu_demand_feature,
                b_zu_demand_feature=sala_crm.b_zu_demand_feature,
                c_zu_demand_feature=sala_crm.c_zu_demand_feature
            )

            db.add(openseas_record)
            success_count += 1

        return success_count

    except Exception as e:
        logger.error(f"添加到公海表失败: {str(e)}")
        return success_count


# 更新转换后的状态
def update_status_after_transfer(lead_ids: List[str], rule_type: int, db: Session):
    """
    更新转换后的状态
    """
    try:
        for lead_id in lead_ids:
            if rule_type == 1:  # 特定条件：更新SalaCrmToOpenseas.is_trans=1
                db.query(SalaCrmToOpenseas).filter(
                    SalaCrmToOpenseas.lead_id == lead_id,
                    SalaCrmToOpenseas.is_trans == 0
                ).update({"is_trans": 1})

            # 所有类型都要更新SalaCrm.os_status=1
            db.query(SalaCrm).filter(SalaCrm.ID == lead_id).update({"os_status": 1})

    except Exception as e:
        logger.error(f"更新状态失败: {str(e)}")
       
       

    except HTTPException:
        raise
    except Exception as e:
        return HTTPException(status_code=400, detail=f"删除规则失败: {str(e)}")