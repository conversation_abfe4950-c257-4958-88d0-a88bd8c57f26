<!-- Loading 组件 -->
<div id="loading-overlay">
    <div class="loading-spinner"></div>
    <div class="loading-text">正在加载系统</div>
    <div class="loading-progress">
        <span id="loading-status">正在加载静态资源</span>
        <span class="loading-dots"></span>
    </div>
</div>

<style>
    /* Loading 动画样式 */
    #loading-overlay {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: #f5f7fa;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        z-index: 9999;
        transition: opacity 0.5s ease-out;
    }

    #loading-overlay.fade-out {
        opacity: 0;
        pointer-events: none;
    }

    .loading-spinner {
        width: 60px;
        height: 60px;
        border: 4px solid rgba(255, 255, 255, 0.3);
        border-top: 4px solid #409eff;
        border-radius: 50%;
        animation: spin 1s linear infinite;
        margin-bottom: 20px;
    }

    .loading-text {
        color: #606266;
        font-size: 18px;
        font-weight: 500;
        margin-bottom: 10px;
        text-align: center;
    }

    .loading-progress {
        color: #909399;
        font-size: 14px;
        text-align: center;
    }

    .loading-dots {
        display: inline-block;
        width: 20px;
    }

    @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
    }

    @keyframes dots {
        0%, 20% { content: ''; }
        40% { content: '.'; }
        60% { content: '..'; }
        80%, 100% { content: '...'; }
    }

    .loading-dots::after {
        content: '';
        animation: dots 1.5s infinite;
    }
</style>

<script>
    // 资源加载检测和Loading控制类
    class LoadingManager {
        constructor(pageTitle = '系统') {
            this.loadingOverlay = document.getElementById('loading-overlay');
            this.loadingStatus = document.getElementById('loading-status');
            this.loadingText = document.querySelector('.loading-text');
            this.pageTitle = pageTitle;
            this.startTime = performance.now();

            // 设置页面标题
            if (this.loadingText) {
                this.loadingText.textContent = `正在加载${this.pageTitle}`;
            }

            // 设置简单的加载状态
            if (this.loadingStatus) {
                this.loadingStatus.textContent = '正在加载资源';
            }
        }

        // 等待单个资源加载
        waitForResource(checkFunction, resourceName, timeout = 5000) {
            return new Promise((resolve, reject) => {
                const startTime = Date.now();
                
                const check = () => {
                    if (checkFunction()) {
                        console.log(`✅ ${resourceName} 已加载`);
                        resolve(resourceName);
                    } else if (Date.now() - startTime > timeout) {
                        console.error(`❌ ${resourceName} 加载超时`);
                        reject(new Error(`${resourceName} 加载超时`));
                    } else {
                        setTimeout(check, 50);
                    }
                };
                
                check();
            });
        }

        // 开始加载检测
        async startLoading(customResources = []) {
            try {
                console.log(`🚀 开始检测${this.pageTitle}资源加载状态...`);

                // 默认资源检测
                const defaultChecks = [
                    { check: () => typeof Vue !== 'undefined', name: 'Vue.js' },
                    { check: () => typeof ElementPlus !== 'undefined', name: 'Element Plus' }
                ];

                // 合并自定义资源检测
                const allChecks = [...defaultChecks, ...customResources];

                // 逐个检测资源
                for (const resource of allChecks) {
                    await this.waitForResource(resource.check, resource.name);
                }

                // 额外等待确保所有资源完全就绪
                await new Promise(resolve => setTimeout(resolve, 300));

                const loadingTime = performance.now() - this.startTime;
                console.log(`✅ ${this.pageTitle}所有资源加载完成，耗时: ${loadingTime.toFixed(2)}ms`);

                // 隐藏loading并初始化应用
                this.hideLoading();
                this.onLoadingComplete();

            } catch (error) {
                console.error(`❌ ${this.pageTitle}资源加载失败:`, error);
                this.showError(error.message);
            }
        }

        // 隐藏loading动画
        hideLoading() {
            if (this.loadingOverlay) {
                this.loadingOverlay.classList.add('fade-out');
                setTimeout(() => {
                    this.loadingOverlay.style.display = 'none';
                }, 500);
            }
        }

        // 显示错误信息
        showError(message) {
            if (this.loadingStatus) {
                this.loadingStatus.textContent = `加载失败: ${message}`;
            }
            if (this.loadingOverlay) {
                this.loadingOverlay.style.background = 'linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%)';
            }
            
            // 5秒后尝试重新加载
            setTimeout(() => {
                location.reload();
            }, 5000);
        }

        // 加载完成回调（可被重写）
        onLoadingComplete() {
            console.log(`🎉 ${this.pageTitle}初始化完成`);
        }
    }

    // 将LoadingManager暴露到全局，供各页面使用
    window.LoadingManager = LoadingManager;
</script>
