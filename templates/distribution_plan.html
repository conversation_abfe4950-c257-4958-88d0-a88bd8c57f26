<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>分发计划 - Lead Distribution</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/element-plus/dist/index.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="stylesheet" href="https://unpkg.com/@element-plus/icons-vue">
    <style>
        :root {
            --primary-color: #409eff;
            --primary-lighter: #a0cfff;
            --primary-darker: #337ecc;
            --success-color: #67c23a;
            --warning-color: #e6a23c;
            --danger-color: #f56c6c;
            --info-color: #909399;
            --primary-text: #303133;
            --regular-text: #606266;
            --secondary-text: #909399;
            --placeholder-text: #c0c4cc;
            --border-color: #dcdfe6;
            --border-hover: #c0c4cc;
            --background: #f5f7fa;
            --card-bg: #ffffff;
            --transition-time: 0.3s;
        }
        
        body {
            margin: 0;
            padding: 0;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
            background: var(--background);
            color: var(--primary-text);
        }
        
        /* 自定义算法说明的tooltip样式 */
        .algorithm-tooltip {
            max-width: 350px !important;
            line-height: 1.5;
        }
        
        .algorithm-tooltip .algorithm-title {
            font-size: 16px !important;
            font-weight: 600;
            margin-bottom: 8px;
            color: var(--primary-darker);
        }
        
        .algorithm-tooltip .algorithm-desc {
            font-size: 14px !important;
            white-space: normal !important;
            margin-bottom: 6px;
        }
        
        /* 强制Element Plus修改tooltip样式 */
        .el-tooltip__popper {
            max-width: 350px !important;
        }
        
        .el-tooltip__popper .el-tooltip__content {
            font-size: 14px !important;
            line-height: 1.5 !important;
            white-space: normal !important;
        }
        
        .container {
            width: 100%;
            max-width: 100%; /* 让页面充满整个屏幕 */
            margin: 0;
            padding: 24px;
            box-sizing: border-box;
        }
        
        /* 添加垂直分栏布局 */
        .content {
            display: flex;
            gap: 24px;
            margin-top: 24px;
        }
        
        .left-column, .right-column {
            /* width 在 HTML 中通过内联样式设置 */
        }
        
        /* 响应式布局调整 */
        @media screen and (max-width: 1200px) {
            .content {
                flex-direction: column; /* 小屏幕上改为上下布局 */
            }
            
            .left-column, .right-column {
                width: 100% !important; /* 覆盖内联样式 */
            }
        }
        
        .page-header {
            margin-bottom: 40px;
            padding-bottom: 20px;
            border-bottom: 1px solid var(--border-color);
            display: flex; /* 使用flex布局 */
            justify-content: space-between; /* 一个靠左一个靠右 */
            align-items: center;
        }
        
        .page-title {
            font-size: 28px;
            color: var(--primary-text);
            margin: 0;
            padding: 0;
            font-weight: 600;
        }
        
        .home-link {
            color: #ffffff;
            text-decoration: none;
            background-color: #409eff;
            border-radius: 4px;
            padding: 8px 16px;
            font-size: 14px;
            display: flex;
            align-items: center;
            transition: background-color 0.3s, transform 0.2s;
        }
        
        .home-link:hover {
            background-color: #66b1ff;
            transform: translateY(-2px);
        }
        
        .home-link i {
            margin-right: 8px;
        }
        
        .section {
            margin-bottom: 40px;
            background: var(--card-bg);
            border-radius: 8px;
            padding: 24px;
            box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
            transition: transform var(--transition-time), box-shadow var(--transition-time);
        }
        
        .section:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(0, 0, 0, 0.1);
        }
        
        .section-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 24px;
            padding-bottom: 16px;
            border-bottom: 1px solid var(--border-color);
        }
        
        .section-title {
            font-size: 20px;
            font-weight: 600;
            color: var(--primary-text);
            margin: 0;
        }
        
        .section-subtitle {
            font-size: 16px;
            font-weight: 600;
            color: var(--primary-text);
            margin-top: 0;
            margin-bottom: 16px;
            padding-bottom: 8px;
            border-bottom: 1px dashed var(--border-color);
        }
        
        .add-button {
            transition: transform 0.2s, box-shadow 0.2s;
        }
        
        .add-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(64, 158, 255, 0.2);
        }
        
        .chat-cards {
            display: grid;
            grid-template-columns: repeat(4, 1fr); /* 每行显示4个卡片 */
            gap: 16px;
            margin-top: 24px;
        }
        
        .limited-cards {
            max-height: calc(2 * (70px + 12px + 16px * 2 + 2px + 40px)); /* 调整为显示完整两行卡片的高度 */
            overflow: hidden;
        }
        
        .view-all-button {
            margin-top: 16px;
            text-align: center;
        }
        
        .chat-card {
            position: relative;
            border-radius: 8px;
            background: var(--card-bg);
            padding: 16px;
            border: 1px solid var(--border-color);
            box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
            transition: all var(--transition-time);
            overflow: hidden;
        }
        
        .chat-card:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 20px rgba(0, 0, 0, 0.08);
            border-color: var(--primary-color);
        }
        
        .chat-card .card-content {
            min-height: 70px; /* 缩小卡片内容高度 */
        }
        
        .chat-name {
            font-size: 16px;
            font-weight: 600;
            color: var(--primary-text);
            margin-bottom: 6px;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }
        
        .chat-id {
            font-size: 12px;
            color: var(--secondary-text);
            margin-top: 6px;
        }
        
        .chat-remark {
            color: var(--primary-color);
            font-size: 14px;
            font-weight: 500;
            margin-top: 6px;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }
        
        .chat-group {
            margin-top: 4px;
        }
        
        .group-badge {
            display: inline-block;
            font-size: 12px;
            color: #606266;
            background-color: #f0f0f0;
            border-radius: 3px;
            padding: 2px 6px;
        }
        
        .chat-actions {
            margin-top: 12px;
            display: flex;
            justify-content: space-between;
        }
        
        .send-message-section {
            margin-top: 40px;
        }
        
        .el-form-item-tip {
            font-size: 12px;
            color: var(--secondary-text);
            margin-top: 5px;
        }
        
        .card-badge {
            position: absolute;
            top: 0;
            right: 0;
            background: var(--primary-color);
            color: white;
            font-size: 11px;
            padding: 3px 6px;
            border-bottom-left-radius: 6px;
        }
        
        /* 动画效果 */
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }
        
        .fade-in {
            animation: fadeIn 0.5s ease-out forwards;
        }
        
        /* 表单弹窗样式 */
        .dialog-form .el-form-item {
            margin-bottom: 22px;
        }
        
        .dialog-footer {
            display: flex;
            justify-content: flex-end;
            margin-top: 24px;
        }
        
        /* 自定义滚动条 */
        ::-webkit-scrollbar {
            width: 6px;
            height: 6px;
        }
        
        ::-webkit-scrollbar-thumb {
            background: #c0c4cc;
            border-radius: 3px;
        }
        
        ::-webkit-scrollbar-thumb:hover {
            background: #909399;
        }
        
        ::-webkit-scrollbar-track {
            background: #f5f7fa;
            border-radius: 3px;
        }
        
        /* 响应式布局调整 */
        @media screen and (max-width: 768px) {
            .chat-cards {
                grid-template-columns: repeat(auto-fill, minmax(160px, 1fr));
            }
            .container {
                padding: 16px;
            }
        }
        
        /* 分发队列样式 */
        .queue-control-area {
            margin-bottom: 20px;
        }
        
        .queue-form {
            margin-bottom: 20px;
        }
        
        .algorithm-description {
            margin: 15px 0;
        }
        
        .queue-display-area {
            margin-top: 30px;
        }
        
        /* 队列总长度样式 */
        .queue-total-count {
            display: inline-block;
            margin-left: 15px;
            padding: 5px 10px;
            background-color: var(--primary-color);
            color: white;
            border-radius: 4px;
            font-size: 14px;
            font-weight: 600;
        }
        
        /* 队列数量间隔样式 */
        .queue-count-spacer {
            display: inline-block;
            width: 1px;
            height: 14px;
            background-color: white;
            margin: 0 10px;
            vertical-align: middle;
            opacity: 0.8;
        }
        
        /* 双表格布局 */
        .queue-tables {
            display: flex;
            flex-direction: column;
            gap: 20px;
            margin-top: 20px;
        }
        
        @media screen and (min-width: 1400px) {
            .queue-tables {
                display: grid;
                grid-template-columns: 1fr 1fr; /* 在大屏幕上使用两栏布局 */
                gap: 20px;
            }
        }
        
        .queue-table-wrapper {
            display: flex;
            flex-direction: column;
            border: 1px solid var(--border-color);
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
        }
        
        .queue-table-title {
            text-align: center;
            margin-bottom: 15px;
            color: var(--primary-color);
            font-weight: 600;
            font-size: 16px;
            padding: 10px 0;
            background-color: #f9f9f9;
            border-bottom: 1px solid #ebeef5;
        }
        
        .queue-table-container {
            border: 1px solid #ebeef5;
            border-radius: 4px;
            box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
            overflow: hidden;
        }
        
        /* 表格行悬停效果 */
        .el-table--enable-row-hover .el-table__body tr:hover > td {
            background-color: #f0f7ff !important;
        }
        
        /* 表头样式 */
        .el-table th {
            background-color: #f5f7fa !important;
            color: #606266 !important;
            font-weight: 600 !important;
        }
        
        /* 表格内容区域边框 */
        .el-table--border .el-table__cell {
            border-right: 1px solid #ebeef5;
        }
        
        /* 条纹效果增强 */
        .el-table--striped .el-table__body tr.el-table__row--striped td {
            background-color: #fafafa;
        }
        
        /* 数据分发样式 */
        .distribute-form {
            margin-top: 20px;
        }
        
        .formatted-data-preview {
            background-color: var(--background);
            border-radius: 6px;
            overflow: hidden;
            border: 1px solid var(--border-color);
        }
        
        .formatted-data-header {
            background-color: var(--primary-lighter);
            padding: 10px;
            margin: 0;
            color: var(--primary-text);
            font-size: 14px;
            display: flex;
            justify-content: space-between;
        }
        
        .formatted-data-time {
            color: var(--regular-text);
            font-size: 12px;
        }
        
        .formatted-data-content {
            padding: 15px;
            background-color: white;
            max-height: 200px;
            overflow-y: auto;
        }
        
        .formatted-data-content pre {
            margin: 0;
            white-space: pre-wrap;
            word-break: break-word;
            font-family: monospace;
            font-size: 14px;
            color: var(--primary-text);
        }
        
        /* 算法说明样式 */
        .algorithm-explanation {
            background-color: #f9f9f9;
            border: 1px solid #e0e0e0;
            border-radius: 4px;
            padding: 15px;
            margin-bottom: 20px;
        }
        
        .algorithm-explanation h4 {
            color: #409EFF;
            margin-top: 0;
            margin-bottom: 10px;
        }
        
        .algorithm-explanation ul {
            padding-left: 20px;
        }
        
        .algorithm-explanation li {
            margin-bottom: 5px;
        }
        
        /* 算法说明区域 */
        .algorithm-explanation {
            margin-bottom: 20px;
        }
        
        .algorithm-explanation p {
            margin-bottom: 10px;
        }
        
        .algorithm-explanation .algorithm-flow {
            margin-bottom: 10px;
        }
        
        .algorithm-explanation .algorithm-flow-title {
            font-size: 18px;
            font-weight: 600;
            margin-bottom: 8px;
        }
        
        .algorithm-explanation .algorithm-flow-diagram {
            display: flex;
            align-items: center;
            flex-wrap: wrap;
            margin-bottom: 10px;
        }
        
        .algorithm-explanation .flow-step {
            display: flex;
            align-items: center;
            margin-right: 20px;
            margin-bottom: 10px;
        }
        
        .algorithm-explanation .flow-step-number {
            background-color: var(--primary-color);
            color: white;
            border-radius: 50%;
            width: 24px;
            height: 24px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 8px;
        }
        
        .algorithm-explanation .flow-step-content {
            flex: 1;
        }
        
        .algorithm-explanation .flow-step-title {
            font-size: 16px;
            font-weight: 600;
            margin-bottom: 4px;
        }
        
        .algorithm-explanation .flow-step-desc {
            color: var(--secondary-text);
        }
        
        .algorithm-explanation .flow-arrow {
            margin: 0 10px;
            font-size: 18px;
            color: var(--primary-color);
            font-weight: bold;
        }
        
        .algorithm-explanation .algorithm-note {
            background-color: #f0f0f0;
            padding: 8px;
            border-radius: 4px;
            margin-top: 10px;
        }
        
        .algorithm-explanation .el-icon-info-filled {
            margin-right: 8px;
        }
        
        /* 在小屏幕上调整布局 */
        @media (max-width: 768px) {
            .algorithm-explanation .algorithm-flow-diagram {
                flex-direction: column;
                align-items: flex-start;
            }
            
            .algorithm-explanation .flow-step {
                margin-bottom: 15px;
            }
            
            .algorithm-explanation .flow-arrow {
                transform: rotate(90deg);
                margin: 5px 0;
            }
        }
        
        /* 队列统计卡片样式 */
        .queue-stats-card {
            background-color: #fff;
            border-radius: 8px;
            padding: 10px;
            margin-bottom: 15px;
            box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
        }
        
        .queue-stats-title {
            font-size: 15px;
            font-weight: 600;
            margin-bottom: 6px;
            color: var(--primary-text);
        }
        
        .queue-stats-row {
            display: flex;
            justify-content: space-between;
            margin-bottom: 5px;
        }
        
        .queue-stats-item {
            flex: 1;
            text-align: center;
            padding: 2px 0;
        }
        
        .queue-stats-label {
            font-size: 12px;
            color: var(--secondary-text);
            margin-bottom: 3px;
        }
        
        .queue-stats-value {
            font-size: 15px;
            font-weight: 600;
        }
        
        .queue-stats-progress-container {
            height: 4px;
            background-color: #f0f0f0;
            border-radius: 2px;
            overflow: hidden;
            margin-top: 3px;
        }
        
        .queue-stats-progress-bar {
            height: 100%;
            border-radius: 3px;
        }
        
        .free-progress {
            background-color: #67c23a;
        }
        
        .paid-progress {
            background-color: #409eff;
        }
        
        /* 队列项操作按钮样式 */
        .queue-item-actions {
            display: flex;
            gap: 5px;
        }
        
        .queue-item-status {
            padding: 2px 6px;
            border-radius: 4px;
            font-size: 12px;
        }
        
        .status-pending {
            background-color: #e6a23c;
            color: #fff;
        }
        
        .status-distributed {
            background-color: #67c23a;
            color: #fff;
        }
        
        .status-cancelled {
            background-color: #f56c6c;
            color: #fff;
        }
        
        /* 删除队列按钮延迟确认样式 */
        .delayed-confirm-button {
            opacity: 0.6;
            cursor: not-allowed;
            background-color: #F56C6C !important;
            border-color: #F56C6C !important;
            color: #FFFFFF !important;
        }
        
        .delayed-confirm-button.can-confirm {
            opacity: 1;
            cursor: pointer;
            background-color: #F56C6C !important;
            border-color: #F56C6C !important;
            color: #FFFFFF !important;
            font-weight: bold;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
        }
        
        /* 队列表格容器样式 */
        .queue-tables-container {
            display: flex;
            flex-direction: column;
            gap: 20px;
            margin-top: 20px;
        }
        
        .queue-table-wrapper {
            width: 100%;
            margin-bottom: 20px;
        }
        
        /* 将两栏布局改为一栏垂直布局 */
        @media screen and (min-width: 1400px) {
            .queue-tables-container {
                display: flex;
                flex-direction: column;
                gap: 20px;
            }
        }
        
        /* ElementUI弹窗样式修改 */
        .el-message-box__btns .delayed-confirm-button.can-confirm:hover {
            opacity: 0.9;
            background-color: #DD6161 !important;
        }
        
        /* 监测功能样式 */
        .monitoring-section {
            margin-bottom: 24px;
        }
        
        .monitoring-controls {
            display: flex;
            align-items: center;
        }
        
        .monitoring-status-label {
            margin-right: 10px;
            font-size: 15px;
            color: var(--primary-text);
        }
        
        .monitoring-content {
            display: flex;
            flex-direction: column;
            gap: 16px;
        }
        
        .stats-cards {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 16px;
            margin-top: 8px;
        }
        
        .stats-card {
            background: var(--card-bg);
            border-radius: 8px;
            padding: 16px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
            display: flex;
            position: relative;
            transition: transform 0.3s, box-shadow 0.3s;
        }
        
        .stats-card:hover {
            transform: translateY(-3px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.12);
        }
        
        .stats-icon {
            width: 48px;
            height: 48px;
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 16px;
            font-size: 20px;
            color: #ffffff;
        }
        
        .stats-card:nth-child(1) .stats-icon {
            background: linear-gradient(135deg, #3498db, #2980b9);
        }
        
        .stats-card:nth-child(2) .stats-icon {
            background: linear-gradient(135deg, #2ecc71, #27ae60);
        }
        
        .stats-card:nth-child(3) .stats-icon {
            background: linear-gradient(135deg, #f39c12, #e67e22);
        }
        
        .stats-card:nth-child(4) .stats-icon {
            background: linear-gradient(135deg, #9b59b6, #8e44ad);
        }
        
        .stats-content {
            flex: 1;
        }
        
        .stats-value {
            font-size: 24px;
            font-weight: 600;
            color: var(--primary-text);
            margin-bottom: 4px;
        }
        
        .stats-label {
            font-size: 14px;
            color: var(--secondary-text);
        }
        
        .stats-total {
            position: static;
            margin-top: 48px;
            margin-left: 64px;
            font-size: 12px;
            color: var(--secondary-text);
        }
        
        .stats-trend {
            position: absolute;
            bottom: 10px;
            right: 10px;
            font-size: 12px;
            padding: 2px 6px;
            border-radius: 10px;
            display: flex;
            align-items: center;
            gap: 4px;
        }
        
        .trend-up {
            color: #f56c6c;
            background-color: rgba(245, 108, 108, 0.1);
        }
        
        .trend-down {
            color: #67c23a;
            background-color: rgba(103, 194, 58, 0.1);
        }
        
        .trend-flat {
            color: #909399;
            background-color: rgba(144, 147, 153, 0.1);
        }
        
        .stats-updated {
            position: absolute;
            bottom: 10px;
            right: 10px;
            font-size: 12px;
            color: var(--secondary-text);
            display: flex;
            align-items: center;
            gap: 4px;
        }
        
        .date-filter {
            display: flex;
            align-items: center;
            justify-content: flex-end;
            margin-top: 8px;
        }
        
        /* 表格行状态样式 */
        .el-table .distributed-row {
            background-color: #f0f9eb !important;
        }
        .el-table .distributed-row:hover > td {
            background-color: #e1f3d8 !important;
        }
        
        .el-table .pending-row {
            cursor: pointer;
        }
        
        .el-table .cancelled-row {
            background-color: #fef0f0 !important;
            color: #999;
        }
        .el-table .cancelled-row:hover > td {
            background-color: #fde2e2 !important;
        }
        
        /* 表格状态行的样式 */
        .distributed-row td {
            background-color: rgba(103, 194, 58, 0.1) !important; /* 浅绿色 */
        }
        
        .pending-row td {
            background-color: rgba(144, 147, 153, 0.1) !important; /* 浅灰色 */
        }
        
        .cancelled-row td {
            background-color: rgba(245, 108, 108, 0.1) !important; /* 浅红色 */
        }
        
        /* 队列统计样式 */
        .queue-stats-container {
            margin-bottom: 20px;
            padding: 16px;
            background-color: white;
            border-radius: 8px;
            box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
        }
        
        .queue-stats-title {
            font-size: 16px;
            font-weight: 600;
            margin-bottom: 12px;
            color: var(--primary-text);
        }
        
        .queue-stats-row {
            display: flex;
            gap: 16px;
            margin-bottom: 16px;
        }
        
        .queue-stats-item {
            flex: 1;
            background-color: #f9f9f9;
            padding: 12px;
            border-radius: 8px;
            border: 1px solid #eee;
        }
        
        .queue-stats-label {
            font-size: 14px;
            color: var(--secondary-text);
            margin-bottom: 8px;
        }
        
        .queue-stats-value {
            font-size: 18px;
            font-weight: 600;
            color: var(--primary-text);
            margin-bottom: 8px;
        }
        
        .queue-stats-progress-container {
            height: 6px;
            background-color: #e0e0e0;
            border-radius: 3px;
            overflow: hidden;
        }
        
        .queue-stats-progress-bar {
            height: 100%;
            background-color: var(--primary-color);
            border-radius: 3px;
            transition: width 0.3s ease;
        }
        
        .queue-stats-progress-bar.free-progress {
            background-color: #67c23a; /* 绿色 */
        }
        
        .queue-stats-progress-bar.paid-progress {
            background-color: #e6a23c; /* 橙色 */
        }
        
        /* 队列表格容器样式 */
        .queue-tables-container {
            display: flex;
            flex-wrap: wrap;
            gap: 20px;
        }
        
        .queue-table-wrapper {
            flex: 1;
            min-width: 250px;
        }
    </style>
</head>
<body>
    <div id="app" class="container">
        <div class="page-header">
            <h1 class="page-title">分发计划</h1>
            <a href="/home" class="home-link">
                <i class="fas fa-home"></i>
                返回首页
            </a>
        </div>
        
        <div class="content">
            
            <!-- 右侧栏扩展到100%宽度 -->
            <div class="right-column" style="width: 100%;">
                <div class="section fade-in">
                    <div class="section-header" style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 10px; padding-bottom: 10px;">
                        <div style="display: flex; align-items: center; gap: 20px;">
                            <h2 class="section-title" style="margin: 0; flex-shrink: 0;">分发队列</h2>
                            
                            <!-- 自动生成队列开关和时间选择器移到这里 -->
                            <div style="display: flex; align-items: center;">
                                <el-switch
                                    v-model="autoGenerateEnabled"
                                    active-text="自动生成队列"
                                    inactive-text=""
                                    @change="handleAutoGenerateChange"
                                    style="margin-right: 10px;">
                                </el-switch>
                                <el-time-picker
                                    v-model="autoGenerateTime"
                                    placeholder="自动生成时间"
                                    format="HH:mm"
                                    value-format="HH:mm"
                                    :disabled="!autoGenerateEnabled"
                                    style="width: 130px;"
                                    :default-value="new Date(2000, 0, 1, 9, 0, 0)"
                                    popper-class="time-picker-popper"
                                    @change="handleTimeChange" 
                                    :clearable="false">
                                </el-time-picker>
                            </div>
                        </div>
                        
                        <!-- 日期选择器保持在中间 -->
                        <div class="date-picker-container" style="display: flex; align-items: center; justify-content: center;">
                            <el-button 
                                type="text" 
                                @click="changeDate(-1)" 
                                style="margin-right: 5px;">
                                <i class="fas fa-chevron-left"></i>
                            </el-button>
                            <el-date-picker
                                v-model="queueForm.date"
                                type="date"
                                placeholder="选择日期"
                                format="YYYY-MM-DD"
                                value-format="YYYY-MM-DD"
                                style="width: 240px;"
                            ></el-date-picker>
                            <el-button 
                                type="text" 
                                @click="changeDate(1)" 
                                style="margin-left: 5px;">
                                <i class="fas fa-chevron-right"></i>
                            </el-button>
                        </div>
                        
                        <!-- 所有操作按钮放在右侧 -->
                        <div style="flex-shrink: 0; display: flex; gap: 10px;">
                            <el-button 
                                type="warning" 
                                @click="updateQueue"
                                :loading="updateLoading">
                                <i class="fas fa-redo"></i> 更新分发队列
                            </el-button>
                            <el-button 
                                type="primary" 
                                @click="refreshQueueStats(true)"
                                :loading="statsLoading">
                                <i class="fas fa-sync"></i> 刷新统计
                            </el-button>
                            <el-button 
                                type="danger" 
                                @click="deleteQueue" 
                                :loading="deleteLoading">
                                <i class="fas fa-trash"></i> 删除队列
                            </el-button>
                            <el-button 
                                type="primary" 
                                @click="generateQueue" 
                                :loading="queueLoading"
                                :disabled="!queueForm.date || hasExistingQueue"
                                style="flex-shrink: 0;">
                                生成分发队列
                            </el-button>
                        </div>
                    </div>
                    <div>
                        <!-- 控制区域完全移除上边距 -->
                        <div class="queue-control-area" style="margin: 0; padding: 0; height: 0;">
                            <!-- 隐藏表单，保留功能但不占用空间 -->
                            <el-form :inline="true" class="queue-form" style="display: none;">
                                <el-form-item>
                                    <!-- 移除了原来的按钮和开关 -->
                                </el-form-item>
                            </el-form>
                        </div>
                        
                        <!-- 队列显示区域 -->
                        <div class="queue-display-area" v-if="true" style="margin-top: 0;">
                            <!-- 队列统计卡片 -->
                            <div class="queue-stats-card" v-if="queueStats" style="margin: 0 0 10px 0; padding-top: 5px;">
                                <div class="queue-stats-title">队列统计信息</div>
                                <!-- 显示所有统计信息 -->
                                <div class="queue-stats-row">
                                    <!-- 电商渠道统计 -->
                                    <div class="queue-stats-item">
                                        <div class="queue-stats-label">电商渠道队列</div>
                                        <div class="queue-stats-value">${ queueStats.ecommerce_distributed || 0 }$ / ${ queueStats.ecommerce_total || 0 }$</div>
                                        <div class="queue-stats-progress-container">
                                            <div class="queue-stats-progress-bar"
                                                :style="{width: (queueStats.ecommerce_total > 0 ? ((queueStats.ecommerce_distributed || 0) / queueStats.ecommerce_total * 100) : 0) + '%',
                                                        backgroundColor: '#409EFF'}">
                                            </div>
                                        </div>
                                    </div>

                                    <!-- 公海分发队列统计 -->
                                    <div class="queue-stats-item">
                                        <div class="queue-stats-label">公海分发队列</div>
                                        <div class="queue-stats-value">${ queueStats.public_sea_distributed || 0 }$ / ${ queueStats.public_sea_total || 0 }$</div>
                                        <div class="queue-stats-progress-container">
                                            <div class="queue-stats-progress-bar"
                                                :style="{width: (queueStats.public_sea_total > 0 ? ((queueStats.public_sea_distributed || 0) / queueStats.public_sea_total * 100) : 0) + '%',
                                                        backgroundColor: '#E6A23C'}">
                                            </div>
                                        </div>
                                    </div>

                                    <!-- 新媒体渠道统计 -->
                                    <div class="queue-stats-item">
                                        <div class="queue-stats-label">新媒体渠道总量</div>
                                        <div class="queue-stats-value">${ (queueStats.free_distributed || 0) + (queueStats.paid_distributed || 0) }$ / ${ (queueStats.free_total || 0) + (queueStats.paid_total || 0) }$</div>
                                        <div class="queue-stats-progress-container">
                                            <div class="queue-stats-progress-bar"
                                                :style="{width: ((queueStats.free_total + queueStats.paid_total) > 0 ? ((queueStats.free_distributed + queueStats.paid_distributed) / (queueStats.free_total + queueStats.paid_total) * 100) : 0) + '%',
                                                        backgroundColor: '#67c23a'}">
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="queue-stats-row">
                                    <!-- 免费队列统计 -->
                                    <div class="queue-stats-item">
                                        <div class="queue-stats-label">新媒体免费队列</div>
                                        <div class="queue-stats-value">${ queueStats.free_distributed || 0 }$ / ${ queueStats.free_total || 0 }$</div>
                                        <div class="queue-stats-progress-container">
                                            <div class="queue-stats-progress-bar free-progress" 
                                                :style="{width: (queueStats.free_total > 0 ? ((queueStats.free_distributed || 0) / queueStats.free_total * 100) : 0) + '%'}">
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <!-- 付费队列统计 -->
                                    <div class="queue-stats-item">
                                        <div class="queue-stats-label">新媒体付费队列</div>
                                        <div class="queue-stats-value">${ queueStats.paid_distributed || 0 }$ / ${ queueStats.paid_total || 0 }$</div>
                                        <div class="queue-stats-progress-container">
                                            <div class="queue-stats-progress-bar paid-progress" 
                                                :style="{width: (queueStats.paid_total > 0 ? ((queueStats.paid_distributed || 0) / queueStats.paid_total * 100) : 0) + '%'}">
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                
                                <!-- 成员分发统计 -->
                                <div v-if="queueStats.member_statistics && queueStats.member_statistics.length > 0" style="margin-top: 8px;">
                                    <div class="queue-stats-title" style="margin-top: 5px; margin-bottom: 4px;">成员分发统计</div>
                                    <div style="display: flex; flex-wrap: wrap; gap: 5px;">
                                        <div v-for="(memberStat, index) in queueStats.member_statistics" :key="index" 
                                            style="background-color: #f5f7fa; padding: 3px 8px; border-radius: 4px; font-size: 12px;">
                                            <span style="font-weight: 600;">${ memberStat.member }$:</span> 
                                            <span>${ memberStat.total_distributed }$条</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- 队列表格区域 -->
                            <div class="queue-tables-container" style="display: flex; flex-direction: row; gap: 15px; justify-content: space-between; margin-top: 10px;">
                                <!-- 电商渠道队列表格 -->
                                <div class="queue-table-wrapper" style="flex: 1; min-width: 250px; max-width: 25%;">
                                    <h4 class="queue-table-title">电商渠道分发队列</h4>
                                    <div class="queue-table-container">
                                        <el-table
                                            :data="ecommerceQueueData?.free_queue || []"
                                            stripe
                                            style="width: 100%"
                                            max-height="600px"
                                            border
                                            :cell-style="{textAlign: 'center'}"
                                            :header-cell-style="{textAlign: 'center', backgroundColor: '#f5f7fa'}"
                                            @row-click="handleDistribute"
                                            :row-class-name="getRowClassName">
                                            <el-table-column
                                                prop="position"
                                                label="队列号"
                                                width="70">
                                            </el-table-column>
                                            <el-table-column
                                                prop="member"
                                                label="成员"
                                                min-width="90">
                                            </el-table-column>
                                            <el-table-column
                                                prop="time_slot"
                                                label="时间段"
                                                min-width="90">
                                            </el-table-column>
                                            <el-table-column
                                                prop="status"
                                                label="状态"
                                                width="90">
                                                <template v-slot="scope">
                                                    <span class="queue-item-status" 
                                                        :class="{
                                                            'status-pending': scope.row.status === '待分发' || scope.row.status === 'pending',
                                                            'status-distributed': scope.row.status === '已分发' || scope.row.status === 'distributed',
                                                            'status-cancelled': scope.row.status === '已取消' || scope.row.status === 'cancelled'
                                                        }">
                                                        ${ 
                                                            scope.row.status === 'pending' || scope.row.status === '待分发' ? '待分发' : 
                                                            scope.row.status === 'distributed' || scope.row.status === '已分发' ? '已分发' : 
                                                            scope.row.status === 'cancelled' || scope.row.status === '已取消' ? '已取消' : 
                                                            scope.row.status 
                                                        }$
                                                    </span>
                                                </template>
                                            </el-table-column>
                                        </el-table>
                                        <div v-if="!ecommerceQueueData?.free_queue?.length" class="no-data-tip" style="text-align: center; padding: 20px; color: #909399; font-size: 14px;">
                                            请先选择日期，然后生成分发队列
                                        </div>
                                    </div>
                                </div>

                                <!-- 公海分发队列表格 -->
                                <div class="queue-table-wrapper" style="flex: 1; min-width: 250px; max-width: 25%;">
                                    <h4 class="queue-table-title">公海分发队列</h4>
                                    <div class="queue-table-container">
                                        <el-table
                                            :data="publicSeaQueueData?.free_queue || []"
                                            stripe
                                            style="width: 100%"
                                            max-height="600px"
                                            border
                                            :cell-style="{textAlign: 'center'}"
                                            :header-cell-style="{textAlign: 'center', backgroundColor: '#f5f7fa'}"
                                            @row-click="handleDistribute"
                                            :row-class-name="getRowClassName">
                                            <el-table-column
                                                prop="position"
                                                label="队列号"
                                                width="70">
                                            </el-table-column>
                                            <el-table-column
                                                prop="member"
                                                label="成员"
                                                min-width="90">
                                            </el-table-column>
                                            <el-table-column
                                                prop="time_slot"
                                                label="时间段"
                                                min-width="90">
                                            </el-table-column>
                                            <el-table-column
                                                prop="status"
                                                label="状态"
                                                width="90">
                                                <template v-slot="scope">
                                                    <span class="queue-item-status"
                                                        :class="{
                                                            'status-pending': scope.row.status === '待分发' || scope.row.status === 'pending',
                                                            'status-distributed': scope.row.status === '已分发' || scope.row.status === 'distributed',
                                                            'status-cancelled': scope.row.status === '已取消' || scope.row.status === 'cancelled'
                                                        }">
                                                        ${
                                                            scope.row.status === 'pending' || scope.row.status === '待分发' ? '待分发' :
                                                            scope.row.status === 'distributed' || scope.row.status === '已分发' ? '已分发' :
                                                            scope.row.status === 'cancelled' || scope.row.status === '已取消' ? '已取消' :
                                                            scope.row.status
                                                        }$
                                                    </span>
                                                </template>
                                            </el-table-column>
                                        </el-table>
                                        <div v-if="!publicSeaQueueData?.free_queue?.length" class="no-data-tip" style="text-align: center; padding: 20px; color: #909399; font-size: 14px;">
                                            请先选择日期，然后生成分发队列
                                        </div>
                                    </div>
                                </div>

                                <!-- 新媒体渠道免费队列表格 -->
                                <div class="queue-table-wrapper" style="flex: 1; min-width: 250px; max-width: 25%;">
                                    <h4 class="queue-table-title">预计免费接待数分发队列</h4>
                                    <div class="queue-table-container">
                                        <el-table
                                            :data="newMediaQueueData?.free_queue || []"
                                            stripe
                                            style="width: 100%"
                                            max-height="600px"
                                            border
                                            :cell-style="{textAlign: 'center'}"
                                            :header-cell-style="{textAlign: 'center', backgroundColor: '#f5f7fa'}"
                                            @row-click="handleDistribute"
                                            :row-class-name="getRowClassName">
                                            <el-table-column
                                                prop="position"
                                                label="队列号"
                                                width="70">
                                            </el-table-column>
                                            <el-table-column
                                                prop="member"
                                                label="成员"
                                                min-width="90">
                                            </el-table-column>
                                            <el-table-column
                                                prop="time_slot"
                                                label="时间段"
                                                min-width="90">
                                            </el-table-column>
                                            <el-table-column
                                                prop="status"
                                                label="状态"
                                                width="90">
                                                <template v-slot="scope">
                                                    <span class="queue-item-status" 
                                                        :class="{
                                                            'status-pending': scope.row.status === '待分发' || scope.row.status === 'pending',
                                                            'status-distributed': scope.row.status === '已分发' || scope.row.status === 'distributed',
                                                            'status-cancelled': scope.row.status === '已取消' || scope.row.status === 'cancelled'
                                                        }">
                                                        ${ 
                                                            scope.row.status === 'pending' || scope.row.status === '待分发' ? '待分发' : 
                                                            scope.row.status === 'distributed' || scope.row.status === '已分发' ? '已分发' : 
                                                            scope.row.status === 'cancelled' || scope.row.status === '已取消' ? '已取消' : 
                                                            scope.row.status 
                                                        }$
                                                    </span>
                                                </template>
                                            </el-table-column>
                                        </el-table>
                                        <div v-if="!newMediaQueueData?.free_queue?.length" class="no-data-tip" style="text-align: center; padding: 20px; color: #909399; font-size: 14px;">
                                            请先选择日期，然后生成分发队列
                                        </div>
                                    </div>
                                </div>
                                
                                <!-- 新媒体渠道付费队列表格 -->
                                <div class="queue-table-wrapper" style="flex: 1; min-width: 250px; max-width: 25%;">
                                    <h4 class="queue-table-title">预计付费接待数分发队列</h4>
                                    <div class="queue-table-container">
                                        <el-table
                                            :data="newMediaQueueData?.paid_queue || []"
                                            stripe
                                            style="width: 100%"
                                            max-height="600px"
                                            border
                                            :cell-style="{textAlign: 'center'}"
                                            :header-cell-style="{textAlign: 'center', backgroundColor: '#f5f7fa'}"
                                            @row-click="handleDistribute"
                                            :row-class-name="getRowClassName">
                                            <el-table-column
                                                prop="position"
                                                label="队列号"
                                                width="70">
                                            </el-table-column>
                                            <el-table-column
                                                prop="member"
                                                label="成员"
                                                min-width="90">
                                            </el-table-column>
                                            <el-table-column
                                                prop="time_slot"
                                                label="时间段"
                                                min-width="90">
                                            </el-table-column>
                                            <el-table-column
                                                prop="status"
                                                label="状态"
                                                width="90">
                                                <template v-slot="scope">
                                                    <span class="queue-item-status" 
                                                        :class="{
                                                            'status-pending': scope.row.status === '待分发' || scope.row.status === 'pending',
                                                            'status-distributed': scope.row.status === '已分发' || scope.row.status === 'distributed',
                                                            'status-cancelled': scope.row.status === '已取消' || scope.row.status === 'cancelled'
                                                        }">
                                                        ${ 
                                                            scope.row.status === 'pending' || scope.row.status === '待分发' ? '待分发' : 
                                                            scope.row.status === 'distributed' || scope.row.status === '已分发' ? '已分发' : 
                                                            scope.row.status === 'cancelled' || scope.row.status === '已取消' ? '已取消' : 
                                                            scope.row.status 
                                                        }$
                                                    </span>
                                                </template>
                                            </el-table-column>
                                        </el-table>
                                        <div v-if="!newMediaQueueData?.paid_queue?.length" class="no-data-tip" style="text-align: center; padding: 20px; color: #909399; font-size: 14px;">
                                            请先选择日期，然后生成分发队列
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- 没有队列数据时显示的内容 -->
                        <div class="no-queue-data" v-if="!hasExistingQueue && !queueGenerated">
                            <!-- 空白占位，移除所有内容 -->
                        </div>
                        
                        <div v-else>
                            <!-- 空白占位，移除所有内容 -->
                        </div>
                    </div>
                </div>
            </div>
        </div>
 
        <!-- 数据分发对话框 -->
        <el-dialog
            title="数据分发"
            v-model="distributeDialogVisible"
            width="600px"
            :close-on-click-modal="false">
            <el-form :model="distributeForm" label-width="100px" class="distribute-form">
                <el-form-item label="分发对象">
                    <p><strong>分组：</strong>${ distributeForm.group_name }$</p>
                    <p><strong>负责人：</strong>${ distributeForm.leader }$</p>
                    <p><strong>成员：</strong>${ distributeForm.member }$</p>
                </el-form-item>
                <el-form-item label="格式化数据" v-if="formattedData">
                    <div class="formatted-data-preview">
                        <p class="formatted-data-header">
                            <strong>ID:</strong> ${ formattedData.id }$ 
                            <span class="formatted-data-time">${ formatDateTime(formattedData.created_at) }$</span>
                        </p>
                        <div class="formatted-data-content">
                            <pre>${ formattedData.formatted_text }$</pre>
                        </div>
                    </div>
                </el-form-item>
            </el-form>
            <template #footer>
                <div class="dialog-footer">
                    <el-button @click="distributeDialogVisible = false">关闭</el-button>
                </div>
            </template>
        </el-dialog>
    </div>
    <script src="/static/js/fetch.js"></script>
    <script src="https://unpkg.com/vue@3.2.47/dist/vue.global.js"></script>
    <script src="https://unpkg.com/element-plus"></script>
    <script>
        const { createApp, ref, onMounted, watch, onUnmounted, computed } = Vue;
        
        const app = createApp({
            // 设置自定义分隔符，避免与Jinja2冲突
            delimiters: ['${', '}$'],
            
            setup() {
                console.log('Vue组件初始化...');
                // 群聊相关变量初始化为空值（钉钉功能已禁用）
                const chatForm = ref({
                    chat_id: '',
                    chat_name: '',
                    group_id: '',
                    member_id: '',
                    webhook_url: '',
                    secret: '',
                    owner_name: ''
                });
                
                // 弹窗显示控制
                const dialogVisible = ref(false);
                const allChatsDialogVisible = ref(false);
                
                // 编辑模式标志
                const isEditMode = ref(false);
                
                // 群聊列表（初始化为空数组）
                const chatList = ref([]);
                
                // 分组和成员列表
                const groupList = ref([]);
                const memberList = ref([]);
                const membersLoading = ref(false);
                
                // 搜索功能
                const searchQuery = ref('');
                const filteredChatList = ref([]);
                
                // 添加分组过滤功能
                const selectedGroupFilter = ref('');
                
                // 从现有群聊中提取唯一的分组名称
                const uniqueGroupNames = computed(() => {
                    // 从群聊列表中获取所有不重复的分组名称
                    const groupNames = new Set();
                    chatList.value.forEach(chat => {
                        const groupName = getGroupName(chat.group_id);
                        if (groupName !== '未知分组') {
                            groupNames.add(groupName);
                        }
                    });
                    // 转换为数组并排序
                    return Array.from(groupNames).sort();
                });
                
                // 消息表单数据
                const messageForm = ref({
                    chat_id: '',
                    type: 'text',
                    title: '',
                    content: ''
                });
                
                // 日期格式化函数
                const formatDate = (dateString) => {
                    if (!dateString) return '';
                    const date = new Date(dateString);
                    const year = date.getFullYear();
                    const month = String(date.getMonth() + 1).padStart(2, '0');
                    const day = String(date.getDate()).padStart(2, '0');
                    return `${year}-${month}-${day}`;
                };
                
                // 分发队列相关数据
                const queueForm = ref({
                    date: formatDate(new Date()),
                    channelType: '新媒体渠道'
                });
                
                const queueData = ref(null);
                const ecommerceQueueData = ref({ free_queue: [], paid_queue: [] });
                const newMediaQueueData = ref({ free_queue: [], paid_queue: [] });
                const publicSeaQueueData = ref({ free_queue: [], paid_queue: [] }); // 新增公海分发队列数据
                const queueLoading = ref(false);
                const queueGenerated = ref(false);
                const deleteLoading = ref(false);
                const updateLoading = ref(false);
                const hasExistingQueue = ref(false); // 新增变量，标记当日是否已有队列
                
                // 数据分发相关
                const distributeDialogVisible = ref(false);
                const distributeLoading = ref(false);
                const formattedData = ref(null);
                const distributeForm = ref({
                    group_name: '',
                    leader: '',
                    member: '',
                    position: 0,
                    chat_id: '',
                    type: 'text',
                    title: '',
                    content: ''
                });
                
                // 日期时间格式化函数
                const formatDateTime = (dateTimeString) => {
                    if (!dateTimeString) return '';
                    const date = new Date(dateTimeString);
                    const year = date.getFullYear();
                    const month = String(date.getMonth() + 1).padStart(2, '0');
                    const day = String(date.getDate()).padStart(2, '0');
                    const hours = String(date.getHours()).padStart(2, '0');
                    const minutes = String(date.getMinutes()).padStart(2, '0');
                    const seconds = String(date.getSeconds()).padStart(2, '0');
                    return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
                };
                
                // 添加统计相关的状态变量
                const queueStats = ref(null);
                const statsLoading = ref(false);
                
                // 添加自动生成队列相关的变量
                const autoGenerateEnabled = ref(false);
                const autoGenerateTime = ref('09:00');
                
                // 处理自动生成队列开关变化
                const handleAutoGenerateChange = async (value) => {
                    console.log('自动生成队列开关状态变更为:', value);
                    
                    try {
                        // 调用后端API更新任务状态
                        const response = await fetch('/api/task/toggle-status', {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json',
                            },
                            body: JSON.stringify({
                                status: value ? 1 : 0
                            })
                        });
                        
                        const result = await response.json();
                        
                        if (result.success) {
                            autoGenerateEnabled.value = value;
                            // 保存开关状态到本地存储
                            localStorage.setItem('autoGenerateEnabled', value);
                            
                            ElementPlus.ElMessage.success(result.message);
                            
                            // 更新显示的时间（如果有返回）
                            if (result.data && result.data.formatted_time) {
                                autoGenerateTime.value = result.data.formatted_time;
                            }
                        } else {
                            throw new Error(result.message || '更新任务状态失败');
                        }
                    } catch (error) {
                        console.error('更新自动生成状态失败:', error);
                        // 恢复开关状态
                        autoGenerateEnabled.value = !value;
                        ElementPlus.ElMessage.error('更新自动生成状态失败: ' + error.message);
                    }
                };
                
                // 处理时间输入，但不立即应用
                const handleTimeInput = (value) => {
                    console.log('时间输入值:', value);
                    if (value) {
                        autoGenerateTime.value = value;
                    }
                };
                
                // 处理时间组件的确认按钮点击
                const handleTimeChange = async (value) => {
                    console.log('时间确认为:', value);
                    if (value) {
                        try {
                            // 调用后端API设置任务时间
                            const response = await fetch('/api/task/set-time', {
                                method: 'POST',
                                headers: {
                                    'Content-Type': 'application/json',
                                },
                                body: JSON.stringify({
                                    time: value
                                })
                            });
                            
                            const result = await response.json();
                            
                            if (result.success) {
                                // 更新本地状态
                                autoGenerateTime.value = value;
                                localStorage.setItem('autoGenerateTime', value);
                                
                                ElementPlus.ElMessage.success(result.message);
                                
                                // 如果任务已开启，后端会自动重新调度
                                console.log('任务时间已更新，后端将自动重新调度');
                            } else {
                                throw new Error(result.message || '设置任务时间失败');
                            }
                        } catch (error) {
                            console.error('设置任务时间出错:', error);
                            ElementPlus.ElMessage.error('设置任务时间失败: ' + error.message);
                        }
                    }
                };
                
                // 手动触发队列生成（保留用于测试）
                const manualTriggerQueue = async () => {
                    try {
                        const response = await fetch('/api/task/manual-trigger', {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json',
                            }
                        });
                        
                        const result = await response.json();
                        
                        if (result.success) {
                            ElementPlus.ElMessage.success(result.message);
                            // 刷新队列显示
                            await loadSavedQueue();
                            await refreshQueueStats(false);
                        } else {
                            ElementPlus.ElMessage.warning(result.message);
                        }
                    } catch (error) {
                        console.error('手动触发队列生成失败:', error);
                        ElementPlus.ElMessage.error('手动触发队列生成失败');
                    }
                };
                
                // 在组件挂载时加载群聊列表和检查已保存的队列
                onMounted(async () => {
                    console.log('组件挂载开始...');
                    try {
                        // 先加载分组列表数据
                        console.log('正在加载分组列表...');
                        await loadGroupList();
                        console.log('分组列表加载完成');
                        
                        // 移除群聊加载，设置为空数组
                        chatList.value = [];
                        filteredChatList.value = [];
                        console.log('设置群聊列表为空数组');
                        
                        console.log('正在加载已保存队列...');
                        await loadSavedQueue();
                        console.log('已保存队列加载完成');
                        
                        console.log('正在刷新队列统计...');
                        await refreshQueueStats(false); // 初始加载时不重复加载队列数据
                        console.log('队列统计刷新完成');
                        
                        // 设置默认值，不再从URL参数获取
                        queueForm.value.date = formatDate(new Date()); // 默认使用当天日期
                        queueForm.value.channelType = '新媒体渠道'; // 默认使用新媒体渠道
                        
                        // 从后端获取今天的任务状态
                        try {
                            console.log('正在从后端获取任务状态...');
                            const taskResponse = await fetch('/api/task/today');
                            const taskResult = await taskResponse.json();
                            
                            if (taskResult.success && taskResult.data) {
                                console.log('从后端获取的任务状态:', taskResult.data);
                                
                                // 设置自动生成开关状态
                                autoGenerateEnabled.value = taskResult.data.status === 1;
                                
                                // 设置自动生成时间
                                if (taskResult.data.formatted_time) {
                                    autoGenerateTime.value = taskResult.data.formatted_time;
                                } else {
                                    autoGenerateTime.value = '09:00';
                                }
                                
                                // 同步到本地存储
                                localStorage.setItem('autoGenerateEnabled', autoGenerateEnabled.value);
                                localStorage.setItem('autoGenerateTime', autoGenerateTime.value);
                                
                                console.log('任务状态已从后端同步:', {
                                    enabled: autoGenerateEnabled.value,
                                    time: autoGenerateTime.value
                                });
                            } else {
                                console.log('后端无任务记录，使用默认值');
                                // 使用默认值
                                autoGenerateEnabled.value = false;
                                autoGenerateTime.value = '09:00';
                                localStorage.setItem('autoGenerateEnabled', false);
                                localStorage.setItem('autoGenerateTime', '09:00');
                            }
                        } catch (error) {
                            console.error('获取任务状态失败，使用本地存储的值:', error);
                            
                            // 如果后端获取失败，回退到本地存储
                            const savedAutoGenerateEnabled = localStorage.getItem('autoGenerateEnabled');
                            const savedAutoGenerateTime = localStorage.getItem('autoGenerateTime');
                            
                            autoGenerateEnabled.value = savedAutoGenerateEnabled === 'true';
                            autoGenerateTime.value = savedAutoGenerateTime || '09:00';
                        }
                        
                        console.log('组件挂载完成');
                    } catch (error) {
                        console.error('组件挂载过程中出错:', error);
                        ElementPlus.ElMessage.error('加载数据失败，请尝试刷新页面');
                    }
                });
                
                // 在组件卸载前的清理工作
                onUnmounted(() => {
                    console.log('组件卸载，清理完成');
                });
                
                // 处理搜索功能
                const handleSearch = () => {
                    console.log('搜索条件变更:', searchQuery.value);
                    applyFilters();
                };
                
                // 处理分组过滤变化
                const handleGroupFilterChange = () => {
                    console.log('分组筛选条件变更:', selectedGroupFilter.value);
                    applyFilters();
                };
                
                // 监听筛选条件变化，自动应用筛选
                watch([searchQuery, selectedGroupFilter], () => {
                    console.log('筛选条件变化，自动应用');
                    applyFilters();
                });
                
                // 应用所有过滤条件
                const applyFilters = () => {
                    // 钉钉功能已禁用，简化为空实现
                    console.log('钉钉功能已禁用，过滤功能不可用');
                };
                
                // 显示添加群聊对话框
                const showAddChatDialog = () => {
                    // 钉钉功能已禁用，显示提示消息
                    ElementPlus.ElMessage({
                        message: '钉钉机器人功能已禁用，无法添加群聊。',
                        type: 'warning',
                        duration: 3000
                    });
                };
                
                // 显示编辑群聊对话框
                const editChat = (chat) => {
                    // 钉钉功能已禁用，显示提示消息
                    ElementPlus.ElMessage({
                        message: '钉钉机器人功能已禁用，无法编辑群聊。',
                        type: 'warning',
                        duration: 3000
                    });
                };
                
                // 加载群聊列表
                const loadChatList = async () => {
                    // 钉钉功能已禁用，不再调用API，直接返回空数组
                    chatList.value = [];
                    filteredChatList.value = [];
                    console.log('钉钉功能已禁用，群聊列表设置为空');
                    return [];
                };
                
                // 检查同一分组内是否存在相同姓名的群聊
                const checkDuplicateOwnerInGroup = (groupId, ownerName, excludeChatId = null) => {
                    // 钉钉功能已禁用，简化为空实现
                    return null;
                };
                
                // 钉钉功能已禁用，移除了原addChat函数
                const addChat = async () => {
                    ElementPlus.ElMessage({
                        message: '钉钉机器人功能已禁用，无法添加群聊。',
                        type: 'warning',
                        duration: 3000
                    });
                };
                
                // 钉钉功能已禁用，移除了原updateChat函数
                const updateChat = async () => {
                    ElementPlus.ElMessage({
                        message: '钉钉机器人功能已禁用，无法更新群聊。',
                        type: 'warning',
                        duration: 3000
                    });
                };
                
                // 钉钉功能已禁用，移除了原deleteChat函数
                const deleteChat = async (chatId) => {
                    ElementPlus.ElMessage({
                        message: '钉钉机器人功能已禁用，请使用其他方式管理群聊。',
                        type: 'warning',
                        duration: 3000
                    });
                };
                
                // 钉钉功能已禁用，移除了原sendMessage函数
                const sendMessage = async () => {
                    ElementPlus.ElMessage({
                        message: '钉钉机器人功能已禁用，请使用其他方式发送数据。',
                        type: 'warning',
                        duration: 3000
                    });
                };
                
                // 更新接待数据的函数
                const updateReceptionCount = async (memberName, ruleDate, isPaid) => {
                    try {
                        // 先获取该成员的分发规则
                        const rulesResponse = await fetch(`/api/distribution/rules?rule_date=${ruleDate}`);
                        const rulesData = await rulesResponse.json();
                        
                        // 查找匹配的规则
                        const rule = rulesData.find(r => r.member === memberName);
                        if (!rule) {
                            console.warn(`未找到${memberName}的分发规则`);
                            return;
                        }
                        
                        // 准备更新数据
                        const actual_total = (rule.actual_total || 0) + 1;
                        // 修复计算逻辑，直接判断isPaid来决定增加哪个计数
                        let actual_free_reception = rule.actual_free || 0;
                        let actual_paid_reception = rule.actual_paid || 0;
                        
                        if (isPaid) {
                            actual_paid_reception += 1;
                        } else {
                            actual_free_reception += 1;
                        }
                        
                        console.log(`更新接待数据: 总数=${actual_total}, 免费=${actual_free_reception}, 付费=${actual_paid_reception}`);
                        
                        // 调用API更新实际接待数
                        const updateResponse = await fetch(`/api/distribution/update-actual-reception/${rule.id}?actual_reception=${actual_total}&actual_free_reception=${actual_free_reception}&actual_paid_reception=${actual_paid_reception}`, {
                            method: 'PUT'
                        });
                        
                        const updateData = await updateResponse.json();
                        console.log('更新接待数据结果：', updateData);
                        
                    } catch (error) {
                        console.error('更新接待数据API调用失败：', error);
                    }
                };
                
                // 钉钉功能已禁用，移除了原showAllChatsDialog函数
                const showAllChatsDialog = async () => {
                    ElementPlus.ElMessage({
                        message: '钉钉机器人功能已禁用，群聊列表不可用。',
                        type: 'warning',
                        duration: 3000
                    });
                };
                
                // 移除了前端队列生成逻辑，现在使用后端API
                // 原 generateQueueData 函数已移除，队列生成现在在后端完成













                
                // 执行自动生成队列 - 优化为并行处理和更好的用户体验
                const generateQueue = async () => {
                    // 验证表单数据
                    if (!queueForm.value.date) {
                        ElementPlus.ElMessage.warning('请选择日期');
                        return;
                    }

                    // 检查是否已存在队列
                    if (hasExistingQueue.value) {
                        ElementPlus.ElMessage.warning('当日已有分发队列，无法重新生成');
                        return;
                    }

                    queueLoading.value = true;
                    queueGenerated.value = false;

                    // 显示加载提示
                    const loadingMessage = ElementPlus.ElMessage({
                        message: '正在生成队列，请稍候...',
                        type: 'info',
                        duration: 0, // 不自动关闭
                        showClose: false
                    });

                    try {
                        // 使用并行处理生成三个渠道的队列
                        const channelTypes = ['电商渠道', '公海分发', '新媒体渠道'];
                        const successChannels = []; // 成功的渠道
                        const failedChannels = []; // 失败的渠道
                        let totalItemsGenerated = 0;

                        // 并行调用API生成队列
                        console.log('开始并行生成三个渠道的队列...');

                        const generatePromises = channelTypes.map(async (channelType) => {
                            try {
                                console.log(`开始生成 ${channelType} 队列数据...`);

                                const response = await fetch('/api/distribution/queue/generate-and-save', {
                                    method: 'POST',
                                    headers: {
                                        'Content-Type': 'application/json'
                                    },
                                    body: JSON.stringify({
                                        queue_date: queueForm.value.date,
                                        channel_type: channelType
                                    })
                                });

                                if (response.ok) {
                                    const result = await response.json();
                                    console.log(`成功生成 ${channelType} 队列数据:`, result);

                                    // 构建前端需要的数据格式
                                    const queueResult = {
                                        free_queue: result.free_queue || [],
                                        paid_queue: result.paid_queue || [],
                                        total_items: result.total_items || 0,
                                        channel_type: channelType,
                                        algorithm_description: result.algorithm_description
                                    };

                                    // 保存到对应的渠道数据
                                    if (channelType === '电商渠道') {
                                        ecommerceQueueData.value = queueResult;
                                    } else if (channelType === '公海分发') {
                                        publicSeaQueueData.value = queueResult;
                                    } else {
                                        newMediaQueueData.value = queueResult;
                                    }

                                    console.log(`${channelType} 队列已生成并保存，应用了时间段交叉排序`);
                                    successChannels.push(channelType);

                                    return {
                                        success: true,
                                        channelType,
                                        result: queueResult,
                                        totalItems: queueResult.total_items
                                    };
                                } else {
                                    const error = await response.json();
                                    console.error(`生成 ${channelType} 队列失败:`, error);

                                    // 如果是队列已存在的错误，视为成功
                                    if (error.detail && error.detail.includes('队列已存在')) {
                                        console.warn(`${channelType} 队列已存在，跳过生成`);
                                        successChannels.push(channelType);

                                        return {
                                            success: true,
                                            channelType,
                                            result: null,
                                            totalItems: 0,
                                            skipped: true
                                        };
                                    }

                                    failedChannels.push({
                                        channelType,
                                        error: error.detail || '未知错误'
                                    });

                                    const emptyResult = {
                                        free_queue: [],
                                        paid_queue: [],
                                        total_items: 0,
                                        channel_type: channelType
                                    };

                                    // 保存到对应的渠道数据
                                    if (channelType === '电商渠道') {
                                        ecommerceQueueData.value = emptyResult;
                                    } else if (channelType === '公海分发') {
                                        publicSeaQueueData.value = emptyResult;
                                    } else {
                                        newMediaQueueData.value = emptyResult;
                                    }

                                    return {
                                        success: false,
                                        channelType,
                                        error: error.detail || '未知错误'
                                    };
                                }
                            } catch (error) {
                                console.error(`生成 ${channelType} 队列数据出错:`, error);

                                failedChannels.push({
                                    channelType,
                                    error: error.message || '网络错误'
                                });

                                const emptyResult = {
                                    free_queue: [],
                                    paid_queue: [],
                                    total_items: 0,
                                    channel_type: channelType
                                };

                                // 保存到对应的渠道数据
                                if (channelType === '电商渠道') {
                                    ecommerceQueueData.value = emptyResult;
                                } else if (channelType === '公海分发') {
                                    publicSeaQueueData.value = emptyResult;
                                } else {
                                    newMediaQueueData.value = emptyResult;
                                }

                                return {
                                    success: false,
                                    channelType,
                                    error: error.message || '网络错误'
                                };
                            }
                        });

                        // 等待所有渠道的队列生成完成
                        const generateResults = await Promise.all(generatePromises);

                        // 关闭加载提示
                        loadingMessage.close();

                        // 统计结果
                        totalItemsGenerated = generateResults.reduce((total, result) => {
                            return total + (result.totalItems || 0);
                        }, 0);

                        const successCount = successChannels.length;
                        const failedCount = failedChannels.length;

                        console.log('队列生成结果统计:', {
                            成功渠道: successChannels,
                            失败渠道: failedChannels,
                            总队列数: totalItemsGenerated
                        });

                        // 设置主队列数据，兼容现有逻辑
                        if (queueForm.value.channelType === '电商渠道') {
                            queueData.value = ecommerceQueueData.value;
                        } else if (queueForm.value.channelType === '公海分发') {
                            queueData.value = publicSeaQueueData.value;
                        } else {
                            queueData.value = newMediaQueueData.value;
                        }

                        console.log('当前显示的队列数据:', queueData.value);

                        // 根据结果显示不同的消息
                        if (successCount === channelTypes.length) {
                            // 全部成功
                            ElementPlus.ElMessage.success(`成功生成并保存分发队列数据，共 ${totalItemsGenerated} 条，已应用时间段交叉排序`);
                            queueGenerated.value = true;
                            hasExistingQueue.value = true;
                        } else if (successCount > 0) {
                            // 部分成功
                            const successMsg = `${successChannels.join('、')} 队列生成成功`;
                            const failedMsg = failedChannels.map(f => `${f.channelType}: ${f.error}`).join('；');
                            ElementPlus.ElMessage.warning(`${successMsg}，但 ${failedMsg}`);
                            queueGenerated.value = true;
                            hasExistingQueue.value = successCount > 0;
                        } else {
                            // 全部失败
                            const failedMsg = failedChannels.map(f => `${f.channelType}: ${f.error}`).join('；');
                            ElementPlus.ElMessage.error(`队列生成失败：${failedMsg}`);
                        }

                        // 如果有成功的渠道，刷新统计信息
                        if (successCount > 0) {
                            await refreshQueueStats(false);
                        }

                    } catch (error) {
                        console.error('生成分发队列出错:', error);

                        // 关闭加载提示
                        if (loadingMessage) {
                            loadingMessage.close();
                        }

                        ElementPlus.ElMessage.error(`生成分发队列失败: ${error.message || '未知错误'}`);
                        queueData.value = null;
                        ecommerceQueueData.value = { free_queue: [], paid_queue: [] };
                        publicSeaQueueData.value = { free_queue: [], paid_queue: [] };
                        newMediaQueueData.value = { free_queue: [], paid_queue: [] };
                    } finally {
                        queueLoading.value = false;
                    }
                };
                
                // 处理分发数据
                const handleDistribute = async (row) => {
                    // 如果已经分发过了，不再处理
                    if (row.status === 'distributed' || row.status === '已分发') {
                        ElementPlus.ElMessage.info('该队列项已完成分发');
                        return;
                    }
                    
                    // 如果已取消，不再处理
                    if (row.status === 'cancelled' || row.status === '已取消') {
                        ElementPlus.ElMessage.warning('该队列项已被取消');
                        return;
                    }
                    
                    // 钉钉功能已禁用，显示提示消息
                    ElementPlus.ElMessage({
                        message: '钉钉机器人功能已禁用，请使用其他方式分发数据。',
                        type: 'warning',
                        duration: 3000
                    });
                };
                
                // 数据分发功能
                const distributeMessage = async () => {
                    // 显示功能已禁用的提示
                    ElementPlus.ElMessage({
                        message: '钉钉机器人功能已禁用，请使用其他方式发送数据。',
                        type: 'warning',
                        duration: 3000
                    });
                    
                    // 关闭对话框
                    distributeDialogVisible.value = false;
                };
                
                // 添加保存队列函数
                const saveQueue = async () => {
                    if (!queueData.value || (!queueData.value.free_queue && !queueData.value.paid_queue)) {
                        ElementPlus.ElMessage.warning('没有队列数据可保存');
                        return;
                    }
                    
                    try {
                        const saveData = {
                            queue_date: queueForm.value.date,
                            channel_type: queueForm.value.channelType,
                            free_queue: queueData.value.free_queue || [],
                            paid_queue: queueData.value.paid_queue || []
                        };
                        
                        const response = await fetch('/api/distribution/queue/save', {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json'
                            },
                            body: JSON.stringify(saveData)
                        });
                        
                        if (response.ok) {
                            const data = await response.json();
                            ElementPlus.ElMessage.success(data.message || '队列保存成功');
                        } else {
                            const error = await response.json();
                            ElementPlus.ElMessage.error(error.detail || '保存队列失败');
                        }
                    } catch (error) {
                        console.error('保存队列出错:', error);
                        ElementPlus.ElMessage.error('保存队列失败');
                    }
                };
                
                // 加载已保存的队列数据 - 优化为并行加载
                const loadSavedQueue = async () => {
                    if (!queueForm.value.date) {
                        console.log('未设置日期，无法加载队列数据');
                        return;
                    }

                    console.log(`正在并行加载队列数据: 日期=${queueForm.value.date}`);

                    try {
                        // 并行加载三个渠道的数据
                        const loadPromises = [
                            // 加载电商渠道数据
                            fetch(`/api/distribution/queue/data?queue_date=${queueForm.value.date}&channel_type=电商渠道`)
                                .then(async (response) => {
                                    if (response.ok) {
                                        const data = await response.json();
                                        console.log('获取到电商渠道队列数据:', data);

                                        // 如果存在已保存的队列数据
                                        if (data && ((data.free_queue && data.free_queue.length > 0) || (data.paid_queue && data.paid_queue.length > 0))) {
                                            console.log('发现已保存电商渠道队列数据');
                                            ecommerceQueueData.value = data;
                                            return { success: true, channelType: '电商渠道', hasData: true };
                                        } else {
                                            console.log('未找到已保存电商渠道队列数据');
                                            ecommerceQueueData.value = { free_queue: [], paid_queue: [] };
                                            return { success: true, channelType: '电商渠道', hasData: false };
                                        }
                                    } else {
                                        console.error('加载电商渠道队列数据失败, HTTP状态码:', response.status);
                                        ecommerceQueueData.value = { free_queue: [], paid_queue: [] };
                                        return { success: false, channelType: '电商渠道', error: `HTTP ${response.status}` };
                                    }
                                })
                                .catch((error) => {
                                    console.error('加载电商渠道队列数据出错:', error);
                                    ecommerceQueueData.value = { free_queue: [], paid_queue: [] };
                                    return { success: false, channelType: '电商渠道', error: error.message };
                                }),

                            // 加载公海分发队列数据
                            fetch(`/api/distribution/queue/data?queue_date=${queueForm.value.date}&channel_type=公海分发`)
                                .then(async (response) => {
                                    if (response.ok) {
                                        const data = await response.json();
                                        console.log('获取到公海分发队列数据:', data);

                                        // 如果存在已保存的队列数据
                                        if (data && ((data.free_queue && data.free_queue.length > 0) || (data.paid_queue && data.paid_queue.length > 0))) {
                                            console.log('发现已保存公海分发队列数据');
                                            publicSeaQueueData.value = data;
                                            return { success: true, channelType: '公海分发', hasData: true };
                                        } else {
                                            console.log('未找到已保存公海分发队列数据');
                                            publicSeaQueueData.value = { free_queue: [], paid_queue: [] };
                                            return { success: true, channelType: '公海分发', hasData: false };
                                        }
                                    } else {
                                        console.error('加载公海分发队列数据失败, HTTP状态码:', response.status);
                                        publicSeaQueueData.value = { free_queue: [], paid_queue: [] };
                                        return { success: false, channelType: '公海分发', error: `HTTP ${response.status}` };
                                    }
                                })
                                .catch((error) => {
                                    console.error('加载公海分发队列数据出错:', error);
                                    publicSeaQueueData.value = { free_queue: [], paid_queue: [] };
                                    return { success: false, channelType: '公海分发', error: error.message };
                                }),

                            // 加载新媒体渠道数据
                            fetch(`/api/distribution/queue/data?queue_date=${queueForm.value.date}&channel_type=新媒体渠道`)
                                .then(async (response) => {
                                    if (response.ok) {
                                        const data = await response.json();
                                        console.log('获取到新媒体渠道队列数据:', data);

                                        // 如果存在已保存的队列数据
                                        if (data && ((data.free_queue && data.free_queue.length > 0) || (data.paid_queue && data.paid_queue.length > 0))) {
                                            console.log('发现已保存新媒体渠道队列数据');
                                            newMediaQueueData.value = data;
                                            return { success: true, channelType: '新媒体渠道', hasData: true };
                                        } else {
                                            console.log('未找到已保存新媒体渠道队列数据');
                                            newMediaQueueData.value = { free_queue: [], paid_queue: [] };
                                            return { success: true, channelType: '新媒体渠道', hasData: false };
                                        }
                                    } else {
                                        console.error('加载新媒体渠道队列数据失败, HTTP状态码:', response.status);
                                        newMediaQueueData.value = { free_queue: [], paid_queue: [] };
                                        return { success: false, channelType: '新媒体渠道', error: `HTTP ${response.status}` };
                                    }
                                })
                                .catch((error) => {
                                    console.error('加载新媒体渠道队列数据出错:', error);
                                    newMediaQueueData.value = { free_queue: [], paid_queue: [] };
                                    return { success: false, channelType: '新媒体渠道', error: error.message };
                                })
                        ];

                        // 等待所有加载完成
                        const loadResults = await Promise.all(loadPromises);

                        // 分析加载结果
                        const successResults = loadResults.filter(r => r.success);
                        const failedResults = loadResults.filter(r => !r.success);
                        const hasDataResults = successResults.filter(r => r.hasData);

                        console.log('并行加载结果:', {
                            成功: successResults.map(r => r.channelType),
                            失败: failedResults.map(r => `${r.channelType}: ${r.error}`),
                            有数据: hasDataResults.map(r => r.channelType)
                        });

                        // 输出队列数据状态
                        console.log('加载完成后队列数据状态：', {
                            'ecommerceQueueData': {
                                'free_queue': ecommerceQueueData.value.free_queue ? ecommerceQueueData.value.free_queue.length : 0,
                                'paid_queue': ecommerceQueueData.value.paid_queue ? ecommerceQueueData.value.paid_queue.length : 0
                            },
                            'publicSeaQueueData': {
                                'free_queue': publicSeaQueueData.value.free_queue ? publicSeaQueueData.value.free_queue.length : 0,
                                'paid_queue': publicSeaQueueData.value.paid_queue ? publicSeaQueueData.value.paid_queue.length : 0
                            },
                            'newMediaQueueData': {
                                'free_queue': newMediaQueueData.value.free_queue ? newMediaQueueData.value.free_queue.length : 0,
                                'paid_queue': newMediaQueueData.value.paid_queue ? newMediaQueueData.value.paid_queue.length : 0
                            }
                        });

                        // 设置主队列数据，用于兼容现有功能
                        if (queueForm.value.channelType === '电商渠道') {
                            queueData.value = ecommerceQueueData.value;
                        } else if (queueForm.value.channelType === '公海分发') {
                            queueData.value = publicSeaQueueData.value;
                        } else {
                            queueData.value = newMediaQueueData.value;
                        }

                        // 根据加载结果设置状态和显示消息
                        if (hasDataResults.length > 0) {
                            queueGenerated.value = true;
                            hasExistingQueue.value = true;

                            if (hasDataResults.length === 2) {
                                ElementPlus.ElMessage.success('已加载两个渠道的队列数据');
                            } else {
                                const loadedChannels = hasDataResults.map(r => r.channelType).join('、');
                                ElementPlus.ElMessage.success(`已加载 ${loadedChannels} 的队列数据`);
                            }
                        } else {
                            queueGenerated.value = false;
                            hasExistingQueue.value = false;

                            if (failedResults.length > 0) {
                                const failedMsg = failedResults.map(r => `${r.channelType}: ${r.error}`).join('；');
                                ElementPlus.ElMessage.warning(`加载队列数据失败：${failedMsg}`);
                            } else {
                                ElementPlus.ElMessage.info(`${queueForm.value.date} 没有保存的队列数据`);
                            }
                        }
                        
                    } catch (error) {
                        console.error('加载队列数据出错:', error);
                        // 发生错误也清空数据
                        queueData.value = null;
                        ecommerceQueueData.value = { free_queue: [], paid_queue: [] };
                        publicSeaQueueData.value = { free_queue: [], paid_queue: [] };
                        newMediaQueueData.value = { free_queue: [], paid_queue: [] };
                        queueGenerated.value = false;
                        hasExistingQueue.value = false; // 设置无队列标记
                        queueStats.value = null;
                    }
                };
                
                // 修改日期或渠道类型时自动加载已保存队列和统计信息
                watch([() => queueForm.value.date, () => queueForm.value.channelType], async () => {
                    await loadSavedQueue();
                    // 移除自动刷新统计，避免频繁刷新
                    // await refreshQueueStats();
                }, { immediate: false });
                
                // 日期变化处理
                const changeDate = async (offset) => {
                    const currentDate = new Date(queueForm.value.date);
                    currentDate.setDate(currentDate.getDate() + offset);
                    queueForm.value.date = formatDate(currentDate);
                    
                    console.log(`日期已变更为: ${queueForm.value.date}`);
                    
                    // 不再更新URL参数
                    
                    // 手动触发加载已保存队列和刷新统计
                    await loadSavedQueue();
                    // 队列加载后只刷新统计数据，不再重新加载队列
                    await refreshQueueStats(false);
                };
                
                // 修改删除队列功能添加确认对话框并同时删除所有渠道数据
                const deleteQueue = async () => {
                    if (!queueForm.value.date) {
                        ElementPlus.ElMessage.warning('请先选择要删除的队列的日期');
                        return;
                    }
                    
                    // 声明倒计时变量
                    let countdown = 30;
                    let countdownTimer = null;
                    let confirmBoxInstance = null;
                    
                    try {
                        // 创建自定义确认对话框，包含倒计时功能
                        confirmBoxInstance = ElementPlus.ElMessageBox.confirm(
                            Vue.h('div', null, [
                                Vue.h('p', { style: 'color: red; font-weight: bold; font-size: 16px;' }, '该操作存在较大风险，请谨慎决定是否删除！！！'),
                                Vue.h('p', null, `确定要删除${queueForm.value.date}的所有分发队列数据吗？此操作不可恢复。`),
                                Vue.h('p', { class: 'countdown-text', style: 'margin-top: 10px;' }, `删除按钮在 ${countdown} 秒后可用`)
                            ]),
                            '危险操作警告',
                            {
                                showCancelButton: true,
                                confirmButtonText: '删除',
                                cancelButtonText: '取消',
                                type: 'warning',
                                confirmButtonClass: 'countdown-confirm-button danger-delete-button',
                                beforeClose: (action, instance, done) => {
                                    if (action === 'confirm') {
                                        // 检查是否倒计时已完成
                                        if (countdown > 0) {
                                            return false; // 阻止关闭
                                        }
                                    }
                                    
                                    // 清除倒计时
                                    if (countdownTimer) {
                                        clearInterval(countdownTimer);
                                        countdownTimer = null;
                                    }
                                    
                                    done(); // 允许关闭对话框
                                    return true;
                                }
                            }
                        );
                        
                        // 添加删除按钮的样式
                        const styleElement = document.createElement('style');
                        styleElement.textContent = `
                            .danger-delete-button {
                                background-color: #F56C6C !important;
                                border-color: #F56C6C !important;
                            }
                            .danger-delete-button:hover {
                                background-color: #f78989 !important;
                                border-color: #f78989 !important;
                            }
                        `;
                        document.head.appendChild(styleElement);
                        
                        // 禁用确认按钮
                        const confirmButton = document.querySelector('.countdown-confirm-button');
                        if (confirmButton) {
                            confirmButton.disabled = true;
                            confirmButton.style.opacity = '0.5';
                            confirmButton.style.cursor = 'not-allowed';
                        }
                        
                        // 开始倒计时
                        countdownTimer = setInterval(() => {
                            countdown--;
                            
                            // 更新倒计时文本
                            const countdownText = document.querySelector('.countdown-text');
                            if (countdownText) {
                                countdownText.textContent = `删除按钮在 ${countdown} 秒后可用`;
                            }
                            
                            // 倒计时结束
                            if (countdown <= 0) {
                                clearInterval(countdownTimer);
                                
                                // 启用确认按钮
                                if (confirmButton) {
                                    confirmButton.disabled = false;
                                    confirmButton.style.opacity = '1';
                                    confirmButton.style.cursor = 'pointer';
                                }
                                
                                if (countdownText) {
                                    countdownText.textContent = '删除按钮已可用，请谨慎操作！';
                                    countdownText.style.color = 'red';
                                }
                            }
                        }, 1000);
                        
                        // 等待用户确认
                        await confirmBoxInstance;
                        
                        // 用户点击确认并且倒计时已结束
                        deleteLoading.value = true;
                        console.log(`正在删除队列: 日期=${queueForm.value.date}`);
                        
                        // 分别删除三种渠道类型的队列
                        const formattedDate = formatDate(queueForm.value.date);

                        // 删除电商渠道队列
                        const responseEcommerce = await fetch(`/api/distribution/queue/delete?queue_date=${formattedDate}&channel_type=电商渠道`, {
                            method: 'DELETE'
                        });

                        // 删除公海分发队列
                        const responsePublicSea = await fetch(`/api/distribution/queue/delete?queue_date=${formattedDate}&channel_type=公海分发`, {
                            method: 'DELETE'
                        });

                        // 删除新媒体渠道队列
                        const responseNewMedia = await fetch(`/api/distribution/queue/delete?queue_date=${formattedDate}&channel_type=新媒体渠道`, {
                            method: 'DELETE'
                        });

                        console.log('删除队列API响应状态:', {
                            ecommerce: responseEcommerce.status,
                            publicSea: responsePublicSea.status,
                            newMedia: responseNewMedia.status
                        });

                        // 清空所有队列数据
                        ecommerceQueueData.value = { free_queue: [], paid_queue: [] };
                        publicSeaQueueData.value = { free_queue: [], paid_queue: [] };
                        newMediaQueueData.value = { free_queue: [], paid_queue: [] };
                        queueData.value = { free_queue: [], paid_queue: [] };
                        queueGenerated.value = false;
                        hasExistingQueue.value = false;
                        queueStats.value = null;
                        
                        console.log('队列数据已成功清空');
                        ElementPlus.ElMessage.success(`删除成功: 所有分发队列数据已删除`);
                    } catch (error) {
                        // 清除倒计时
                        if (countdownTimer) {
                            clearInterval(countdownTimer);
                            countdownTimer = null;
                        }
                        
                        if (error === 'cancel') {
                            // 用户取消删除，不做任何操作
                            return;
                        }
                        console.error('删除队列出错:', error);
                        ElementPlus.ElMessage.error(`删除队列出错: ${error.message || '未知错误'}`);
                    } finally {
                        deleteLoading.value = false;
                    }
                };
                
                // 刷新队列统计信息
                const refreshQueueStats = async (shouldReloadQueue = false) => {
                    if (!queueForm.value.date) {
                        return;
                    }
                    
                    statsLoading.value = true;
                    
                    try {
                        // 获取新媒体渠道统计
                        const responseNewMedia = await fetch(`/api/distribution/queue/statistics?queue_date=${queueForm.value.date}&channel_type=新媒体渠道`);
                        const newMediaStats = responseNewMedia.ok ? await responseNewMedia.json() : null;
                        
                        // 获取电商渠道统计
                        const responseEcommerce = await fetch(`/api/distribution/queue/statistics?queue_date=${queueForm.value.date}&channel_type=电商渠道`);
                        const ecommerceStats = responseEcommerce.ok ? await responseEcommerce.json() : null;

                        // 获取公海分发队列统计
                        const responsePublicSea = await fetch(`/api/distribution/queue/statistics?queue_date=${queueForm.value.date}&channel_type=公海分发`);
                        const publicSeaStats = responsePublicSea.ok ? await responsePublicSea.json() : null;

                        // 合并统计数据
                        const mergedStats = newMediaStats || {};
                        
                        // 添加电商渠道统计
                        if (ecommerceStats) {
                            mergedStats.ecommerce_total = ecommerceStats.free_total || 0;
                            mergedStats.ecommerce_distributed = ecommerceStats.free_distributed || 0;
                            
                            // 合并成员统计
                            if (ecommerceStats.member_statistics && ecommerceStats.member_statistics.length > 0) {
                                if (!mergedStats.member_statistics) {
                                    mergedStats.member_statistics = [];
                                }
                                
                                // 添加电商渠道的成员统计
                                ecommerceStats.member_statistics.forEach(ecommerceMemberStat => {
                                    const existingMemberStat = mergedStats.member_statistics.find(
                                        ms => ms.member === ecommerceMemberStat.member
                                    );
                                    
                                    if (existingMemberStat) {
                                        // 合并已有成员的统计
                                        existingMemberStat.total_distributed += ecommerceMemberStat.total_distributed;
                                    } else {
                                        // 添加新成员的统计
                                        mergedStats.member_statistics.push({ ...ecommerceMemberStat });
                                    }
                                });
                            }
                        }

                        // 添加公海分发队列统计
                        if (publicSeaStats) {
                            mergedStats.public_sea_total = publicSeaStats.free_total || 0;
                            mergedStats.public_sea_distributed = publicSeaStats.free_distributed || 0;

                            // 合并成员统计
                            if (publicSeaStats.member_statistics && publicSeaStats.member_statistics.length > 0) {
                                if (!mergedStats.member_statistics) {
                                    mergedStats.member_statistics = [];
                                }

                                // 添加公海分发队列的成员统计
                                publicSeaStats.member_statistics.forEach(publicSeaMemberStat => {
                                    const existingMemberStat = mergedStats.member_statistics.find(
                                        ms => ms.member === publicSeaMemberStat.member
                                    );

                                    if (existingMemberStat) {
                                        // 合并已有成员的统计
                                        existingMemberStat.total_distributed += publicSeaMemberStat.total_distributed;
                                    } else {
                                        // 添加新成员的统计
                                        mergedStats.member_statistics.push({ ...publicSeaMemberStat });
                                    }
                                });
                            }
                        }

                        // 检查是否有实际的统计数据
                        if (mergedStats && (
                            mergedStats.free_total > 0 ||
                            mergedStats.paid_total > 0 ||
                            mergedStats.ecommerce_total > 0 ||
                            mergedStats.public_sea_total > 0
                        )) {
                            queueStats.value = mergedStats;
                        } else {
                            // 没有统计数据，清空
                            queueStats.value = null;
                            console.log(`${queueForm.value.date} 没有队列统计数据`);
                        }
                    } catch (error) {
                        console.error('刷新队列统计信息出错:', error);
                        // 发生错误时清空统计数据
                        queueStats.value = null;
                    } finally {
                        statsLoading.value = false;
                    }
                    
                    // 只有在显式要求刷新队列数据时才加载
                    if (shouldReloadQueue) {
                        try {
                            await loadSavedQueue();
                            console.log('已刷新队列数据');
                        } catch (error) {
                            console.error('刷新队列数据失败:', error);
                        }
                    }
                };
                
                // 获取分组列表
                const loadGroupList = async () => {
                    try {
                        const response = await fetch('/api/presets/sales');
                        if (response.ok) {
                            const data = await response.json();
                            // 将sales数据转换为分组列表格式
                            const groups = (data.sales || []).map(sale => ({
                                id: sale.id,
                                name: sale.group
                            }));
                            groupList.value = groups;
                            console.log('获取分组列表成功:', groupList.value);
                        } else {
                            const error = await response.json();
                            ElementPlus.ElMessage.error(error.detail || '获取分组列表失败');
                            console.error('获取分组列表失败:', error);
                        }
                    } catch (error) {
                        console.error('获取分组列表出错:', error);
                        ElementPlus.ElMessage.error('获取分组列表失败');
                    }
                };
                
                // 根据分组ID获取成员列表
                const loadMemberList = async (groupId) => {
                    if (!groupId) {
                        memberList.value = [];
                        return;
                    }
                    
                    membersLoading.value = true;
                    try {
                        const response = await fetch('/api/presets/sales');
                        if (response.ok) {
                            const data = await response.json();
                            // 查找选中的分组
                            const selectedGroup = (data.sales || []).find(sale => sale.id === groupId);
                            
                            if (selectedGroup && selectedGroup.members) {
                                // 将成员字符串拆分为数组
                                const memberNames = selectedGroup.members.split(',').map(name => name.trim());
                                // 转换为所需格式
                                const members = memberNames.map((name, index) => ({
                                    id: `${groupId}-${index}`,
                                    name: name
                                }));
                                memberList.value = members;
                                console.log('获取成员列表成功:', memberList.value);
                            } else {
                                memberList.value = [];
                                console.log('未找到该分组的成员:', groupId);
                            }
                        } else {
                            const error = await response.json();
                            ElementPlus.ElMessage.error(error.detail || '获取成员列表失败');
                            console.error('获取成员列表失败:', error);
                        }
                    } catch (error) {
                        console.error('获取成员列表出错:', error);
                        ElementPlus.ElMessage.error('获取成员列表失败');
                    } finally {
                        membersLoading.value = false;
                    }
                };
                
                // 处理分组变更
                const handleGroupChange = (groupId) => {
                    console.log('分组变更为:', groupId);
                    // 清空成员选择
                    chatForm.value.member_id = '';
                    // 加载对应分组的成员
                    loadMemberList(groupId);
                };
                
                // 处理成员变更
                const handleMemberChange = (memberId) => {
                    console.log('成员变更为:', memberId);
                    // 自动设置owner_name为所选成员的名称
                    if (memberId) {
                        const selectedMember = memberList.value.find(member => member.id === memberId);
                        if (selectedMember) {
                            chatForm.value.owner_name = selectedMember.name;
                        }
                    }
                };
                
                // 根据分组ID获取分组名称
                const getGroupName = (groupId) => {
                    if (!groupId) return '未知分组';
                    
                    // 1. 尝试直接通过ID匹配分组
                    const groupById = groupList.value.find(g => g.id === groupId);
                    if (groupById) return groupById.name;
                    
                    // 2. 检查groupId本身是否就是分组名称
                    const groupByName = groupList.value.find(g => g.name === groupId);
                    if (groupByName) return groupId;
                    
                    // 3. 在API调用前显示可能是真实分组名称的groupId
                    if (typeof groupId === 'string' && groupId.trim() !== '') {
                        // 如果不像是UUID，可能是直接存储的名称
                        const uuidPattern = /^[0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12}$/;
                        if (!uuidPattern.test(groupId)) {
                            return groupId;
                        }
                    }
                    
                    // 如果分组列表为空，主动加载
                    if (groupList.value.length === 0) {
                        console.log('分组列表为空，正在重新加载...');
                        loadGroupList();
                    }
                    
                    return '未知分组';
                };
                
                // 更新分发队列功能（修复版 - 只更新当前选中的渠道）
                const updateQueue = async () => {
                    if (!queueForm.value.date) {
                        ElementPlus.ElMessage.warning('请选择要更新的队列的日期');
                        return;
                    }

                    if (!queueForm.value.channelType) {
                        ElementPlus.ElMessage.warning('请选择要更新的渠道类型');
                        return;
                    }

                    updateLoading.value = true;

                    try {
                        // 首先确保已有队列数据
                        if (!hasExistingQueue.value) {
                            ElementPlus.ElMessage.warning('没有现有队列数据可以更新');
                            updateLoading.value = false;
                            return;
                        }

                        const currentChannelType = queueForm.value.channelType;
                        console.log(`正在更新${currentChannelType}队列...`);

                        // 调用后端队列更新API - 只更新当前选中的渠道
                        const response = await fetch('/api/distribution/queue/update', {
                            method: 'PUT',
                            headers: {
                                'Content-Type': 'application/json'
                            },
                            body: JSON.stringify({
                                queue_date: queueForm.value.date,
                                channel_type: currentChannelType
                            })
                        });

                        if (response.ok) {
                            const result = await response.json();
                            console.log(`${currentChannelType}队列更新成功:`, result);

                            // 更新对应渠道的本地缓存数据
                            if (currentChannelType === '电商渠道') {
                                ecommerceQueueData.value = result;
                            } else if (currentChannelType === '公海分发') {
                                publicSeaQueueData.value = result;
                            } else if (currentChannelType === '新媒体渠道') {
                                newMediaQueueData.value = result;
                            }

                            // 更新主队列数据（当前显示的数据）
                            queueData.value = result;

                            // 显示成功消息
                            ElementPlus.ElMessage.success(`${currentChannelType}分发队列已成功更新`);

                            // 打印更新结果
                            console.log(`${currentChannelType}更新结果:`, {
                                免费队列: result.free_queue ? result.free_queue.length : 0,
                                付费队列: result.paid_queue ? result.paid_queue.length : 0,
                                总计: (result.free_queue ? result.free_queue.length : 0) + (result.paid_queue ? result.paid_queue.length : 0)
                            });

                            // 刷新统计信息，不重复加载队列数据
                            await refreshQueueStats(false);

                        } else {
                            const error = await response.json();
                            console.error(`${currentChannelType}队列更新失败:`, error);
                            ElementPlus.ElMessage.error(error.detail || `${currentChannelType}队列更新失败`);
                        }

                    } catch (error) {
                        console.error('更新队列出错:', error);
                        ElementPlus.ElMessage.error('更新队列失败');
                    } finally {
                        updateLoading.value = false;
                    }
                };
                
                // 格式化时间显示
                const formatTime = (isoTime) => {
                    if (!isoTime) return '未知';
                    const date = new Date(isoTime);
                    return date.toLocaleString('zh-CN', { 
                        hour: '2-digit', 
                        minute: '2-digit',
                        second: '2-digit',
                        hour12: false
                    });
                };
                
                // 组件挂载后初始化
                Vue.onMounted(async () => {
                    // 原有功能的初始化
                    console.log('分发计划页面初始化');
                    
                    // 加载数据
                    await Promise.all([
                        loadSavedQueue(),
                        loadGroupList(),
                        loadMemberList()
                    ]);
                    
                    // 检查URL参数中是否有日期
                    const urlParams = new URLSearchParams(window.location.search);
                    const dateParam = urlParams.get('date');
                    if (dateParam) {
                        queueForm.value.date = dateParam;
                    } else {
                        // 默认设置为今天
                        queueForm.value.date = formatDate(new Date());
                    }
                    
                    // 如果启用了自动生成，设置定时器
                    if (autoGenerateEnabled.value) {
                        setAutoGenerateTimer();
                    }
                });

                // 监听渠道类型变化，切换显示对应的队列数据
                const handleChannelTypeChange = () => {
                    console.log(`切换到 ${queueForm.value.channelType} 队列数据`);
                    loadSavedQueue(); // 重新加载对应渠道的队列数据
                    refreshQueueStats(false); // 更新队列统计信息
                };
                
                // 添加表格行类名函数，用于显示不同状态的行样式
                const getRowClassName = ({ row }) => {
                    if (row.status === 'distributed' || row.status === '已分发') {
                        return 'distributed-row';
                    } else if (row.status === 'cancelled' || row.status === '已取消') {
                        return 'cancelled-row';
                    } else {
                        return 'pending-row';
                    }
                };
                
                // 添加调试功能
                const debugQueueData = () => {
                    console.log('======== 调试队列数据 ========');
                    console.log('queueForm:', queueForm.value);
                    console.log('queueGenerated:', queueGenerated.value);
                    console.log('hasExistingQueue:', hasExistingQueue.value);
                    
                    console.log('电商渠道队列数据:', ecommerceQueueData.value);
                    console.log('电商渠道队列长度:', {
                        free_queue: ecommerceQueueData.value.free_queue ? ecommerceQueueData.value.free_queue.length : 0,
                        paid_queue: ecommerceQueueData.value.paid_queue ? ecommerceQueueData.value.paid_queue.length : 0
                    });
                    
                    console.log('新媒体渠道队列数据:', newMediaQueueData.value);
                    console.log('新媒体渠道队列长度:', {
                        free_queue: newMediaQueueData.value.free_queue ? newMediaQueueData.value.free_queue.length : 0,
                        paid_queue: newMediaQueueData.value.paid_queue ? newMediaQueueData.value.paid_queue.length : 0
                    });
                    
                    console.log('主队列数据:', queueData.value);
                    console.log('队列统计数据:', queueStats.value);
                    
                    // 重新加载队列数据
                    ElementPlus.ElMessage.info('正在重新加载队列数据，请查看控制台日志');
                    loadSavedQueue();
                };
                
                return {
                    chatForm,
                    chatList,
                    filteredChatList,
                    searchQuery,
                    messageForm,
                    dialogVisible,
                    allChatsDialogVisible,
                    isEditMode,
                    showAddChatDialog,
                    editChat,
                    addChat,
                    updateChat,
                    loadChatList,
                    deleteChat,
                    sendMessage,
                    showAllChatsDialog,
                    handleSearch,
                    queueForm,
                    queueData,
                    queueLoading,
                    queueGenerated,
                    generateQueue,
                    handleDistribute,
                    formatDate,
                    distributeDialogVisible,
                    distributeLoading,
                    formattedData,
                    distributeForm,
                    distributeMessage,
                    formatDateTime,
                    changeDate,
                    saveQueue,
                    loadSavedQueue,
                    deleteQueue,
                    queueStats,
                    statsLoading,
                    refreshQueueStats,
                    autoGenerateEnabled,
                    autoGenerateTime,
                    handleAutoGenerateChange,
                    handleTimeChange,
                    groupList,
                    memberList,
                    membersLoading,
                    loadGroupList,
                    loadMemberList,
                    handleGroupChange,
                    handleMemberChange,
                    selectedGroupFilter,
                    getGroupName,
                    uniqueGroupNames,
                    checkDuplicateOwnerInGroup,
                    hasExistingQueue,
                    updateQueue,
                    deleteLoading,
                    updateLoading,
                    formatTime,
                    handleChannelTypeChange,
                    getRowClassName,
                    debugQueueData,
                    // 添加这两个缺失的变量
                    ecommerceQueueData,
                    newMediaQueueData,
                    // 添加监测相关占位变量，防止报错
                    monitoringEnabled: Vue.ref(false),
                    monitoringSwitchLoading: Vue.ref(false),
                    monitoringStats: Vue.ref({
                        todayCount: 0,
                        totalCount: 0,
                        todaySent: 0,
                        totalSent: 0,
                        waiting: 0,
                        waitingTrend: 0,
                        avgProcessTime: 0,
                        lastUpdateTime: null
                    }),
                    monitoringDateFilter: Vue.ref('today'),
                    customDateRange: Vue.ref([]),
                    handleMonitoringStatusChange: () => {},
                    handleDateFilterChange: () => {},
                    handleCustomDateRangeChange: () => {},
                };
            }
        });
        
        app.use(ElementPlus);
        app.mount('#app');
    </script>
</body>
</html>