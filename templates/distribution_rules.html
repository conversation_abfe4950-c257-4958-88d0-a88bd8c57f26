{% raw %}
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>分发管理 - Lead Distribution</title>
    <link href="/static/css/element-plus.css" rel="stylesheet">
    <link rel="stylesheet" href="/static/css/font-awesome.min.css">
    <!-- 引入API密钥获取脚本 -->
    <script src="/static/js/getKey.js"></script>
    <style>
        body {
            margin: 0;
            padding: 0;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
            background: #f5f7fa;
        }
        
        .container {
            padding: 20px;
        }
        
        .header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }
        
        .header h1 {
            font-size: 24px;
            color: #303133;
            margin: 0;
        }
        
        .back-button {
            display: inline-flex;
            align-items: center;
            padding: 8px 16px;
            background-color: #409EFF;
            color: white;
            border-radius: 4px;
            text-decoration: none;
            font-size: 14px;
            transition: background-color 0.3s;
        }
        
        .back-button:hover {
            background-color: #3a8ee6;
        }
        
        .back-icon {
            margin-right: 5px;
        }

        .page-content {
            background: #fff;
            border-radius: 4px;
            box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
            padding: 20px;
        }

        .action-bar {
            margin-bottom: 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .date-selector {
            display: flex;
            align-items: center;
        }

        .date-selector .el-date-editor {
            margin-right: 10px;
        }

        /* 修改表格容器样式，添加最大高度和滚动功能 */
        .table-container {
            margin-top: 20px;
            height: calc(100vh - 340px);
            overflow-y: auto;
            overflow-x: hidden; /* 禁用水平滚动 */
            border: 1px solid #e4e7ed;
            border-radius: 4px;
            padding-bottom: 12px; /* 从10px增加到12px */
            padding-top: 0px; /* 添加顶部内边距 */
            position: relative; /* 添加相对定位 */
            width: 100%; /* 确保容器宽度为100% */
        }

        /* 确保表格内容正确滚动 */
        .el-table {
            overflow: visible !important;
            width: 100% !important; /* 确保表格宽度为100% */
            table-layout: fixed !important; /* 使用固定表格布局 */
            position: relative; /* 添加相对定位 */
        }

        /* 确保表格头部不随内容滚动 */
        .el-table__header-wrapper {
            overflow: visible !important;
            position: sticky !important; /* 使用sticky定位 */
            top: 0 !important; /* 固定在顶部 */
            z-index: 10 !important; /* 确保在其他元素上方 */
        }

        /* 确保表格体部分可以滚动 */
        .el-table__body-wrapper {
            overflow-y: auto !important;
        }

        /* 隐藏表格组件自带的水平滚动条 */
        .el-table__body-wrapper::-webkit-scrollbar:horizontal {
            display: none !important;
        }
        
        /* 隐藏表格底部的水平滚动条 */
        .el-scrollbar__bar.is-horizontal {
            display: none !important;
        }
        
        /* 只保留表格容器的水平滚动条 */
        .table-container::-webkit-scrollbar:horizontal {
            display: block !important;
            height: 8px;
        }

        /* 压缩表格行高 */
        .el-table .el-table__row {
            height: 24px; /* 原来是30px，压缩20%后为24px */
        }

        /* 压缩表格单元格内边距 */
        .el-table .el-table__cell {
            padding: 2px 0; /* 原来是3px，压缩后为2px */
        }

        /* 压缩表格内文字大小 */
        .el-table .cell {
            line-height: 1; /* 原来是1.1，压缩后为1 */
            padding: 2px 6px; /* 原来是3px 6px，压缩后为2px 6px */
        }

        /* 调整按钮大小 */
        .el-table .el-button--small {
            padding: 5px 10px; /* 从4px 8px增加到5px 10px */
            font-size: 12px;
        }

        /* 操作列按钮样式优化 */
        .el-table .operation-buttons {
            display: flex;
            flex-direction: row;
            justify-content: space-around;
            align-items: center;
            white-space: nowrap;
            width: 100%;
        }
        
        .el-table .operation-buttons .icon-button {
            margin: 0 4px;
            padding: 4px;
            font-size: 16px;
            width: 28px;
            height: 28px;
            border-radius: 50%;
            display: flex;
            justify-content: center;
            align-items: center;
            cursor: pointer;
            transition: all 0.3s;
            color: white;
        }
        
        .el-table .operation-buttons .icon-button.edit {
            background-color: #409EFF;
        }
        
        .el-table .operation-buttons .icon-button.add {
            background-color: #67C23A;
        }
        
        .el-table .operation-buttons .icon-button.delete {
            background-color: #F56C6C;
        }
        
        .el-table .operation-buttons .icon-button:hover {
            transform: scale(1.1);
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
        }
        
        /* 确保操作列内容不换行 */
        .el-table .operation-column .cell {
            white-space: nowrap;
            overflow: visible;
            width: 100%;
            display: flex;
            justify-content: center;
            padding: 0 2px;
        }
        
        /* 调整操作列宽度 */
        .el-table .operation-column {
            width: 90px !important;
        }
        
        /* 调整备注列宽度 */
        .el-table .remarks-column .cell {
            max-width: 100px;
            white-space: normal;
            word-break: break-all;
            line-height: 1.2;
        }

        /* 隐藏Element Plus表格的所有水平滚动条 */
        .el-table .el-scrollbar__bar.is-horizontal,
        .el-table .el-scrollbar__wrap--hidden-default::-webkit-scrollbar,
        .el-table .el-scrollbar__thumb.is-horizontal,
        .el-table .el-scrollbar__view::-webkit-scrollbar {
            display: none !important;
            width: 0 !important;
            height: 0 !important;
            opacity: 0 !important;
            visibility: hidden !important;
        }
        
        /* 确保表格内容宽度与表格容器宽度一致 */
        .el-table__inner-wrapper,
        .el-table__body-wrapper,
        .el-table__header-wrapper {
            width: 100% !important;
        }

        .el-table .warning-row {
            background: #fdf6ec;
        }

        .el-table .success-row {
            background: #f0f9eb;
        }
        
        .el-table .primary-row {
            background: #ecf5ff;
        }
        
        .el-table .info-row {
            background: #e6f7ff;
        }

        .el-table .danger-row {
            background: #f3d4c8;
        }

        .dialog-footer {
            display: flex;
            justify-content: flex-end;
            margin-top: 20px;
        }

        .dialog-footer button {
            margin-left: 10px;
        }

        .steps-container {
            margin-bottom: 20px;
        }

        .form-container {
            margin-top: 20px;
        }

        .channel-selection {
            margin-bottom: 20px;
        }
        
        /* 独立的channel-selection样式 */
        .form-container > .channel-selection {
            padding: 15px;
            border: 1px solid #ebeef5;
            border-radius: 4px;
        }
        
        /* 渠道类型标题和选择框的样式 */
        .channel-type-header {
            display: flex;
            align-items: center;
            margin-bottom: 20px;
        }
        
        .channel-type-header h3 {
            margin: 0;
            margin-right: 20px;
            white-space: nowrap;
        }
        
        .channel-type-header .channel-selection {
            margin-bottom: 0;
            padding: 0;
            border: none;
            flex: 1;
        }

        .group-selection {
            margin-bottom: 20px;
        }

        .reception-settings {
            margin-bottom: 20px;
            padding: 15px;
            border: 1px solid #ebeef5;
            border-radius: 4px;
        }

        .rule-table-container {
            margin-top: 20px;
        }

        .empty-data {
            text-align: center;
            padding: 40px 0;
            color: #909399;
        }

        .empty-data i {
            font-size: 60px;
            margin-bottom: 10px;
        }

        .empty-data p {
            font-size: 16px;
        }

        .form-tip {
            font-size: 12px;
            color: #909399;
            margin-top: 5px;
        }

        .group-rules {
            margin-bottom: 30px;
        }
        
        .group-rules h4 {
            margin-top: 20px;
            margin-bottom: 10px;
            font-size: 16px;
            color: #303133;
        }
        
        .add-member-btn {
            margin-top: 10px;
            text-align: right;
        }

        .group-selection-container {
            display: flex;
            justify-content: space-between;
            margin-bottom: 20px;
        }

        .group-selection-row {
            display: flex;
            flex-wrap: wrap;
            width: 100%;
            justify-content: space-between;
        }
        
        .group-selection-row.single-channel {
            justify-content: center;
        }
        
        .group-selection-row.single-channel .group-selection-col {
            width: 100%;
            max-width: 600px;
        }

        .group-selection-col {
            width: 48%;
        }
        
        /* 当只有一个渠道被选中时，使分组选择区域占满整行 */
        .group-selection-row:has(.group-selection-col:only-child) .group-selection-col {
            width: 100%;
        }
        
        /* 兼容不支持:has选择器的浏览器 */
        @media screen {
            .group-selection-col:only-child {
                width: 100%;
            }
        }
        
        /* 添加新的样式 */
        .el-transfer {
            display: flex;
            align-items: flex-start;
        }
        
        .el-transfer__buttons {
            display: flex;
            flex-direction: column;
            justify-content: center;
            padding: 0 10px;
        }
        
        .el-transfer-panel {
            width: 45%;
        }
        
        .el-transfer-panel__body {
            height: 300px;
        }
        
        .channel-title {
            margin-bottom: 15px;
            font-weight: bold;
            font-size: 16px;
            color: #303133;
            display: flex;
            align-items: center;
        }
        
        .channel-title .el-icon {
            margin-right: 5px;
        }

        .alert-info {
            margin-bottom: 10px;
        }

        .alert-info i {
            margin-right: 5px;
        }
        
        /* 规则设置区域样式 */
        .rule-settings-section {
            margin-top: 20px;
            padding: 15px;
            border: 1px solid #e4e7ed;
            border-radius: 4px;
            background-color: #f5f7fa;
        }
        
        .section-title {
            margin-top: 0;
            margin-bottom: 15px;
            font-size: 16px;
            color: #303133;
        }
        
        .section-title i {
            margin-right: 5px;
        }
        
        .channel-reception {
            margin-bottom: 15px;
            padding: 10px;
            background-color: #fff;
            border-radius: 4px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
        }
        
        .reception-label {
            font-weight: bold;
            margin-right: 10px;
        }
        
        .group-rules {
            display: flex;
            flex-wrap: wrap;
            gap: 8px;
        }
        
        .group-rule-card {
            flex: 0 0 calc(33.333% - 10px);
            min-width: 160px;
            max-width: 240px;
            border: 1px solid #dcdfe6;
            border-radius: 4px;
            overflow: hidden;
            background-color: #fff;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
        }
        
        .group-rule-header {
            padding: 6px 10px;
            background-color: #f0f2f5;
            border-bottom: 1px solid #dcdfe6;
        }
        
        .group-name {
            font-weight: bold;
            color: #303133;
        }
        
        .group-rule-body {
            padding: 8px;
        }
        
        .rule-form-item {
            margin-bottom: 10px;
            display: flex;
            align-items: center;
            flex-wrap: nowrap;
            white-space: nowrap;
        }
        
        .rule-label {
            width: 70px;
            font-size: 13px;
            flex-shrink: 0;
            text-align: right;
            margin-right: 10px;
        }
        
        .rule-unit {
            margin-left: 5px;
        }
        
        /* 批量选择按钮样式 */
        .selection-actions {
            display: flex;
            justify-content: flex-end;
            margin-bottom: 8px;
            gap: 8px;
        }
        
        .selection-actions .el-button {
            padding: 4px 8px;
            font-size: 12px;
        }

        /* 规则设置容器样式 */
        .rules-container {
            margin-top: 20px;
        }
        
        .rules-row {
            display: flex;
            flex-wrap: wrap;
            gap: 20px;
            justify-content: space-between;
        }
        
        .rules-row.single-channel .rule-settings-section {
            width: 100%;
        }
        
        .rule-settings-section {
            flex: 1;
            min-width: 300px;
            padding: 15px;
            border: 1px solid #e4e7ed;
            border-radius: 4px;
            background-color: #f5f7fa;
        }
        
        /* 创建分发规则对话框样式 */
        .create-rule-dialog {
            position: fixed !important;
            width: 80% !important;
            top: calc(5vh - 120px) !important;
            left: 50% !important;
            transform: translateX(-50%) !important;
            margin-bottom: 5vh !important;
        }
        
        .create-rule-dialog .el-dialog {
            margin: 0 auto !important;
            width: 90% !important;
            max-width: 1400px !important;
        }
        
        .create-rule-dialog .el-dialog__body {
            max-height: 70vh;
            overflow-y: auto;
        }
        
        .create-rule-dialog .el-dialog__header,
        .create-rule-dialog .el-dialog__footer {
            position: sticky;
            background: white;
            z-index: 10;
        }
        
        .create-rule-dialog .el-dialog__header {
            top: 0;
            border-bottom: 1px solid #ebeef5;
            padding-bottom: 15px;
        }
        
        .create-rule-dialog .el-dialog__footer {
            bottom: 0;
            border-top: 1px solid #ebeef5;
            padding-top: 15px;
        }
        
        /* 分组规则滚动容器 */
        .group-rules-scroll-container {
            max-height: 480px; /* 从400px增加到480px */
            overflow-y: auto;
            border: 1px solid #e4e7ed;
            border-radius: 4px;
            padding: 10px;
            background-color: #fff;
            margin-bottom: 15px;
        }
        
        /* 滚动条美化 */
        .group-rules-scroll-container::-webkit-scrollbar {
            width: 6px;
        }
        
        .group-rules-scroll-container::-webkit-scrollbar-track {
            background: #f1f1f1;
            border-radius: 3px;
        }
        
        .group-rules-scroll-container::-webkit-scrollbar-thumb {
            background: #c0c4cc;
            border-radius: 3px;
        }
        
        .group-rules-scroll-container::-webkit-scrollbar-thumb:hover {
            background: #909399;
        }
        
        /* 分配总量状态颜色 */
        .distribution-total-status {
            padding: 2px 8px;
            border-radius: 4px;
            display: inline-block;
            min-width: 120px;
            text-align: center;
        }
        
        .distribution-total-status.warning {
            background-color: #fdf6ec;
            color: #e6a23c;
            border: 1px solid #f5dab1;
        }
        
        .distribution-total-status.success {
            background-color: #f0f9eb;
            color: #67c23a;
            border: 1px solid #c2e7b0;
        }
        
        .distribution-total-status.danger {
            background-color: #fef0f0;
            color: #f56c6c;
            border: 1px solid #fbc4c4;
        }

        /* 表格内容居中对齐 */
        .el-table th.el-table__cell {
            text-align: center !important;
            background-color: #f5f7fa !important; /* 确保背景色一致 */
        }

        .el-table td.el-table__cell {
            text-align: center !important;
        }

        .el-table .cell {
            text-align: center !important;
        }

        .channel-info {
            margin-top: 0;
            margin-bottom: 10px;
        }

        .channel-info .el-descriptions {
            margin-top: 0;
        }
        
        /* 接待数字段样式 */
        .expected-reception-item .el-descriptions__label,
        .expected-reception-item .el-descriptions__content {
            color: #409EFF !important;
            font-weight: bold;
            white-space: nowrap;
        }

        .actual-reception-item .el-descriptions__label,
        .actual-reception-item .el-descriptions__content {
            color: #67C23A !important;
            font-weight: bold;
            white-space: nowrap;
        }

        /* 调整描述列表的列数和宽度 */
        .reception-descriptions {
            margin-bottom: 15px;
            width: 100%;
        }
        
        .el-descriptions__body {
            width: 100%;
        }

        .el-descriptions__table {
            width: 100%;
            table-layout: fixed;
        }

        .el-descriptions__cell {
            padding: 8px 10px;
        }

        .el-descriptions__label {
            font-weight: bold;
            color: #606266;
            width: auto;
            text-align: center;
        }
        
        .el-descriptions__content {
            text-align: center;
            color: #303133;
        }
        
        /* 确保表格容器内的元素不超出容器宽度 */
        .channel-info {
            width: 100%;
            overflow-x: hidden;
        }
        
        /* 调整标签大小 */
        .el-table .el-tag {
            height: 20px;
            line-height: 20px;
            padding: 0 6px;
        }

        /* 固定表头 - 增强版 */
        .el-table--border th.el-table__cell.is-leaf,
        .el-table--border .el-table__header-wrapper .el-table__header thead tr th {
            position: sticky !important;
            top: 0 !important;
            z-index: 10 !important; /* 提高z-index确保在最上层 */
            background-color: #f5f7fa !important;
            color: #000000 !important; /* 改为黑色字体 */
            height: 40px !important; /* 增加行高 */
            font-weight: bold !important; /* 加粗字体 */
            border-bottom: 2px solid #dcdfe6 !important; /* 加粗底部边框 */
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1) !important; /* 添加阴影效果 */
        }

        /* 表头文字样式 */
        .el-table th.el-table__cell .cell {
            line-height: 1.5; /* 增加行高 */
            padding: 8px 6px; /* 增加内边距 */
            font-size: 14px; /* 增加字体大小 */
            text-align: center !important; /* 确保文字居中 */
            white-space: nowrap; /* 防止文字换行 */
        }

        /* 添加新的固定表头样式 */
        .sticky-header th {
            position: sticky !important;
            top: 0 !important;
            z-index: 100 !important;
            background-color: #f5f7fa !important;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1) !important;
        }

        /* 确保表头在滚动时不会被其他元素覆盖 */
        .el-table__header-wrapper {
            z-index: 100 !important;
            position: sticky !important;
            top: 0 !important;
        }

        /* 添加表格滚动提示样式 */
        .table-scroll-hint {
            text-align: center;
            color: #909399;
            margin: 5px 0;
            font-size: 12px;
            cursor: pointer;
        }

        /* 添加滚动条样式 */
        .table-container::-webkit-scrollbar {
            width: 8px;
            height: 8px;
        }

        .table-container::-webkit-scrollbar-thumb {
            background: #c0c4cc;
            border-radius: 4px;
        }

        .table-container::-webkit-scrollbar-track {
            background: #f5f7fa;
            border-radius: 4px;
        }

        .table-container::-webkit-scrollbar-corner {
            background: #f5f7fa;
        }

        /* 添加表格自适应样式 */
        .el-table__body,
        .el-table__header {
            width: 100% !important;
            table-layout: fixed !important;
        }

        /* 保持表头行不变 */
        .el-table .el-table__header .el-table__cell {
            padding: 8px 0; /* 保持表头行的原始内边距 */
        }

        .el-table .el-table__header .cell {
            line-height: 1.5; /* 保持表头行的原始行高 */
            padding: 8px 6px; /* 保持表头行的原始内边距 */
        }

        /* 预期接待数样式 */
        .reception-descriptions .expected-reception-item .el-descriptions__label,
        .reception-descriptions .expected-reception-item .el-descriptions__content {
            background-color: #f0f9eb !important;
            font-weight: bold;
            white-space: nowrap !important;
        }
        
        /* 当前接待数样式 */
        .reception-descriptions .current-reception-item .el-descriptions__label,
        .reception-descriptions .current-reception-item .el-descriptions__content {
            background-color: #e6f7ff !important;
            font-weight: bold;
            white-space: nowrap !important;
        }
        
        /* 实际接待数样式 */
        .reception-descriptions .actual-reception-item .el-descriptions__label,
        .reception-descriptions .actual-reception-item .el-descriptions__content {
            background-color: #fef0f0 !important;
            font-weight: bold;
            white-space: nowrap !important;
        }

        /* 接待数描述组件样式 */
        .reception-descriptions .el-descriptions__cell {
            padding: 3px 4px !important;
        }

        .reception-descriptions .el-descriptions__label {
            width: auto !important;
            min-width: 0 !important;
            white-space: nowrap !important;
            max-width: 95px !important;
            overflow: hidden !important;
            text-overflow: ellipsis !important;
        }

        .reception-descriptions .el-descriptions__content {
            width: auto !important;
            min-width: 0 !important;
            white-space: nowrap !important;
            max-width: 50px !important;
            overflow: hidden !important;
            text-overflow: ellipsis !important;
        }

        .reception-descriptions.el-descriptions {
            width: 100% !important;
            table-layout: fixed !important;
        }

        .channel-info {
            overflow-x: auto !important;
        }
        
        /* 确保所有描述内容居中 */
        .reception-descriptions .el-descriptions__cell {
            text-align: center !important;
        }
        
        /* 表格内容居中 */
        .el-table .cell {
            text-align: center !important;
        }
        
        /* 确保预计接待数等字段居中显示 */
        .expected-reception-item, .current-reception-item, .actual-reception-item {
            text-align: center !important;
        }
        
        /* 所有数值显示居中 */
        .el-descriptions-item__content span {
            text-align: center !important;
            display: inline-block;
            width: 100%;
        }
        
        /* 店铺选择对话框样式 */
        .store-selection-dialog .el-dialog__body {
            padding: 15px;
            overflow: hidden !important; /* 禁止对话框内容区域滚动 */
        }

        .store-selection-dialog .el-dialog {
            position: absolute !important;
            top: 50% !important;
            left: 50% !important;
            transform: translate(-50%, -50%) !important;
            margin: 0 !important;
            max-height: 80vh !important; /* 限制最大高度 */
            display: flex !important;
            flex-direction: column !important;
            z-index: 2001 !important; /* 确保显示在最上层 */
        }
        
        /* 对话框蒙层样式 */
        .store-selection-dialog .el-overlay {
            z-index: 2000 !important; /* 确保蒙层也有较高的z-index */
        }
        
        .store-selection-dialog .el-table {
            margin-top: 10px;
        }
        
        .store-selection-dialog .el-button {
            padding: 2px 8px;
            font-size: 12px;
            height: 24px;
            line-height: 1;
        }
        
        /* 店铺选择对话框新样式 */
        .shop-dialog-content {
            display: flex;
            flex-direction: column;
            gap: 15px;
            overflow: hidden !important; /* 禁止整体内容滚动 */
            height: 100% !important;
            flex: 1 !important;
        }
        
        .shop-search {
            margin-bottom: 10px;
        }
        
        /* 修正表格高度和滚动 */
        .shop-table-container {
            max-height: 50vh !important; /* 使用视口高度的50%作为最大高度 */
            overflow: auto;
            flex: 1 !important; /* 允许表格容器占用剩余空间 */
        }
        
        /* 确保表格内容按列显示 */
        .store-selection-dialog .el-table {
            margin-top: 10px;
            height: 100% !important; /* 确保表格占满容器高度 */
        }
        
        .store-selection-dialog .el-table .el-table__body {
            table-layout: fixed;
            width: 100%;
        }
        
        /* 确保表头正确显示 */
        .store-selection-dialog .el-table__header-wrapper {
            background-color: #f5f7fa;
        }
        
        /* 确保每列的宽度固定 */
        .store-selection-dialog .el-table .el-table__cell {
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }
        
        /* 对话框底部按钮样式 */
        .dialog-footer {
            display: flex;
            justify-content: flex-end;
            gap: 10px;
        }

        /* 表格内链接按钮样式 */
        .store-selection-dialog .el-button--link {
            margin-left: 0;
            padding: 0;
            height: auto;
            font-weight: normal;
        }

        .selected-store {
            background-color: #e6f7ff !important;
            color: #1890ff !important;
            font-weight: bold !important;
            position: relative !important;
        }

        .selected-store::after {
            content: "✓";
            position: absolute;
            right: -10px;
            top: 50%;
            transform: translateY(-50%);
            font-size: 12px;
            color: #52c41a;
        }

        .selected-list {
            display: flex;
            flex-wrap: wrap;
            gap: 8px;
            padding: 10px;
            background-color: #f5f7fa;
            border-radius: 4px;
            max-height: 120px;
            overflow-y: auto;
        }

        .store-tag {
            margin-right: 0;
            margin-bottom: 0;
            display: flex;
            align-items: center;
        }

        .selected-title {
            margin-bottom: 10px;
            font-weight: bold;
        }

        .selected-stores-preview {
            margin-top: 15px;
            border-top: 1px solid #ebeef5;
            padding-top: 15px;
        }

        .selected-stores-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 10px;
        }

        .selected-stores-header h4 {
          margin: 0;
        }

        .selected-stores-section {
            border-top: 1px solid #eee;
            padding-top: 10px;
            margin-top: 10px;
        }

        .selected-stores-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
            color: #606266;
            font-size: 14px;
        }

        .selected-stores-tags {
            display: flex;
            flex-wrap: wrap;
            gap: 8px;
            max-height: 80px;
            overflow-y: auto;
            padding: 5px 0;
        }

        .store-tag {
            margin-right: 5px;
            margin-bottom: 5px;
        }

        /* 批量修改相关样式 */
        .batch-operations {
            padding: 10px;
            background-color: #f5f7fa;
            border-radius: 4px;
            border: 1px solid #e4e7ed;
        }

        .batch-edit-table-container {
            margin-top: 20px;
        }

        .batch-actions {
            padding: 10px;
            background-color: #f8f9fa;
            border-radius: 4px;
            border: 1px solid #e9ecef;
        }

        .batch-edit-form .el-form-item {
            margin-bottom: 20px;
        }

        .batch-edit-form .el-checkbox {
            margin-right: 10px;
        }

        .quick-batch-form .el-form-item {
            margin-bottom: 18px;
        }

        .quick-batch-form .el-checkbox {
            margin-right: 10px;
        }

        /* 选中行高亮样式 */
        .el-table .el-table__row.selected-row {
            background-color: #e6f7ff !important;
        }

        .el-table .el-table__row.selected-row:hover {
            background-color: #bae7ff !important;
        }

        .batch-operations .el-button:last-child {
            margin-right: 0;
        }

        /* 批量修改对话框样式 */
        .create-rule-dialog .batch-edit-table-container {
            max-height: 500px;
            overflow-y: auto;
        }

        .create-rule-dialog .batch-edit-table-container .el-table {
            font-size: 13px;
        }

        .create-rule-dialog .batch-edit-table-container .el-table .el-table__cell {
            padding: 6px 0;
        }

        /* 批量修改排班对话框样式 */
        .quick-batch-form {
            padding: 10px 0;
        }

        .quick-batch-form .el-form-item__label {
            font-weight: 500;
        }

        .quick-batch-form .el-input-number {
            width: 150px;
        }

        .quick-batch-form .el-select {
            width: 180px;
        }

        /* Loading 动画样式 */
        #loading-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: #f5f7fa;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            z-index: 9999;
            transition: opacity 0.5s ease-out;
        }

        #loading-overlay.fade-out {
            opacity: 0;
            pointer-events: none;
        }

        .loading-spinner {
            width: 60px;
            height: 60px;
            border: 4px solid rgba(255, 255, 255, 0.3);
            border-top: 4px solid #409eff;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-bottom: 20px;
        }

        .loading-text {
            color: #606266;
            font-size: 18px;
            font-weight: 500;
            margin-bottom: 10px;
            text-align: center;
        }

        .loading-progress {
            color: #909399;
            font-size: 14px;
            text-align: center;
        }

        .loading-dots {
            display: inline-block;
            width: 20px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        @keyframes dots {
            0%, 20% { content: ''; }
            40% { content: '.'; }
            60% { content: '..'; }
            80%, 100% { content: '...'; }
        }

        .loading-dots::after {
            content: '';
            animation: dots 1.5s infinite;
        }
    </style>
</head>
<body>
    <!-- Loading 组件 -->
    <div id="loading-overlay">
        <div class="loading-spinner"></div>
        <div class="loading-text">正在加载分发规则管理</div>
        <div class="loading-progress">
            <span id="loading-status">正在加载静态资源</span>
            <span class="loading-dots"></span>
        </div>
    </div>

    <div class="container">
        <div class="header">
            <div class="page-title">
                <h1><i class="fas fa-random"></i> 销售排班</h1>
            </div>
            <a href="/home" class="back-button"><i class="fas fa-arrow-left back-icon"></i> 返回主页</a>
        </div>
        
        <div id="app">
            <div class="page-content">
                <div class="action-bar">
                    <div class="date-selector">
                        <el-date-picker
                            v-model="currentDate"
                            type="date"
                            placeholder="选择日期"
                            format="YYYY-MM-DD"
                            value-format="YYYY-MM-DD"
                            @change="handleDateChange">
                        </el-date-picker>
                        <el-button type="primary" @click="setToday">今天</el-button>
                    </div>
                    <div class="action-buttons">
                        <el-button type="primary" @click="showCreateDialog">创建分发规则</el-button>
                        <el-button type="success" @click="showCopyDialog" :disabled="!hasRules">复制规则</el-button>
                        <el-button type="danger" @click="deleteAllRules" :disabled="!hasRules">删除所有规则</el-button>
                    </div>
                </div>

                <!-- 这里将放置规则列表和创建规则的对话框 -->
                <div class="empty-data" v-if="loading">
                    <i class="el-icon-loading"></i>
                    <p>加载中...</p>
                </div>
                <div class="empty-data" v-else-if="!hasRules">
                    <i class="fas fa-inbox"></i>
                    <p>当前日期没有分发规则，请点击"创建分发规则"按钮创建</p>
                    <p v-if="apiError" style="color: #F56C6C; margin-top: 10px;">
                        注意：API请求失败，但您仍然可以创建规则。创建规则后，系统将自动创建必要的数据库表。
                    </p>
                </div>
                <div v-else class="rule-list">
                    <el-tabs v-model="activeTab">
                        <el-tab-pane label="电商渠道" name="ecommerce">
                            <div class="channel-info" v-if="hasEcommerceRules">
                                <el-descriptions
                                  class="reception-descriptions"
                                  :column="2"
                                  border
                                  size="small"
                                  :labelStyle="{minWidth: 'auto', width: 'auto', fontSize: '12px', padding: '3px', textAlign: 'center'}"
                                  :contentStyle="{minWidth: 'auto', width: 'auto', fontSize: '12px', padding: '3px', textAlign: 'center'}">
                                    <el-descriptions-item label="预计接待数" class="expected-reception-item">
                                        <div 
                                            style="position: relative; padding: 2px; border-radius: 4px; min-height: 20px; text-align: center;"
                                            @mouseenter="showEcommerceExpectedReceptionEditIcon = true"
                                            @mouseleave="showEcommerceExpectedReceptionEditIcon = false"
                                        >
                                            <template v-if="!isEditingEcommerceExpectedReception">
                                                <span style="text-align: center; display: inline-block; width: 100%;">{{ ecommerceReception?.expected_reception }}</span>
                                                <i
                                                    v-show="showEcommerceExpectedReceptionEditIcon"
                                                    class="el-icon-edit"
                                                    style="position: absolute; right: 5px; top: 50%; transform: translateY(-50%); cursor: pointer; color: #409EFF; font-size: 12px;"
                                                    @click="() => {
                                                        isEditingEcommerceExpectedReception = true;
                                                        tempEcommerceExpectedReception = ecommerceReception?.expected_reception;
                                                    }"
                                                >
                                                    <svg viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg" width="12" height="12">
                                                        <path fill="currentColor" d="M832 512a32 32 0 1 1 64 0v352a32 32 0 0 1-32 32H160a32 32 0 0 1-32-32V160a32 32 0 0 1 32-32h352a32 32 0 0 1 0 64H192v640h640V512z"></path>
                                                        <path fill="currentColor" d="m469.952 554.24 52.8-7.552L847.104 222.4a32 32 0 1 0-45.248-45.248L477.44 501.44l-7.552 52.8zm422.4-422.4a96 96 0 0 1 0 135.808l-331.84 331.84a32 32 0 0 1-18.112 9.088L436.8 623.68a32 32 0 0 1-36.224-36.224l15.104-105.6a32 32 0 0 1 9.024-18.112l331.904-331.84a96 96 0 0 1 135.744 0z"></path>
                                                    </svg>
                                                </i>
                                            </template>
                                            <template v-else>
                                                <div style="display: flex; align-items: center; width: 100%;">
                                                    <el-input-number
                                                        v-model="tempEcommerceExpectedReception"
                                                        :min="0"
                                                        :precision="0"
                                                        :controls="false"
                                                        size="small"
                                                        style="width: 80px;"
                                                        @keyup.enter="() => { 
                                                            updateChannelExpectedReception('ecommerce', tempEcommerceExpectedReception);
                                                            isEditingEcommerceExpectedReception = false;
                                                        }"
                                                    ></el-input-number>
                                                </div>
                                            </template>
                                        </div>
                                    </el-descriptions-item>
                                    <el-descriptions-item label="实际接待数" class="actual-reception-item" style="text-align: center;">{{ calculateActualTotalReception('ecommerce') || 0 }}</el-descriptions-item>
                                </el-descriptions>
                            </div>
                            <div class="table-container" v-if="hasEcommerceRules" id="ecommerce-table">
                                <!-- 批量操作按钮 -->
                                <div class="batch-operations" style="margin-bottom: 10px; display: flex; justify-content: space-between; align-items: center;">
                                    <div>
                                        <el-button size="small" @click="selectAllEcommerceRows">全选</el-button>
                                        <el-button size="small" @click="clearEcommerceSelection">清空选择</el-button>
                                        <span style="margin-left: 15px; color: #606266;">
                                            已选择 {{ selectedEcommerceRows.length }} 条记录
                                        </span>
                                    </div>
                                    <div>
                                        <el-button
                                            type="warning"
                                            size="small"
                                            @click="showQuickBatchEdit('ecommerce')"
                                            :disabled="selectedEcommerceRows.length === 0">
                                            批量修改排班
                                        </el-button>
                                        <el-button
                                            type="danger"
                                            size="small"
                                            @click="showBatchDeleteConfirm('ecommerce')"
                                            :disabled="selectedEcommerceRows.length === 0">
                                            批量删除
                                        </el-button>
                                        <el-button
                                            type="primary"
                                            size="small"
                                            @click="showBatchAppendEdit('ecommerce')"
                                            :disabled="selectedEcommerceRows.length === 0">
                                            批量追加
                                        </el-button>
                                    </div>
                                </div>

                                <el-table
                                    :data="ecommerceRules"
                                    border
                                    style="width: 100%"
                                    :row-class-name="tableRowClassName"
                                    :span-method="leaderSpanMethod"
                                    header-row-class-name="sticky-header"
                                    :header-cell-style="{background:'#f5f7fa',color:'#000000',fontWeight:'bold'}"
                                    :show-overflow-tooltip="true"
                                    :scrollbar-always-on="false"
                                    @selection-change="handleEcommerceSelectionChange"
                                    ref="ecommerceTable">
                                    <el-table-column type="selection" width="55"></el-table-column>
                                    <el-table-column type="index" label="序号" width="45"></el-table-column>
                                    <el-table-column prop="group_name" label="分组" width="100">
                                        <template #default="scope">
                                            <div style="display: flex; flex-direction: column; padding: 5px;">
                                                <div style="font-weight: bold; margin-bottom: 4px;">{{ scope.row.group_name }}</div>
                                                <div style="font-size: 12px; color: #606266; margin-bottom: 2px;">{{ scope.row.leader }}</div>
                                                <div style="font-size: 12px; color: #909399;" v-if="isFirstInGroup(scope.row, scope.$index, 'ecommerce')">
                                                    在岗: {{ getGroupMemberCount(scope.row.group_name, 'ecommerce') }} 人
                                                </div>
                                            </div>
                                        </template>
                                    </el-table-column>
                                    <el-table-column label="分配数量" width="85">
                                        <template #default="scope">
                                            <div style="display: flex; flex-direction: column;">
                                                <div
                                                    class="distribution-setting-row"
                                                    style="position: relative; padding: 5px; background-color: #f5f7fa; border-radius: 4px; margin-bottom: 2px; min-height: 24px;"
                                                    @mouseenter="scope.row.showEditIcon = true"
                                                    @mouseleave="scope.row.showEditIcon = false"
                                                >
                                                    <template v-if="!scope.row.isEditing">
                                                        <span>{{ getGroupSettingDistributionRatio(scope.row.group_name, 'ecommerce') }}</span>
                                                        <i
                                                            v-show="scope.row.showEditIcon"
                                                            class="el-icon-edit"
                                                            style="position: absolute; right: 5px; top: 50%; transform: translateY(-50%); cursor: pointer; color: #409EFF; font-size: 14px;"
                                                            @click="() => {
                                                                scope.row.isEditing = true;
                                                                scope.row.tempDistributionRatio = getGroupSettingDistributionRatio(scope.row.group_name, 'ecommerce');
                                                            }"
                                                        >
                                                            <svg viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg" width="14" height="14">
                                                                <path fill="currentColor" d="M832 512a32 32 0 1 1 64 0v352a32 32 0 0 1-32 32H160a32 32 0 0 1-32-32V160a32 32 0 0 1 32-32h352a32 32 0 0 1 0 64H192v640h640V512z"></path>
                                                                <path fill="currentColor" d="m469.952 554.24 52.8-7.552L847.104 222.4a32 32 0 1 0-45.248-45.248L477.44 501.44l-7.552 52.8zm422.4-422.4a96 96 0 0 1 0 135.808l-331.84 331.84a32 32 0 0 1-18.112 9.088L436.8 623.68a32 32 0 0 1-36.224-36.224l15.104-105.6a32 32 0 0 1 9.024-18.112l331.904-331.84a96 96 0 0 1 135.744 0z"></path>
                                                            </svg>
                                                        </i>
                                                    </template>
                                                    <template v-else>
                                                        <div style="display: flex; align-items: center; width: 100%;">
                                                            <el-input-number
                                                                v-model="scope.row.tempDistributionRatio"
                                                                :min="0"
                                                                :precision="0"
                                                                :controls="false"
                                                                size="small"
                                                                style="width: 50px;"
                                                                @keyup.enter="() => {
                                                                    updateGroupSettingDistributionRatio(scope.row.group_name, 'ecommerce', scope.row.tempDistributionRatio);
                                                                    scope.row.isEditing = false;
                                                                }"
                                                            ></el-input-number>
                                                            <div style="margin-left: 5px;">
                                                                <i
                                                                    class="el-icon-check"
                                                                    style="cursor: pointer; color: #67C23A; margin-right: 3px;"
                                                                    @click="() => {
                                                                        updateGroupSettingDistributionRatio(scope.row.group_name, 'ecommerce', scope.row.tempDistributionRatio);
                                                                        scope.row.isEditing = false;
                                                                    }"
                                                                >
                                                                    <svg viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg" width="14" height="14">
                                                                        <path fill="currentColor" d="M406.656 706.944 195.84 496.256a32 32 0 1 0-45.248 45.248l256 256 512-512a32 32 0 0 0-45.248-45.248L406.592 706.944z"></path>
                                                                    </svg>
                                                                </i>
                                                                <i
                                                                    class="el-icon-close"
                                                                    style="cursor: pointer; color: #F56C6C;"
                                                                    @click="scope.row.isEditing = false"
                                                                >
                                                                    <svg viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg" width="14" height="14">
                                                                        <path fill="currentColor" d="M764.288 214.592 512 466.88 259.712 214.592a31.936 31.936 0 0 0-45.12 45.12L466.752 512 214.528 764.224a31.936 31.936 0 1 0 45.12 45.184L512 557.184l252.288 252.288a31.936 31.936 0 0 0 45.12-45.12L557.12 512.064l252.288-252.352a31.936 31.936 0 1 0-45.12-45.184z"></path>
                                                                    </svg>
                                                                </i>
                                                            </div>
                                                        </div>
                                                    </template>
                                                </div>
                                                <div
                                                    style="padding: 5px; background-color: #e6f7ff; border-radius: 4px; margin-bottom: 2px; min-height: 24px; text-align: center; font-size: 12px;"
                                                >
                                                    <span title="分组内预计接待数总和">预计: {{ getGroupExpectedTotalReception(scope.row.group_name, 'ecommerce') }}</span>
                                                </div>
                                                <div
                                                    style="padding: 5px; background-color: #f0f9eb; border-radius: 4px; min-height: 24px; text-align: center; font-size: 12px;"
                                                >
                                                    <span title="分组内实际接待数总和">实际: {{ getGroupActualTotalReception(scope.row.group_name, 'ecommerce') }}</span>
                                                </div>
                                            </div>
                                        </template>
                                    </el-table-column>
                                    <el-table-column prop="member" label="人员" width="100">
                                        <template #default="scope">
                                            <div 
                                                style="position: relative; padding: 5px; border-radius: 4px; min-height: 24px;"
                                                @mouseenter="scope.row.showMemberEditIcon = true"
                                                @mouseleave="scope.row.showMemberEditIcon = false"
                                            >
                                                <template v-if="!scope.row.isMemberEditing">
                                                    <span>{{ scope.row.member }}</span>
                                                    <i 
                                                        v-show="scope.row.showMemberEditIcon"
                                                        class="el-icon-edit"
                                                        style="position: absolute; right: 5px; top: 50%; transform: translateY(-50%); cursor: pointer; color: #409EFF; font-size: 14px;"
                                                        @click="() => { 
                                                            scope.row.isMemberEditing = true; 
                                                            scope.row.tempMember = scope.row.member;
                                                        }"
                                                    >
                                                        <svg viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg" width="14" height="14">
                                                            <path fill="currentColor" d="M832 512a32 32 0 1 1 64 0v352a32 32 0 0 1-32 32H160a32 32 0 0 1-32-32V160a32 32 0 0 1 32-32h352a32 32 0 0 1 0 64H192v640h640V512z"></path>
                                                            <path fill="currentColor" d="m469.952 554.24 52.8-7.552L847.104 222.4a32 32 0 1 0-45.248-45.248L477.44 501.44l-7.552 52.8zm422.4-422.4a96 96 0 0 1 0 135.808l-331.84 331.84a32 32 0 0 1-18.112 9.088L436.8 623.68a32 32 0 0 1-36.224-36.224l15.104-105.6a32 32 0 0 1 9.024-18.112l331.904-331.84a96 96 0 0 1 135.744 0z"></path>
                                                        </svg>
                                                    </i>
                                                </template>
                                                <template v-else>
                                                    <div style="display: flex; align-items: center; width: 100%;">
                                                        <el-select
                                                            v-model="scope.row.tempMember"
                                                            size="small"
                                                            style="width: 100%;"
                                                            filterable
                                                            placeholder="选择人员"
                                                            @change="() => { 
                                                                updateRuleMember(scope.row.id, scope.row.tempMember);
                                                                scope.row.isMemberEditing = false;
                                                            }"
                                                            @keyup.enter="() => {
                                                                updateRuleMember(scope.row.id, scope.row.tempMember);
                                                                scope.row.isMemberEditing = false;
                                                            }"
                                                            :loading="scope.row.loadingMembers"
                                                        >
                                                            <el-option
                                                                v-for="member in getGroupMembers(scope.row.group_name, scope.row.channel_type === '电商渠道' ? 'ecommerce' : 'newmedia')"
                                                                :key="member.name"
                                                                :label="member.name"
                                                                :value="member.name"
                                                            ></el-option>
                                                        </el-select>
                                                    </div>
                                                </template>
                                            </div>
                                        </template>
                                    </el-table-column>
                                    <el-table-column prop="shift" label="班次" width="70">
                                        <template #default="scope">
                                            <div 
                                                style="position: relative; padding: 5px; border-radius: 4px; min-height: 24px;"
                                                @mouseenter="scope.row.showShiftEditIcon = true"
                                                @mouseleave="scope.row.showShiftEditIcon = false"
                                            >
                                                <template v-if="!scope.row.isShiftEditing">
                                            <el-tag v-if="scope.row.shift === '白班'" type="success">白班</el-tag>
                                            <el-tag v-else-if="scope.row.shift === '午班'" type="primary">午班</el-tag>
                                            <el-tag v-else-if="scope.row.shift === '晚班'" type="warning">晚班</el-tag>
                                                    <el-tag v-else-if="scope.row.shift === '全天'" type="danger">全天</el-tag>
                                                    <el-tag v-else type="info">无</el-tag>
                                                    <i 
                                                        v-show="scope.row.showShiftEditIcon"
                                                        class="el-icon-edit"
                                                        style="position: absolute; right: 5px; top: 50%; transform: translateY(-50%); cursor: pointer; color: #409EFF; font-size: 14px;"
                                                        @click="() => { 
                                                            scope.row.isShiftEditing = true; 
                                                            scope.row.tempShift = scope.row.shift;
                                                        }"
                                                    >
                                                        <svg viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg" width="14" height="14">
                                                            <path fill="currentColor" d="M832 512a32 32 0 1 1 64 0v352a32 32 0 0 1-32 32H160a32 32 0 0 1-32-32V160a32 32 0 0 1 32-32h352a32 32 0 0 1 0 64H192v640h640V512z"></path>
                                                            <path fill="currentColor" d="m469.952 554.24 52.8-7.552L847.104 222.4a32 32 0 1 0-45.248-45.248L477.44 501.44l-7.552 52.8zm422.4-422.4a96 96 0 0 1 0 135.808l-331.84 331.84a32 32 0 0 1-18.112 9.088L436.8 623.68a32 32 0 0 1-36.224-36.224l15.104-105.6a32 32 0 0 1 9.024-18.112l331.904-331.84a96 96 0 0 1 135.744 0z"></path>
                                                        </svg>
                                                    </i>
                                                </template>
                                                <template v-else>
                                                    <div style="display: flex; align-items: center; width: 100%;">
                                                        <el-select
                                                            v-model="scope.row.tempShift"
                                                            size="small"
                                                            style="width: 100%;"
                                                            @change="() => { 
                                                                updateRuleShift(scope.row.id, scope.row.tempShift);
                                                                scope.row.isShiftEditing = false;
                                                            }"
                                                            @keyup.enter="() => {
                                                                updateRuleShift(scope.row.id, scope.row.tempShift);
                                                                scope.row.isShiftEditing = false;
                                                            }"
                                                        >
                                                            <el-option
                                                                v-for="shift in ['无', '白班','午班', '晚班', '全天']"
                                                                :key="shift"
                                                                :label="shift"
                                                                :value="shift"
                                                            ></el-option>
                                                        </el-select>
                                                    </div>
                                                </template>
                                            </div>
                                        </template>
                                    </el-table-column>
                                    <el-table-column prop="expected_total" label="预计接待数" width="90" align="center">
                                        <template #default="scope">
                                            <div 
                                                style="position: relative; padding: 5px; border-radius: 4px; min-height: 24px;"
                                                @mouseenter="scope.row.showExpectedReceptionEditIcon = true"
                                                @mouseleave="scope.row.showExpectedReceptionEditIcon = false"
                                            >
                                                <template v-if="!scope.row.isExpectedReceptionEditing">
                                                    <span>{{ scope.row?.expected_total }}</span>
                                                    <i
                                                        v-show="scope.row.showExpectedReceptionEditIcon"
                                                        class="el-icon-edit"
                                                        style="position: absolute; right: 5px; top: 50%; transform: translateY(-50%); cursor: pointer; color: #409EFF; font-size: 14px;"
                                                        @click="() => {
                                                            scope.row.isExpectedReceptionEditing = true;
                                                            scope.row.tempExpectedReception = scope.row?.expected_total;
                                                        }"
                                                    >
                                                        <svg viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg" width="14" height="14">
                                                            <path fill="currentColor" d="M832 512a32 32 0 1 1 64 0v352a32 32 0 0 1-32 32H160a32 32 0 0 1-32-32V160a32 32 0 0 1 32-32h352a32 32 0 0 1 0 64H192v640h640V512z"></path>
                                                            <path fill="currentColor" d="m469.952 554.24 52.8-7.552L847.104 222.4a32 32 0 1 0-45.248-45.248L477.44 501.44l-7.552 52.8zm422.4-422.4a96 96 0 0 1 0 135.808l-331.84 331.84a32 32 0 0 1-18.112 9.088L436.8 623.68a32 32 0 0 1-36.224-36.224l15.104-105.6a32 32 0 0 1 9.024-18.112l331.904-331.84a96 96 0 0 1 135.744 0z"></path>
                                                        </svg>
                                                    </i>
                                                </template>
                                                <template v-else>
                                                    <div style="display: flex; align-items: center; width: 100%;">
                                                        <el-input-number
                                                            v-model="scope.row.tempExpectedReception"
                                                            :min="0"
                                                            :precision="0"
                                                            :controls="false"
                                                            size="small"
                                                            style="width: 50px;"
                                                            @keyup.enter="() => { 
                                                                updateRuleExpectedReception(scope.row.id, scope.row.tempExpectedReception);
                                                                scope.row.isExpectedReceptionEditing = false;
                                                            }"
                                                        ></el-input-number>
                                                        <div style="margin-left: 5px;">
                                                            <i 
                                                                class="el-icon-check" 
                                                                style="cursor: pointer; color: #67C23A; margin-right: 3px;"
                                                                @click="() => { 
                                                                    updateRuleExpectedReception(scope.row.id, scope.row.tempExpectedReception);
                                                                    scope.row.isExpectedReceptionEditing = false;
                                                                }"
                                                            >
                                                                <svg viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg" width="14" height="14">
                                                                    <path fill="currentColor" d="M406.656 706.944 195.84 496.256a32 32 0 1 0-45.248 45.248l256 256 512-512a32 32 0 0 0-45.248-45.248L406.592 706.944z"></path>
                                                                </svg>
                                                            </i>
                                                            <i 
                                                                class="el-icon-close" 
                                                                style="cursor: pointer; color: #F56C6C;"
                                                                @click="scope.row.isExpectedReceptionEditing = false"
                                                            >
                                                                <svg viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg" width="14" height="14">
                                                                    <path fill="currentColor" d="M764.288 214.592 512 466.88 259.712 214.592a31.936 31.936 0 0 0-45.12 45.12L466.752 512 214.528 764.224a31.936 31.936 0 1 0 45.12 45.184L512 557.184l252.288 252.288a31.936 31.936 0 0 0 45.12-45.12L557.12 512.064l252.288-252.352a31.936 31.936 0 1 0-45.12-45.184z"></path>
                                                                </svg>
                                                            </i>
                                                        </div>
                                                    </div>
                                                </template>
                                            </div>
                                        </template>
                                    </el-table-column>
                                    <el-table-column prop="actual_reception" label="实际接待数" width="85" align="center">
                                        <template #default="scope">
                                            <div style="padding: 5px; border-radius: 4px; min-height: 24px; text-align: center;">
                                                {{ (scope.row.actual_free_reception || scope.row.actual_free || 0) + (scope.row.actual_paid_reception || scope.row.actual_paid || 0) }}
                                            </div>
                                        </template>
                                    </el-table-column>
                                    <el-table-column prop="store" label="店铺" width="180">
                                        <template #default="scope">
                                            <div style="display: flex; justify-content: space-between; align-items: center;">
                                                <span>{{ scope.row.store }}</span>
                                                <el-button 
                                                    size="small" 
                                                    type="primary" 
                                                    style="padding: 2px 5px; font-size: 12px; margin-left: 5px; height: 22px; line-height: 1;"
                                                    @click="showStoreSelection(scope.row)">
                                                    选择
                                                </el-button>
                                            </div>
                                        </template>
                                    </el-table-column>
                                    <el-table-column label="时间段" width="110">
                                        <template #default="scope">
                                            {{ formatTime(scope.row.time_range_start) }} - {{ formatTime(scope.row.time_range_end) }}
                                        </template>
                                    </el-table-column>
                                    
                                    <el-table-column label="操作" width="65" class-name="operation-column">
                                        <template #default="scope">
                                            <div class="operation-buttons">
                                                <div class="icon-button add" @click="showAddRowDialog(scope.row)">
                                                    <i class="fas fa-plus"></i>
                                                </div>
                                                <div class="icon-button delete" @click="deleteRule(scope.row.id)">
                                                    <i class="fas fa-trash-alt"></i>
                                                </div>
                                            </div>
                                        </template>
                                    </el-table-column>
                                </el-table>
                            </div>
                            <div class="empty-data" v-else>
                                <i class="fas fa-inbox"></i>
                                <p>当前日期没有电商渠道分发规则，请点击"创建分发规则"按钮创建</p>
                            </div>
                        </el-tab-pane>
                        <el-tab-pane label="新媒体渠道" name="newmedia">
                            <div class="channel-info" v-if="hasNewMediaRules">
                                <el-descriptions
                                  class="reception-descriptions"
                                  :column="6"
                                  border
                                  size="small"
                                  :labelStyle="{minWidth: 'auto', width: 'auto', fontSize: '12px', padding: '3px', textAlign: 'center'}"
                                  :contentStyle="{minWidth: 'auto', width: 'auto', fontSize: '12px', padding: '3px', textAlign: 'center'}">
                                    <el-descriptions-item label="预计接待数" class="expected-reception-item">
                                        <div 
                                            style="position: relative; padding: 2px; border-radius: 4px; min-height: 20px; text-align: center;"
                                            @mouseenter="showNewMediaExpectedReceptionEditIcon = true"
                                            @mouseleave="showNewMediaExpectedReceptionEditIcon = false"
                                        >
                                            <template v-if="!isEditingNewMediaExpectedReception">
                                                <span style="text-align: center; display: inline-block; width: 100%;">{{ newMediaReception?.expected_reception }}</span>
                                                <i
                                                    v-show="showNewMediaExpectedReceptionEditIcon"
                                                    class="el-icon-edit"
                                                    style="position: absolute; right: 5px; top: 50%; transform: translateY(-50%); cursor: pointer; color: #409EFF; font-size: 12px;"
                                                    @click="() => {
                                                        isEditingNewMediaExpectedReception = true;
                                                        tempNewMediaExpectedReception = newMediaReception?.expected_reception;
                                                    }"
                                                >
                                                    <svg viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg" width="12" height="12">
                                                        <path fill="currentColor" d="M832 512a32 32 0 1 1 64 0v352a32 32 0 0 1-32 32H160a32 32 0 0 1-32-32V160a32 32 0 0 1 32-32h352a32 32 0 0 1 0 64H192v640h640V512z"></path>
                                                        <path fill="currentColor" d="m469.952 554.24 52.8-7.552L847.104 222.4a32 32 0 1 0-45.248-45.248L477.44 501.44l-7.552 52.8zm422.4-422.4a96 96 0 0 1 0 135.808l-331.84 331.84a32 32 0 0 1-18.112 9.088L436.8 623.68a32 32 0 0 1-36.224-36.224l15.104-105.6a32 32 0 0 1 9.024-18.112l331.904-331.84a96 96 0 0 1 135.744 0z"></path>
                                                    </svg>
                                                </i>
                                            </template>
                                            <template v-else>
                                                <div style="display: flex; align-items: center; width: 100%;">
                                                    <el-input-number
                                                        v-model="tempNewMediaExpectedReception"
                                                        :min="0"
                                                        :precision="0"
                                                        :controls="false"
                                                        size="small"
                                                        style="width: 80px;"
                                                        @keyup.enter="() => { 
                                                            updateChannelExpectedReception('newmedia', tempNewMediaExpectedReception);
                                                            isEditingNewMediaExpectedReception = false;
                                                        }"
                                                    ></el-input-number>
                                                </div>
                                            </template>
                                        </div>
                                    </el-descriptions-item>
                                    <el-descriptions-item label="预计免费接待数" class="expected-reception-item">
                                        <div 
                                            style="position: relative; padding: 2px; border-radius: 4px; min-height: 20px;"
                                            @mouseenter="showNewMediaExpectedFreeReceptionEditIcon = true"
                                            @mouseleave="showNewMediaExpectedFreeReceptionEditIcon = false"
                                        >
                                            <template v-if="!isEditingNewMediaExpectedFreeReception">
                                                <span style="text-align: center; display: inline-block; width: 100%;">{{ newMediaReception.expected_free_reception || 0 }}</span>
                                                <i 
                                                    v-show="showNewMediaExpectedFreeReceptionEditIcon"
                                                    class="el-icon-edit"
                                                    style="position: absolute; right: 5px; top: 50%; transform: translateY(-50%); cursor: pointer; color: #409EFF; font-size: 12px;"
                                                    @click="() => { 
                                                        isEditingNewMediaExpectedFreeReception = true; 
                                                        tempNewMediaExpectedFreeReception = newMediaReception.expected_free_reception || 0;
                                                    }"
                                                >
                                                    <svg viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg" width="12" height="12">
                                                        <path fill="currentColor" d="M832 512a32 32 0 1 1 64 0v352a32 32 0 0 1-32 32H160a32 32 0 0 1-32-32V160a32 32 0 0 1 32-32h352a32 32 0 0 1 0 64H192v640h640V512z"></path>
                                                        <path fill="currentColor" d="m469.952 554.24 52.8-7.552L847.104 222.4a32 32 0 1 0-45.248-45.248L477.44 501.44l-7.552 52.8zm422.4-422.4a96 96 0 0 1 0 135.808l-331.84 331.84a32 32 0 0 1-18.112 9.088L436.8 623.68a32 32 0 0 1-36.224-36.224l15.104-105.6a32 32 0 0 1 9.024-18.112l331.904-331.84a96 96 0 0 1 135.744 0z"></path>
                                                    </svg>
                                                </i>
                                            </template>
                                            <template v-else>
                                                <div style="display: flex; align-items: center; width: 100%;">
                                                    <el-input-number
                                                        v-model="tempNewMediaExpectedFreeReception"
                                                        :min="0"
                                                        :precision="0"
                                                        :controls="false"
                                                        size="small"
                                                        style="width: 80px;"
                                                        @keyup.enter="() => { 
                                                            updateChannelExpectedFreeReception('newmedia', tempNewMediaExpectedFreeReception);
                                                            isEditingNewMediaExpectedFreeReception = false;
                                                        }"
                                                    ></el-input-number>
                                                </div>
                                            </template>
                                        </div>
                                    </el-descriptions-item>
                                    <el-descriptions-item label="预计付费接待数" class="expected-reception-item">
                                        <div 
                                            style="position: relative; padding: 2px; border-radius: 4px; min-height: 20px;"
                                            @mouseenter="showNewMediaExpectedPaidReceptionEditIcon = true"
                                            @mouseleave="showNewMediaExpectedPaidReceptionEditIcon = false"
                                        >
                                            <template v-if="!isEditingNewMediaExpectedPaidReception">
                                                <span style="text-align: center; display: inline-block; width: 100%;">{{ newMediaReception.expected_paid_reception || 0 }}</span>
                                                <i 
                                                    v-show="showNewMediaExpectedPaidReceptionEditIcon"
                                                    class="el-icon-edit"
                                                    style="position: absolute; right: 5px; top: 50%; transform: translateY(-50%); cursor: pointer; color: #409EFF; font-size: 12px;"
                                                    @click="() => { 
                                                        isEditingNewMediaExpectedPaidReception = true; 
                                                        tempNewMediaExpectedPaidReception = newMediaReception.expected_paid_reception || 0;
                                                    }"
                                                >
                                                    <svg viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg" width="12" height="12">
                                                        <path fill="currentColor" d="M832 512a32 32 0 1 1 64 0v352a32 32 0 0 1-32 32H160a32 32 0 0 1-32-32V160a32 32 0 0 1 32-32h352a32 32 0 0 1 0 64H192v640h640V512z"></path>
                                                        <path fill="currentColor" d="m469.952 554.24 52.8-7.552L847.104 222.4a32 32 0 1 0-45.248-45.248L477.44 501.44l-7.552 52.8zm422.4-422.4a96 96 0 0 1 0 135.808l-331.84 331.84a32 32 0 0 1-18.112 9.088L436.8 623.68a32 32 0 0 1-36.224-36.224l15.104-105.6a32 32 0 0 1 9.024-18.112l331.904-331.84a96 96 0 0 1 135.744 0z"></path>
                                                    </svg>
                                                </i>
                                            </template>
                                            <template v-else>
                                                <div style="display: flex; align-items: center; width: 100%;">
                                                    <el-input-number
                                                        v-model="tempNewMediaExpectedPaidReception"
                                                        :min="0"
                                                        :precision="0"
                                                        :controls="false"
                                                        size="small"
                                                        style="width: 80px;"
                                                        @keyup.enter="() => { 
                                                            updateChannelExpectedPaidReception('newmedia', tempNewMediaExpectedPaidReception);
                                                            isEditingNewMediaExpectedPaidReception = false;
                                                        }"
                                                    ></el-input-number>
                                                </div>
                                            </template>
                                        </div>
                                    </el-descriptions-item>
                                    <el-descriptions-item label="实际接待数" class="actual-reception-item" style="text-align: center;">{{ calculateActualTotalReception('newmedia') || 0 }}</el-descriptions-item>
                                    <el-descriptions-item label="实际免费接待数" class="actual-reception-item" style="text-align: center;">{{ calculateActualFreeReception('newmedia') || 0 }}</el-descriptions-item>
                                    <el-descriptions-item label="实际付费接待数" class="actual-reception-item" style="text-align: center;">{{ calculateActualPaidReception('newmedia') || 0 }}</el-descriptions-item>
                                </el-descriptions>
                            </div>
                            <div class="table-container" v-if="hasNewMediaRules" id="newmedia-table">
                                <!-- 批量操作按钮 -->
                                <div class="batch-operations" style="margin-bottom: 10px; display: flex; justify-content: space-between; align-items: center;">
                                    <div>
                                        <el-button size="small" @click="selectAllNewMediaRows">全选</el-button>
                                        <el-button size="small" @click="clearNewMediaSelection">清空选择</el-button>
                                        <span style="margin-left: 15px; color: #606266;">
                                            已选择 {{ selectedNewMediaRows.length }} 条记录
                                        </span>
                                    </div>
                                    <div style="display: flex; gap: 8px;">
                                        <el-button
                                            type="warning"
                                            size="small"
                                            @click="showQuickBatchEdit('newmedia')"
                                            :disabled="selectedNewMediaRows.length === 0">
                                            批量修改排班
                                        </el-button>
                                        <el-button
                                            type="danger"
                                            size="small"
                                            @click="showBatchDeleteConfirm('newmedia')"
                                            :disabled="selectedNewMediaRows.length === 0">
                                            批量删除
                                        </el-button>
                                        <el-button
                                            type="primary"
                                            size="small"
                                            @click="showBatchAppendEdit('newmedia')"
                                            :disabled="selectedNewMediaRows.length === 0">
                                            批量追加
                                        </el-button>
                                    </div>
                                </div>

                                <el-table
                                    :data="newMediaRules"
                                    border
                                    style="width: 100%"
                                    :row-class-name="tableRowClassName"
                                    :span-method="leaderSpanMethod"
                                    header-row-class-name="sticky-header"
                                    :header-cell-style="{background:'#f5f7fa',color:'#000000',fontWeight:'bold'}"
                                    :show-overflow-tooltip="true"
                                    :scrollbar-always-on="false"
                                    @selection-change="handleNewMediaSelectionChange"
                                    ref="newMediaTable">
                                    <el-table-column type="selection" width="55"></el-table-column>
                                    <el-table-column type="index" label="序号" width="45"></el-table-column>
                                    <el-table-column prop="group_name" label="分组" width="100">
                                        <template #default="scope">
                                            <div style="display: flex; flex-direction: column; padding: 5px;">
                                                <div style="font-weight: bold; margin-bottom: 4px;">{{ scope.row.group_name }}</div>
                                                <div style="font-size: 12px; color: #606266; margin-bottom: 2px;">{{ scope.row.leader }}</div>
                                                <div style="font-size: 12px; color: #909399;" v-if="isFirstInGroup(scope.row, scope.$index, 'newmedia')">
                                                    在岗: {{ getGroupMemberCount(scope.row.group_name, 'newmedia') }} 人
                                                </div>
                                            </div>
                                        </template>
                                    </el-table-column>
                                    <el-table-column label="分配数量" width="85">
                                        <template #default="scope">
                                            <div style="display: flex; flex-direction: column;">
                                                <div
                                                    class="distribution-setting-row"
                                                    style="position: relative; padding: 5px; background-color: #f5f7fa; border-radius: 4px; margin-bottom: 2px; min-height: 24px;"
                                                    @mouseenter="scope.row.showEditIcon = true"
                                                    @mouseleave="scope.row.showEditIcon = false"
                                                >
                                                    <template v-if="!scope.row.isEditing">
                                                        <span>{{ getGroupSettingDistributionRatio(scope.row.group_name, 'newmedia') }}</span>
                                                        <i
                                                            v-show="scope.row.showEditIcon"
                                                            class="el-icon-edit"
                                                            style="position: absolute; right: 5px; top: 50%; transform: translateY(-50%); cursor: pointer; color: #409EFF; font-size: 14px;"
                                                            @click="() => {
                                                                scope.row.isEditing = true;
                                                                scope.row.tempDistributionRatio = getGroupSettingDistributionRatio(scope.row.group_name, 'newmedia');
                                                            }"
                                                        >
                                                            <svg viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg" width="14" height="14">
                                                                <path fill="currentColor" d="M832 512a32 32 0 1 1 64 0v352a32 32 0 0 1-32 32H160a32 32 0 0 1-32-32V160a32 32 0 0 1 32-32h352a32 32 0 0 1 0 64H192v640h640V512z"></path>
                                                                <path fill="currentColor" d="m469.952 554.24 52.8-7.552L847.104 222.4a32 32 0 1 0-45.248-45.248L477.44 501.44l-7.552 52.8zm422.4-422.4a96 96 0 0 1 0 135.808l-331.84 331.84a32 32 0 0 1-18.112 9.088L436.8 623.68a32 32 0 0 1-36.224-36.224l15.104-105.6a32 32 0 0 1 9.024-18.112l331.904-331.84a96 96 0 0 1 135.744 0z"></path>
                                                            </svg>
                                                        </i>
                                                    </template>
                                                    <template v-else>
                                                        <div style="display: flex; align-items: center; width: 100%;">
                                                            <el-input-number
                                                                v-model="scope.row.tempDistributionRatio"
                                                                :min="0"
                                                                :precision="0"
                                                                :controls="false"
                                                                size="small"
                                                                style="width: 50px;"
                                                                @keyup.enter="() => {
                                                                    updateGroupSettingDistributionRatio(scope.row.group_name, 'newmedia', scope.row.tempDistributionRatio);
                                                                    scope.row.isEditing = false;
                                                                }"
                                                            ></el-input-number>
                                                            <div style="margin-left: 5px;">
                                                                <i
                                                                    class="el-icon-check"
                                                                    style="cursor: pointer; color: #67C23A; margin-right: 3px;"
                                                                    @click="() => {
                                                                        updateGroupSettingDistributionRatio(scope.row.group_name, 'newmedia', scope.row.tempDistributionRatio);
                                                                        scope.row.isEditing = false;
                                                                    }"
                                                                >
                                                                    <svg viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg" width="14" height="14">
                                                                        <path fill="currentColor" d="M406.656 706.944 195.84 496.256a32 32 0 1 0-45.248 45.248l256 256 512-512a32 32 0 0 0-45.248-45.248L406.592 706.944z"></path>
                                                                    </svg>
                                                                </i>
                                                                <i
                                                                    class="el-icon-close"
                                                                    style="cursor: pointer; color: #F56C6C;"
                                                                    @click="scope.row.isEditing = false"
                                                                >
                                                                    <svg viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg" width="14" height="14">
                                                                        <path fill="currentColor" d="M764.288 214.592 512 466.88 259.712 214.592a31.936 31.936 0 0 0-45.12 45.12L466.752 512 214.528 764.224a31.936 31.936 0 1 0 45.12 45.184L512 557.184l252.288 252.288a31.936 31.936 0 0 0 45.12-45.12L557.12 512.064l252.288-252.352a31.936 31.936 0 1 0-45.12-45.184z"></path>
                                                                    </svg>
                                                                </i>
                                                            </div>
                                                        </div>
                                                    </template>
                                                </div>
                                                <div
                                                    style="padding: 5px; background-color: #e6f7ff; border-radius: 4px; margin-bottom: 2px; min-height: 24px; text-align: center; font-size: 12px;"
                                                >
                                                    <span title="分组内预计接待数总和">预计: {{ getGroupExpectedTotalReception(scope.row.group_name, 'newmedia') }}</span>
                                                </div>
                                                <div
                                                    style="padding: 5px; background-color: #f0f9eb; border-radius: 4px; min-height: 24px; text-align: center; font-size: 12px;"
                                                >
                                                    <span title="分组内实际接待数总和">实际: {{ getGroupActualTotalReception(scope.row.group_name, 'newmedia') }}</span>
                                                </div>
                                            </div>
                                        </template>
                                    </el-table-column>
                                    <el-table-column prop="member" label="人员" width="100">
                                        <template #default="scope">
                                            <div 
                                                style="position: relative; padding: 5px; border-radius: 4px; min-height: 24px;"
                                                @mouseenter="scope.row.showMemberEditIcon = true"
                                                @mouseleave="scope.row.showMemberEditIcon = false"
                                            >
                                                <template v-if="!scope.row.isMemberEditing">
                                                    <span>{{ scope.row.member }}</span>
                                                    <i 
                                                        v-show="scope.row.showMemberEditIcon"
                                                        class="el-icon-edit"
                                                        style="position: absolute; right: 5px; top: 50%; transform: translateY(-50%); cursor: pointer; color: #409EFF; font-size: 14px;"
                                                        @click="() => { 
                                                            scope.row.isMemberEditing = true; 
                                                            scope.row.tempMember = scope.row.member;
                                                        }"
                                                    >
                                                        <svg viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg" width="14" height="14">
                                                            <path fill="currentColor" d="M832 512a32 32 0 1 1 64 0v352a32 32 0 0 1-32 32H160a32 32 0 0 1-32-32V160a32 32 0 0 1 32-32h352a32 32 0 0 1 0 64H192v640h640V512z"></path>
                                                            <path fill="currentColor" d="m469.952 554.24 52.8-7.552L847.104 222.4a32 32 0 1 0-45.248-45.248L477.44 501.44l-7.552 52.8zm422.4-422.4a96 96 0 0 1 0 135.808l-331.84 331.84a32 32 0 0 1-18.112 9.088L436.8 623.68a32 32 0 0 1-36.224-36.224l15.104-105.6a32 32 0 0 1 9.024-18.112l331.904-331.84a96 96 0 0 1 135.744 0z"></path>
                                                        </svg>
                                                    </i>
                                                </template>
                                                <template v-else>
                                                    <div style="display: flex; align-items: center; width: 100%;">
                                                        <el-select
                                                            v-model="scope.row.tempMember"
                                                            size="small"
                                                            style="width: 100%;"
                                                            filterable
                                                            placeholder="选择人员"
                                                            @change="() => { 
                                                                updateRuleMember(scope.row.id, scope.row.tempMember);
                                                                scope.row.isMemberEditing = false;
                                                            }"
                                                            @keyup.enter="() => {
                                                                updateRuleMember(scope.row.id, scope.row.tempMember);
                                                                scope.row.isMemberEditing = false;
                                                            }"
                                                            :loading="scope.row.loadingMembers"
                                                        >
                                                            <el-option
                                                                v-for="member in getGroupMembers(scope.row.group_name, scope.row.channel_type === '电商渠道' ? 'ecommerce' : 'newmedia')"
                                                                :key="member.name"
                                                                :label="member.name"
                                                                :value="member.name"
                                                            ></el-option>
                                                        </el-select>
                                                    </div>
                                                </template>
                                            </div>
                                        </template>
                                    </el-table-column>
                                    <el-table-column prop="shift" label="班次" width="70">
                                        <template #default="scope">
                                            <div 
                                                style="position: relative; padding: 5px; border-radius: 4px; min-height: 24px;"
                                                @mouseenter="scope.row.showShiftEditIcon = true"
                                                @mouseleave="scope.row.showShiftEditIcon = false"
                                            >
                                                <template v-if="!scope.row.isShiftEditing">
                                            <el-tag v-if="scope.row.shift === '白班'" type="success">白班</el-tag>
                                            <el-tag v-else-if="scope.row.shift === '午班'" type="primary">午班</el-tag>
                                            <el-tag v-else-if="scope.row.shift === '晚班'" type="warning">晚班</el-tag>
                                                    <el-tag v-else-if="scope.row.shift === '全天'" type="danger">全天</el-tag>
                                                    <el-tag v-else type="info">无</el-tag>
                                                    <i 
                                                        v-show="scope.row.showShiftEditIcon"
                                                        class="el-icon-edit"
                                                        style="position: absolute; right: 5px; top: 50%; transform: translateY(-50%); cursor: pointer; color: #409EFF; font-size: 14px;"
                                                        @click="() => { 
                                                            scope.row.isShiftEditing = true; 
                                                            scope.row.tempShift = scope.row.shift;
                                                        }"
                                                    >
                                                        <svg viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg" width="14" height="14">
                                                            <path fill="currentColor" d="M832 512a32 32 0 1 1 64 0v352a32 32 0 0 1-32 32H160a32 32 0 0 1-32-32V160a32 32 0 0 1 32-32h352a32 32 0 0 1 0 64H192v640h640V512z"></path>
                                                            <path fill="currentColor" d="m469.952 554.24 52.8-7.552L847.104 222.4a32 32 0 1 0-45.248-45.248L477.44 501.44l-7.552 52.8zm422.4-422.4a96 96 0 0 1 0 135.808l-331.84 331.84a32 32 0 0 1-18.112 9.088L436.8 623.68a32 32 0 0 1-36.224-36.224l15.104-105.6a32 32 0 0 1 9.024-18.112l331.904-331.84a96 96 0 0 1 135.744 0z"></path>
                                                        </svg>
                                                    </i>
                                                </template>
                                                <template v-else>
                                                    <div style="display: flex; align-items: center; width: 100%;">
                                                        <el-select
                                                            v-model="scope.row.tempShift"
                                                            size="small"
                                                            style="width: 100%;"
                                                            @change="() => { 
                                                                updateRuleShift(scope.row.id, scope.row.tempShift);
                                                                scope.row.isShiftEditing = false;
                                                            }"
                                                            @keyup.enter="() => {
                                                                updateRuleShift(scope.row.id, scope.row.tempShift);
                                                                scope.row.isShiftEditing = false;
                                                            }"
                                                        >
                                                            <el-option
                                                                v-for="shift in ['无', '白班', '午班', '晚班', '全天']"
                                                                :key="shift"
                                                                :label="shift"
                                                                :value="shift"
                                                            ></el-option>
                                                        </el-select>
                                                    </div>
                                                </template>
                                            </div>
                                        </template>
                                    </el-table-column>
                                    <el-table-column prop="expected_total" label="预计接待数" width="90">
                                        <template #default="scope">
                                            <div style="padding: 5px; border-radius: 4px; min-height: 24px; text-align: center;">
                                                {{ (scope.row.expected_free_reception || scope.row.expected_free || 0) + (scope.row.expected_paid_reception || scope.row.expected_paid || 0) }}
                                            </div>
                                        </template>
                                    </el-table-column>
                                    <el-table-column prop="expected_free_reception" label="预计免费接待数" width="95">
                                        <template #default="scope">
                                            <div 
                                                style="position: relative; padding: 5px; border-radius: 4px; min-height: 24px;"
                                                @mouseenter="scope.row.showExpectedFreeReceptionEditIcon = true"
                                                @mouseleave="scope.row.showExpectedFreeReceptionEditIcon = false"
                                            >
                                                <template v-if="!scope.row.isExpectedFreeReceptionEditing">
                                                    <span style="text-align: center; display: inline-block; width: 100%;">{{ scope.row.expected_free_reception || scope.row.expected_free || 0 }}</span>
                                                    <i 
                                                        v-show="scope.row.showExpectedFreeReceptionEditIcon"
                                                        class="el-icon-edit"
                                                        style="position: absolute; right: 5px; top: 50%; transform: translateY(-50%); cursor: pointer; color: #409EFF; font-size: 14px;"
                                                        @click="() => { 
                                                            scope.row.isExpectedFreeReceptionEditing = true; 
                                                            scope.row.tempExpectedFreeReception = scope.row.expected_free_reception || scope.row.expected_free || 0;
                                                        }"
                                                    >
                                                        <svg viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg" width="14" height="14">
                                                            <path fill="currentColor" d="M832 512a32 32 0 1 1 64 0v352a32 32 0 0 1-32 32H160a32 32 0 0 1-32-32V160a32 32 0 0 1 32-32h352a32 32 0 0 1 0 64H192v640h640V512z"></path>
                                                            <path fill="currentColor" d="m469.952 554.24 52.8-7.552L847.104 222.4a32 32 0 1 0-45.248-45.248L477.44 501.44l-7.552 52.8zm422.4-422.4a96 96 0 0 1 0 135.808l-331.84 331.84a32 32 0 0 1-18.112 9.088L436.8 623.68a32 32 0 0 1-36.224-36.224l15.104-105.6a32 32 0 0 1 9.024-18.112l331.904-331.84a96 96 0 0 1 135.744 0z"></path>
                                                        </svg>
                                                    </i>
                                                </template>
                                                <template v-else>
                                                    <div style="display: flex; align-items: center; width: 100%;">
                                                        <el-input-number
                                                            v-model="scope.row.tempExpectedFreeReception"
                                                            :min="0"
                                                            :precision="0"
                                                            :controls="false"
                                                            size="small"
                                                            style="width: 100%;"
                                                            @keyup.enter="() => { 
                                                                updateRuleFreeReception(scope.row.id, scope.row.tempExpectedFreeReception);
                                                                scope.row.isExpectedFreeReceptionEditing = false;
                                                            }"
                                                        ></el-input-number>
                                                    </div>
                                                </template>
                                            </div>
                                        </template>
                                    </el-table-column>
                                    <el-table-column prop="expected_paid_reception" label="预计付费接待数" width="95">
                                        <template #default="scope">
                                            <div 
                                                style="position: relative; padding: 5px; border-radius: 4px; min-height: 24px;"
                                                @mouseenter="scope.row.showExpectedPaidReceptionEditIcon = true"
                                                @mouseleave="scope.row.showExpectedPaidReceptionEditIcon = false"
                                            >
                                                <template v-if="!scope.row.isExpectedPaidReceptionEditing">
                                                    <span style="text-align: center; display: inline-block; width: 100%;">{{ scope.row.expected_paid_reception || scope.row.expected_paid || 0 }}</span>
                                                    <i 
                                                        v-show="scope.row.showExpectedPaidReceptionEditIcon"
                                                        class="el-icon-edit"
                                                        style="position: absolute; right: 5px; top: 50%; transform: translateY(-50%); cursor: pointer; color: #409EFF; font-size: 14px;"
                                                        @click="() => { 
                                                            scope.row.isExpectedPaidReceptionEditing = true; 
                                                            scope.row.tempExpectedPaidReception = scope.row.expected_paid_reception || scope.row.expected_paid || 0;
                                                        }"
                                                    >
                                                        <svg viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg" width="14" height="14">
                                                            <path fill="currentColor" d="M832 512a32 32 0 1 1 64 0v352a32 32 0 0 1-32 32H160a32 32 0 0 1-32-32V160a32 32 0 0 1 32-32h352a32 32 0 0 1 0 64H192v640h640V512z"></path>
                                                            <path fill="currentColor" d="m469.952 554.24 52.8-7.552L847.104 222.4a32 32 0 1 0-45.248-45.248L477.44 501.44l-7.552 52.8zm422.4-422.4a96 96 0 0 1 0 135.808l-331.84 331.84a32 32 0 0 1-18.112 9.088L436.8 623.68a32 32 0 0 1-36.224-36.224l15.104-105.6a32 32 0 0 1 9.024-18.112l331.904-331.84a96 96 0 0 1 135.744 0z"></path>
                                                        </svg>
                                                    </i>
                                                </template>
                                                <template v-else>
                                                    <div style="display: flex; align-items: center; width: 100%;">
                                                        <el-input-number
                                                            v-model="scope.row.tempExpectedPaidReception"
                                                            :min="0"
                                                            :precision="0"
                                                            :controls="false"
                                                            size="small"
                                                            style="width: 100%;"
                                                            @keyup.enter="() => { 
                                                                updateRulePaidReception(scope.row.id, scope.row.tempExpectedPaidReception);
                                                                scope.row.isExpectedPaidReceptionEditing = false;
                                                            }"
                                                        ></el-input-number>
                                                    </div>
                                                </template>
                                            </div>
                                        </template>
                                    </el-table-column>
                                    <el-table-column prop="actual_reception" label="实际接待数" width="85" align="center">
                                        <template #default="scope">
                                            <div style="padding: 5px; border-radius: 4px; min-height: 24px; text-align: center;">
                                                {{ (scope.row.actual_free_reception || scope.row.actual_free || 0) + (scope.row.actual_paid_reception || scope.row.actual_paid || 0) }}
                                            </div>
                                        </template>
                                    </el-table-column>
                                    <el-table-column prop="actual_free_reception" label="实际免费接待数" width="95" align="center">
                                        <template #default="scope">
                                            <div style="padding: 5px; border-radius: 4px; min-height: 24px; text-align: center;">
                                                {{ scope.row.actual_free_reception || scope.row.actual_free || 0 }}
                                            </div>
                                        </template>
                                    </el-table-column>
                                    <el-table-column prop="actual_paid_reception" label="实际付费接待数" width="95" align="center">
                                        <template #default="scope">
                                            <div style="padding: 5px; border-radius: 4px; min-height: 24px; text-align: center;">
                                                {{ scope.row.actual_paid_reception || scope.row.actual_paid || 0 }}
                                            </div>
                                        </template>
                                    </el-table-column>
                                    <el-table-column prop="store" label="店铺" width="180">
                                        <template #default="scope">
                                            <div style="display: flex; justify-content: space-between; align-items: center;">
                                                <span>{{ scope.row.store }}</span>
                                                <el-button 
                                                    size="small" 
                                                    type="primary" 
                                                    style="padding: 2px 5px; font-size: 12px; margin-left: 5px; height: 22px; line-height: 1;"
                                                    @click="showStoreSelection(scope.row)">
                                                    选择
                                                </el-button>
                                            </div>
                                        </template>
                                    </el-table-column>
                                    <el-table-column prop="paid_value" label="付费产值" width="75"></el-table-column>
                                    <el-table-column label="时间段" width="110">
                                        <template #default="scope">
                                            {{ formatTime(scope.row.time_range_start) }} - {{ formatTime(scope.row.time_range_end) }}
                                        </template>
                                    </el-table-column>
                                    
                                    <el-table-column label="操作" width="65" class-name="operation-column" align="center">
                                        <template #default="scope">
                                            <div class="operation-buttons">
                                                <div class="icon-button add" @click="showAddRowDialog(scope.row)">
                                                    <i class="fas fa-plus"></i>
                                                </div>
                                                <div class="icon-button delete" @click="deleteRule(scope.row.id)">
                                                    <i class="fas fa-trash-alt"></i>
                                                </div>
                                            </div>
                                        </template>
                                    </el-table-column>
                                </el-table>
                            </div>
                            <div class="empty-data" v-else>
                                <i class="fas fa-inbox"></i>
                                <p>当前日期没有新媒体渠道分发规则，请点击"创建分发规则"按钮创建</p>
                            </div>
                        </el-tab-pane>
                        <el-tab-pane label="公海渠道" name="gonghai">
                            <div class="channel-info" v-if="hasGonghaiRules">
                                <el-descriptions
                                  class="reception-descriptions"
                                  :column="2"
                                  border
                                  size="small"
                                  :labelStyle="{minWidth: 'auto', width: 'auto', fontSize: '12px', padding: '3px', textAlign: 'center'}"
                                  :contentStyle="{minWidth: 'auto', width: 'auto', fontSize: '12px', padding: '3px', textAlign: 'center'}">
                                    <el-descriptions-item label="预计接待数" class="expected-reception-item">
                                        <div
                                            style="position: relative; padding: 2px; border-radius: 4px; min-height: 20px; text-align: center;"
                                            @mouseenter="showGonghaiExpectedReceptionEditIcon = true"
                                            @mouseleave="showGonghaiExpectedReceptionEditIcon = false"
                                        >
                                            <template v-if="!isEditingGonghaiExpectedReception">
                                                <span style="text-align: center; display: inline-block; width: 100%;">{{ gonghaiReception?.expected_reception }}</span>
                                                <i
                                                    v-show="showGonghaiExpectedReceptionEditIcon"
                                                    class="el-icon-edit"
                                                    style="position: absolute; right: 5px; top: 50%; transform: translateY(-50%); cursor: pointer; color: #409EFF; font-size: 12px;"
                                                    @click="() => {
                                                        isEditingGonghaiExpectedReception = true;
                                                        tempGonghaiExpectedReception = gonghaiReception?.expected_reception;
                                                    }"
                                                >
                                                    <svg viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg" width="12" height="12">
                                                        <path fill="currentColor" d="M832 512a32 32 0 1 1 64 0v352a32 32 0 0 1-32 32H160a32 32 0 0 1-32-32V160a32 32 0 0 1 32-32h352a32 32 0 0 1 0 64H192v640h640V512z"></path>
                                                        <path fill="currentColor" d="m469.952 554.24 52.8-7.552L847.104 222.4a32 32 0 1 0-45.248-45.248L477.44 501.44l-7.552 52.8zm422.4-422.4a96 96 0 0 1 0 135.808l-331.84 331.84a32 32 0 0 1-18.112 9.088L436.8 623.68a32 32 0 0 1-36.224-36.224l15.104-105.6a32 32 0 0 1 9.024-18.112l331.904-331.84a96 96 0 0 1 135.744 0z"></path>
                                                    </svg>
                                                </i>
                                            </template>
                                            <template v-else>
                                                <div style="display: flex; align-items: center; width: 100%;">
                                                    <el-input-number
                                                        v-model="tempGonghaiExpectedReception"
                                                        :min="0"
                                                        :precision="0"
                                                        :controls="false"
                                                        size="small"
                                                        style="width: 80px;"
                                                        @keyup.enter="() => {
                                                            updateChannelExpectedReception('gonghai', tempGonghaiExpectedReception);
                                                            isEditingGonghaiExpectedReception = false;
                                                        }"
                                                    ></el-input-number>
                                                </div>
                                            </template>
                                        </div>
                                    </el-descriptions-item>
                                    <el-descriptions-item label="实际接待数" class="actual-reception-item" style="text-align: center;">{{ calculateActualTotalReception('gonghai') || 0 }}</el-descriptions-item>
                                </el-descriptions>
                            </div>
                            <div class="table-container" v-if="hasGonghaiRules" id="gonghai-table">
                                <!-- 批量操作按钮 -->
                                <div class="batch-operations" style="margin-bottom: 10px; display: flex; justify-content: space-between; align-items: center;">
                                    <div>
                                        <el-button size="small" @click="selectAllGonghaiRows">全选</el-button>
                                        <el-button size="small" @click="clearGonghaiSelection">清空选择</el-button>
                                        <span style="margin-left: 15px; color: #606266;">
                                            已选择 {{ selectedGonghaiRows.length }} 条记录
                                        </span>
                                    </div>
                                    <div>
                                        <el-button
                                            type="warning"
                                            size="small"
                                            @click="showQuickBatchEdit('gonghai')"
                                            :disabled="selectedGonghaiRows.length === 0">
                                            批量修改排班
                                        </el-button>
                                        <el-button
                                            type="danger"
                                            size="small"
                                            @click="showBatchDeleteConfirm('gonghai')"
                                            :disabled="selectedGonghaiRows.length === 0">
                                            批量删除
                                        </el-button>
                                        <el-button
                                            type="primary"
                                            size="small"
                                            @click="showBatchAppendEdit('gonghai')"
                                            :disabled="selectedGonghaiRows.length === 0">
                                            批量追加
                                        </el-button>
                                    </div>
                                </div>

                                <el-table
                                    :data="gonghaiRules"
                                    border
                                    style="width: 100%"
                                    :row-class-name="tableRowClassName"
                                    :span-method="leaderSpanMethod"
                                    header-row-class-name="sticky-header"
                                    :header-cell-style="{background:'#f5f7fa',color:'#000000',fontWeight:'bold'}"
                                    :show-overflow-tooltip="true"
                                    :scrollbar-always-on="false"
                                    @selection-change="handleGonghaiSelectionChange"
                                    ref="gonghaiTable">
                                    <el-table-column type="selection" width="55"></el-table-column>
                                    <el-table-column type="index" label="序号" width="45"></el-table-column>
                                    <el-table-column prop="group_name" label="分组" width="100">
                                        <template #default="scope">
                                            <div style="display: flex; flex-direction: column; padding: 5px;">
                                                <div style="font-weight: bold; margin-bottom: 4px;">{{ scope.row.group_name }}</div>
                                                <div style="font-size: 12px; color: #606266; margin-bottom: 2px;">{{ scope.row.leader }}</div>
                                                <div style="font-size: 12px; color: #909399;" v-if="isFirstInGroup(scope.row, scope.$index, 'gonghai')">
                                                    在岗: {{ getGroupMemberCount(scope.row.group_name, 'gonghai') }} 人
                                                </div>
                                            </div>
                                        </template>
                                    </el-table-column>
                                    <el-table-column label="分配数量" width="85">
                                        <template #default="scope">
                                            <div style="display: flex; flex-direction: column;">
                                                <div
                                                    class="distribution-setting-row"
                                                    style="position: relative; padding: 5px; background-color: #f5f7fa; border-radius: 4px; margin-bottom: 2px; min-height: 24px;"
                                                    @mouseenter="scope.row.showEditIcon = true"
                                                    @mouseleave="scope.row.showEditIcon = false"
                                                >
                                                    <template v-if="!scope.row.isEditing">
                                                        <span>{{ getGroupSettingDistributionRatio(scope.row.group_name, 'gonghai') }}</span>
                                                        <i
                                                            v-show="scope.row.showEditIcon"
                                                            class="el-icon-edit"
                                                            style="position: absolute; right: 5px; top: 50%; transform: translateY(-50%); cursor: pointer; color: #409EFF; font-size: 14px;"
                                                            @click="() => {
                                                                scope.row.isEditing = true;
                                                                scope.row.tempDistributionRatio = getGroupSettingDistributionRatio(scope.row.group_name, 'gonghai');
                                                            }"
                                                        >
                                                            <svg viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg" width="14" height="14">
                                                                <path fill="currentColor" d="M832 512a32 32 0 1 1 64 0v352a32 32 0 0 1-32 32H160a32 32 0 0 1-32-32V160a32 32 0 0 1 32-32h352a32 32 0 0 1 0 64H192v640h640V512z"></path>
                                                                <path fill="currentColor" d="m469.952 554.24 52.8-7.552L847.104 222.4a32 32 0 1 0-45.248-45.248L477.44 501.44l-7.552 52.8zm422.4-422.4a96 96 0 0 1 0 135.808l-331.84 331.84a32 32 0 0 1-18.112 9.088L436.8 623.68a32 32 0 0 1-36.224-36.224l15.104-105.6a32 32 0 0 1 9.024-18.112l331.904-331.84a96 96 0 0 1 135.744 0z"></path>
                                                            </svg>
                                                        </i>
                                                    </template>
                                                    <template v-else>
                                                        <div style="display: flex; align-items: center; width: 100%;">
                                                            <el-input-number
                                                                v-model="scope.row.tempDistributionRatio"
                                                                :min="0"
                                                                :precision="0"
                                                                :controls="false"
                                                                size="small"
                                                                style="width: 50px;"
                                                                @keyup.enter="() => {
                                                                    updateGroupSettingDistributionRatio(scope.row.group_name, 'gonghai', scope.row.tempDistributionRatio);
                                                                    scope.row.isEditing = false;
                                                                }"
                                                            ></el-input-number>
                                                            <div style="margin-left: 5px;">
                                                                <i
                                                                    class="el-icon-check"
                                                                    style="cursor: pointer; color: #67C23A; margin-right: 3px;"
                                                                    @click="() => {
                                                                        updateGroupSettingDistributionRatio(scope.row.group_name, 'gonghai', scope.row.tempDistributionRatio);
                                                                        scope.row.isEditing = false;
                                                                    }"
                                                                >
                                                                    <svg viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg" width="14" height="14">
                                                                        <path fill="currentColor" d="M406.656 706.944 195.84 496.256a32 32 0 1 0-45.248 45.248l256 256 512-512a32 32 0 0 0-45.248-45.248L406.592 706.944z"></path>
                                                                    </svg>
                                                                </i>
                                                                <i
                                                                    class="el-icon-close"
                                                                    style="cursor: pointer; color: #F56C6C;"
                                                                    @click="scope.row.isEditing = false"
                                                                >
                                                                    <svg viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg" width="14" height="14">
                                                                        <path fill="currentColor" d="M764.288 214.592 512 466.88 259.712 214.592a31.936 31.936 0 0 0-45.12 45.12L466.752 512 214.528 764.224a31.936 31.936 0 1 0 45.12 45.184L512 557.184l252.288 252.288a31.936 31.936 0 0 0 45.12-45.12L557.12 512.064l252.288-252.352a31.936 31.936 0 1 0-45.12-45.184z"></path>
                                                                    </svg>
                                                                </i>
                                                            </div>
                                                        </div>
                                                    </template>
                                                </div>
                                                <div
                                                    style="padding: 5px; background-color: #e6f7ff; border-radius: 4px; margin-bottom: 2px; min-height: 24px; text-align: center; font-size: 12px;"
                                                >
                                                    <span title="分组内预计接待数总和">预计: {{ getGroupExpectedTotalReception(scope.row.group_name, 'gonghai') }}</span>
                                                </div>
                                                <div
                                                    style="padding: 5px; background-color: #f0f9eb; border-radius: 4px; min-height: 24px; text-align: center; font-size: 12px;"
                                                >
                                                    <span title="分组内实际接待数总和">实际: {{ getGroupActualTotalReception(scope.row.group_name, 'gonghai') }}</span>
                                                </div>
                                            </div>
                                        </template>
                                    </el-table-column>
                                    <el-table-column prop="member" label="人员" width="100">
                                        <template #default="scope">
                                            <div
                                                style="position: relative; padding: 5px; border-radius: 4px; min-height: 24px;"
                                                @mouseenter="scope.row.showMemberEditIcon = true"
                                                @mouseleave="scope.row.showMemberEditIcon = false"
                                            >
                                                <template v-if="!scope.row.isMemberEditing">
                                                    <span>{{ scope.row.member }}</span>
                                                    <i
                                                        v-show="scope.row.showMemberEditIcon"
                                                        class="el-icon-edit"
                                                        style="position: absolute; right: 5px; top: 50%; transform: translateY(-50%); cursor: pointer; color: #409EFF; font-size: 14px;"
                                                        @click="() => {
                                                            scope.row.isMemberEditing = true;
                                                            scope.row.tempMember = scope.row.member;
                                                        }"
                                                    >
                                                        <svg viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg" width="14" height="14">
                                                            <path fill="currentColor" d="M832 512a32 32 0 1 1 64 0v352a32 32 0 0 1-32 32H160a32 32 0 0 1-32-32V160a32 32 0 0 1 32-32h352a32 32 0 0 1 0 64H192v640h640V512z"></path>
                                                            <path fill="currentColor" d="m469.952 554.24 52.8-7.552L847.104 222.4a32 32 0 1 0-45.248-45.248L477.44 501.44l-7.552 52.8zm422.4-422.4a96 96 0 0 1 0 135.808l-331.84 331.84a32 32 0 0 1-18.112 9.088L436.8 623.68a32 32 0 0 1-36.224-36.224l15.104-105.6a32 32 0 0 1 9.024-18.112l331.904-331.84a96 96 0 0 1 135.744 0z"></path>
                                                        </svg>
                                                    </i>
                                                </template>
                                                <template v-else>
                                                    <div style="display: flex; align-items: center; width: 100%;">
                                                        <el-select
                                                            v-model="scope.row.tempMember"
                                                            size="small"
                                                            style="width: 100%;"
                                                            filterable
                                                            placeholder="选择人员"
                                                            @change="() => {
                                                                updateRuleMember(scope.row.id, scope.row.tempMember);
                                                                scope.row.isMemberEditing = false;
                                                            }"
                                                            @keyup.enter="() => {
                                                                updateRuleMember(scope.row.id, scope.row.tempMember);
                                                                scope.row.isMemberEditing = false;
                                                            }"
                                                            :loading="scope.row.loadingMembers"
                                                        >
                                                            <el-option
                                                                v-for="member in getGroupMembers(scope.row.group_name, scope.row.channel_type === '公海渠道' ? 'gonghai' : (scope.row.channel_type === '电商渠道' ? 'ecommerce' : 'newmedia'))"
                                                                :key="member.name"
                                                                :label="member.name"
                                                                :value="member.name"
                                                            ></el-option>
                                                        </el-select>
                                                    </div>
                                                </template>
                                            </div>
                                        </template>
                                    </el-table-column>
                                    <el-table-column prop="shift" label="班次" width="70">
                                        <template #default="scope">
                                            <div
                                                style="position: relative; padding: 5px; border-radius: 4px; min-height: 24px;"
                                                @mouseenter="scope.row.showShiftEditIcon = true"
                                                @mouseleave="scope.row.showShiftEditIcon = false"
                                            >
                                                <template v-if="!scope.row.isShiftEditing">
                                            <el-tag v-if="scope.row.shift === '白班'" type="success">白班</el-tag>
                                            <el-tag v-else-if="scope.row.shift === '午班'" type="primary">午班</el-tag>
                                            <el-tag v-else-if="scope.row.shift === '晚班'" type="warning">晚班</el-tag>
                                                    <el-tag v-else-if="scope.row.shift === '全天'" type="danger">全天</el-tag>
                                                    <el-tag v-else type="info">无</el-tag>
                                                    <i
                                                        v-show="scope.row.showShiftEditIcon"
                                                        class="el-icon-edit"
                                                        style="position: absolute; right: 5px; top: 50%; transform: translateY(-50%); cursor: pointer; color: #409EFF; font-size: 14px;"
                                                        @click="() => {
                                                            scope.row.isShiftEditing = true;
                                                            scope.row.tempShift = scope.row.shift;
                                                        }"
                                                    >
                                                        <svg viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg" width="14" height="14">
                                                            <path fill="currentColor" d="M832 512a32 32 0 1 1 64 0v352a32 32 0 0 1-32 32H160a32 32 0 0 1-32-32V160a32 32 0 0 1 32-32h352a32 32 0 0 1 0 64H192v640h640V512z"></path>
                                                            <path fill="currentColor" d="m469.952 554.24 52.8-7.552L847.104 222.4a32 32 0 1 0-45.248-45.248L477.44 501.44l-7.552 52.8zm422.4-422.4a96 96 0 0 1 0 135.808l-331.84 331.84a32 32 0 0 1-18.112 9.088L436.8 623.68a32 32 0 0 1-36.224-36.224l15.104-105.6a32 32 0 0 1 9.024-18.112l331.904-331.84a96 96 0 0 1 135.744 0z"></path>
                                                        </svg>
                                                    </i>
                                                </template>
                                                <template v-else>
                                                    <div style="display: flex; align-items: center; width: 100%;">
                                                        <el-select
                                                            v-model="scope.row.tempShift"
                                                            size="small"
                                                            style="width: 100%;"
                                                            @change="() => {
                                                                updateRuleShift(scope.row.id, scope.row.tempShift);
                                                                scope.row.isShiftEditing = false;
                                                            }"
                                                            @keyup.enter="() => {
                                                                updateRuleShift(scope.row.id, scope.row.tempShift);
                                                                scope.row.isShiftEditing = false;
                                                            }"
                                                        >
                                                            <el-option
                                                                v-for="shift in ['无', '白班','午班', '晚班', '全天']"
                                                                :key="shift"
                                                                :label="shift"
                                                                :value="shift"
                                                            ></el-option>
                                                        </el-select>
                                                    </div>
                                                </template>
                                            </div>
                                        </template>
                                    </el-table-column>
                                    <el-table-column prop="expected_total" label="预计接待数" width="90" align="center">
                                        <template #default="scope">
                                            <div
                                                style="position: relative; padding: 5px; border-radius: 4px; min-height: 24px;"
                                                @mouseenter="scope.row.showExpectedReceptionEditIcon = true"
                                                @mouseleave="scope.row.showExpectedReceptionEditIcon = false"
                                            >
                                                <template v-if="!scope.row.isExpectedReceptionEditing">
                                                    <span>{{ scope.row?.expected_total }}</span>
                                                    <i
                                                        v-show="scope.row.showExpectedReceptionEditIcon"
                                                        class="el-icon-edit"
                                                        style="position: absolute; right: 5px; top: 50%; transform: translateY(-50%); cursor: pointer; color: #409EFF; font-size: 14px;"
                                                        @click="() => {
                                                            scope.row.isExpectedReceptionEditing = true;
                                                            scope.row.tempExpectedReception = scope.row?.expected_total;
                                                        }"
                                                    >
                                                        <svg viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg" width="14" height="14">
                                                            <path fill="currentColor" d="M832 512a32 32 0 1 1 64 0v352a32 32 0 0 1-32 32H160a32 32 0 0 1-32-32V160a32 32 0 0 1 32-32h352a32 32 0 0 1 0 64H192v640h640V512z"></path>
                                                            <path fill="currentColor" d="m469.952 554.24 52.8-7.552L847.104 222.4a32 32 0 1 0-45.248-45.248L477.44 501.44l-7.552 52.8zm422.4-422.4a96 96 0 0 1 0 135.808l-331.84 331.84a32 32 0 0 1-18.112 9.088L436.8 623.68a32 32 0 0 1-36.224-36.224l15.104-105.6a32 32 0 0 1 9.024-18.112l331.904-331.84a96 96 0 0 1 135.744 0z"></path>
                                                        </svg>
                                                    </i>
                                                </template>
                                                <template v-else>
                                                    <div style="display: flex; align-items: center; width: 100%;">
                                                        <el-input-number
                                                            v-model="scope.row.tempExpectedReception"
                                                            :min="0"
                                                            :precision="0"
                                                            :controls="false"
                                                            size="small"
                                                            style="width: 50px;"
                                                            @keyup.enter="() => {
                                                                updateRuleExpectedReception(scope.row.id, scope.row.tempExpectedReception);
                                                                scope.row.isExpectedReceptionEditing = false;
                                                            }"
                                                        ></el-input-number>
                                                        <div style="margin-left: 5px;">
                                                            <i
                                                                class="el-icon-check"
                                                                style="cursor: pointer; color: #67C23A; margin-right: 3px;"
                                                                @click="() => {
                                                                    updateRuleExpectedReception(scope.row.id, scope.row.tempExpectedReception);
                                                                    scope.row.isExpectedReceptionEditing = false;
                                                                }"
                                                            >
                                                                <svg viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg" width="14" height="14">
                                                                    <path fill="currentColor" d="M406.656 706.944 195.84 496.256a32 32 0 1 0-45.248 45.248l256 256 512-512a32 32 0 0 0-45.248-45.248L406.592 706.944z"></path>
                                                                </svg>
                                                            </i>
                                                            <i
                                                                class="el-icon-close"
                                                                style="cursor: pointer; color: #F56C6C;"
                                                                @click="scope.row.isExpectedReceptionEditing = false"
                                                            >
                                                                <svg viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg" width="14" height="14">
                                                                    <path fill="currentColor" d="M764.288 214.592 512 466.88 259.712 214.592a31.936 31.936 0 0 0-45.12 45.12L466.752 512 214.528 764.224a31.936 31.936 0 1 0 45.12 45.184L512 557.184l252.288 252.288a31.936 31.936 0 0 0 45.12-45.12L557.12 512.064l252.288-252.352a31.936 31.936 0 1 0-45.12-45.184z"></path>
                                                                </svg>
                                                            </i>
                                                        </div>
                                                    </div>
                                                </template>
                                            </div>
                                        </template>
                                    </el-table-column>
                                    <el-table-column prop="actual_reception" label="实际接待数" width="85" align="center">
                                        <template #default="scope">
                                            <div style="padding: 5px; border-radius: 4px; min-height: 24px; text-align: center;">
                                                {{ (scope.row.actual_free_reception || scope.row.actual_free || 0) + (scope.row.actual_paid_reception || scope.row.actual_paid || 0) }}
                                            </div>
                                        </template>
                                    </el-table-column>
                                    <el-table-column prop="store" label="店铺" width="180">
                                        <template #default="scope">
                                            <div style="display: flex; justify-content: space-between; align-items: center;">
                                                <span>{{ scope.row.store }}</span>
                                                <el-button
                                                    size="small"
                                                    type="primary"
                                                    style="padding: 2px 5px; font-size: 12px; margin-left: 5px; height: 22px; line-height: 1;"
                                                    @click="showStoreSelection(scope.row)">
                                                    选择
                                                </el-button>
                                            </div>
                                        </template>
                                    </el-table-column>
                                    <el-table-column label="时间段" width="110">
                                        <template #default="scope">
                                            {{ formatTime(scope.row.time_range_start) }} - {{ formatTime(scope.row.time_range_end) }}
                                        </template>
                                    </el-table-column>

                                    <el-table-column label="操作" width="65" class-name="operation-column">
                                        <template #default="scope">
                                            <div class="operation-buttons">
                                                <div class="icon-button add" @click="showAddRowDialog(scope.row)">
                                                    <i class="fas fa-plus"></i>
                                                </div>
                                                <div class="icon-button delete" @click="deleteRule(scope.row.id)">
                                                    <i class="fas fa-trash-alt"></i>
                                                </div>
                                            </div>
                                        </template>
                                    </el-table-column>
                                </el-table>
                            </div>
                            <div class="empty-data" v-else>
                                <i class="fas fa-inbox"></i>
                                <p>当前日期没有公海渠道分发规则，请点击"创建分发规则"按钮创建</p>
                            </div>
                        </el-tab-pane>
                    </el-tabs>
                </div>
            </div>



            <!-- 批量修改排班对话框 -->
            <el-dialog
                title="批量修改排班"
                v-model="showQuickBatchDialog"
                width="500px"
                :append-to-body="true"
                :close-on-click-modal="false">
                <div class="quick-batch-form">
                    <div style="margin-bottom: 15px; padding: 10px; background-color: #f0f9eb; border-radius: 4px; color: #67C23A;">
                        <i class="el-icon-success" style="margin-right: 5px;"></i>
                        将对选中的 {{ quickBatchSelectedRows.length }} 条记录进行批量修改
                    </div>
                    <el-form :model="quickBatchForm" label-width="120px">
                        <el-form-item v-if="quickBatchChannelType === 'newmedia'" label="人员">
                            <el-select
                                v-model="quickBatchForm.member"
                                placeholder="选择人员（不选择则不修改）"
                                clearable
                                filterable
                                style="width: 300px;">
                                <el-option
                                    v-for="member in getQuickBatchAvailableMembers()"
                                    :key="member.name"
                                    :label="member.name"
                                    :value="member.name">
                                </el-option>
                            </el-select>
                        </el-form-item>

                        <el-form-item label="班次">
                            <el-select
                                v-model="quickBatchForm.shift"
                                placeholder="选择班次（不选择则不修改）"
                                clearable
                                style="width: 300px;">
                                <el-option label="无" value="无"></el-option>
                                <el-option label="白班" value="白班"></el-option>
                                <el-option label="午班" value="午班"></el-option>
                                <el-option label="晚班" value="晚班"></el-option>
                                <el-option label="全天" value="全天"></el-option>
                            </el-select>
                        </el-form-item>

                        <el-form-item v-if="quickBatchChannelType === 'ecommerce'" label="预计接待数">
                            <el-input-number
                                v-model="quickBatchForm.expectedTotal"
                                :min="0"
                                placeholder="不填写则不修改"
                                style="width: 300px;">
                            </el-input-number>
                        </el-form-item>

                        <el-form-item v-if="quickBatchChannelType === 'newmedia'" label="预计免费接待数">
                            <el-input-number
                                v-model="quickBatchForm.expectedFree"
                                :min="0"
                                placeholder="不填写则不修改"
                                style="width: 300px;">
                            </el-input-number>
                        </el-form-item>

                        <el-form-item v-if="quickBatchChannelType === 'newmedia'" label="预计付费接待数">
                            <el-input-number
                                v-model="quickBatchForm.expectedPaid"
                                :min="0"
                                placeholder="不填写则不修改"
                                style="width: 300px;">
                            </el-input-number>
                        </el-form-item>

                        <el-form-item label="店铺">
                            <div>
                                <el-button
                                    size="small"
                                    type="primary"
                                    @click="openQuickBatchStoreSelection">
                                    选择店铺 ({{ quickBatchForm.selectedStores.length }})
                                </el-button>
                            </div>
                            <div v-if="quickBatchForm.selectedStores.length > 0" style="margin-top: 10px;">
                                <el-tag
                                    v-for="store in quickBatchForm.selectedStores"
                                    :key="store.name"
                                    closable
                                    @close="removeQuickBatchStoreSelection(store)"
                                    style="margin-right: 5px; margin-bottom: 5px;">
                                    {{ store.name }}
                                </el-tag>
                            </div>
                        </el-form-item>
                    </el-form>
                </div>
                <template #footer>
                    <span class="dialog-footer">
                        <el-button @click="showQuickBatchDialog = false">取消</el-button>
                        <el-button type="primary" @click="applyQuickBatchEdit">立即应用</el-button>
                    </span>
                </template>
            </el-dialog>

            <!-- 批量追加对话框 -->
            <el-dialog
                title="批量追加"
                v-model="showBatchAppendDialog"
                width="500px"
                :close-on-click-modal="false"
                destroy-on-close>
                <div style="margin-bottom: 20px;">
                    <el-alert
                        :title="`已选择 ${batchAppendSelectedRows.length} 条${batchAppendChannelType === 'ecommerce' ? '电商渠道' : '新媒体渠道'}记录`"
                        type="info"
                        :closable="false"
                        show-icon>
                    </el-alert>
                </div>
                <div>
                    <el-form :model="batchAppendForm" label-width="140px">
                        <el-form-item v-if="batchAppendChannelType === 'ecommerce'" label="预计接待数追加">
                            <el-input-number
                                v-model="batchAppendForm.expectedTotalAdd"
                                :min="0"
                                :disabled="batchAppendForm.expectedTotalReduce !== null && batchAppendForm.expectedTotalReduce !== undefined"
                                placeholder="输入要追加的数量"
                                style="width: 300px;">
                            </el-input-number>
                        </el-form-item>

                        <el-form-item v-if="batchAppendChannelType === 'ecommerce'" label="预计接待数减少">
                            <el-input-number
                                v-model="batchAppendForm.expectedTotalReduce"
                                :min="0"
                                :disabled="batchAppendForm.expectedTotalAdd !== null && batchAppendForm.expectedTotalAdd !== undefined"
                                placeholder="输入要减少的数量"
                                style="width: 300px;">
                            </el-input-number>
                        </el-form-item>

                        <el-form-item v-if="batchAppendChannelType === 'newmedia'" label="预计免费接待数追加">
                            <el-input-number
                                v-model="batchAppendForm.expectedFreeAdd"
                                :min="0"
                                :disabled="batchAppendForm.expectedFreeReduce !== null && batchAppendForm.expectedFreeReduce !== undefined"
                                placeholder="输入要追加的数量"
                                style="width: 300px;">
                            </el-input-number>
                        </el-form-item>

                        <el-form-item v-if="batchAppendChannelType === 'newmedia'" label="预计免费接待数减少">
                            <el-input-number
                                v-model="batchAppendForm.expectedFreeReduce"
                                :min="0"
                                :disabled="batchAppendForm.expectedFreeAdd !== null && batchAppendForm.expectedFreeAdd !== undefined"
                                placeholder="输入要减少的数量"
                                style="width: 300px;">
                            </el-input-number>
                        </el-form-item>

                        <el-form-item v-if="batchAppendChannelType === 'newmedia'" label="预计付费接待数追加">
                            <el-input-number
                                v-model="batchAppendForm.expectedPaidAdd"
                                :min="0"
                                :disabled="batchAppendForm.expectedPaidReduce !== null && batchAppendForm.expectedPaidReduce !== undefined"
                                placeholder="输入要追加的数量"
                                style="width: 300px;">
                            </el-input-number>
                        </el-form-item>

                        <el-form-item v-if="batchAppendChannelType === 'newmedia'" label="预计付费接待数减少">
                            <el-input-number
                                v-model="batchAppendForm.expectedPaidReduce"
                                :min="0"
                                :disabled="batchAppendForm.expectedPaidAdd !== null && batchAppendForm.expectedPaidAdd !== undefined"
                                placeholder="输入要减少的数量"
                                style="width: 300px;">
                            </el-input-number>
                        </el-form-item>
                    </el-form>
                </div>
                <template #footer>
                    <span class="dialog-footer">
                        <el-button @click="showBatchAppendDialog = false">取消</el-button>
                        <el-button type="primary" @click="applyBatchAppendEdit">立即应用</el-button>
                    </span>
                </template>
            </el-dialog>

            <!-- 创建分发规则对话框 -->
            <el-dialog
                title="创建分发规则"
                v-model="createDialogVisible"
                :close-on-click-modal="false"
                :close-on-press-escape="false"
                :modal-append-to-body="false"
                :append-to-body="true"
                :lock-scroll="true"
                class="create-rule-dialog"
                :before-close="handleCloseDialog">
                <div class="form-container">
                    <div class="channel-type-header">
                        <h3>选择渠道类型</h3>
                        <div class="channel-selection">
                            <el-checkbox-group v-model="selectedChannels" @change="handleChannelChange">
                                <el-checkbox label="电商渠道">电商渠道</el-checkbox>
                                <el-checkbox label="新媒体渠道">新媒体渠道</el-checkbox>
                                <el-checkbox label="公海渠道">公海渠道</el-checkbox>
                            </el-checkbox-group>
                        </div>
                    </div>
                    
                    <div class="group-selection-container" v-if="selectedChannels.length > 0">
                        <div class="group-selection-row" :class="{'single-channel': selectedChannels.length === 1}">
                            <div class="group-selection-col" v-if="selectedChannels.includes('电商渠道')">
                                <div class="alert-info">
                                    <i class="el-icon-info"></i> 请选择电商渠道分组
                                </div>
                                <div class="selection-actions">
                                    <el-button size="small" type="primary" @click="selectAllEcommerceGroups">全选</el-button>
                                    <el-button size="small" @click="clearEcommerceGroups">清空</el-button>
                                </div>
                                <el-select
                                    v-model="selectedEcommerceGroups"
                                    multiple
                                    filterable
                                    placeholder="选择电商渠道分组"
                                    style="width: 100%;"
                                    @change="handleEcommerceGroupChange"
                                >
                                    <el-option
                                        v-for="group in ecommerceGroups"
                                        :key="group.id"
                                        :label="group.name"
                                        :value="group.id"
                                        :disabled="group.disabled || getExistingGroups().has(group.name)"
                                    >
                                        <span :style="{ color: group.disabled || getExistingGroups().has(group.name) ? '#C0C4CC' : 'inherit' }">{{ group.name }}</span>
                                        <span v-if="group.disabled" style="float: right; color: #8492a6; font-size: 13px">
                                            (已被新媒体渠道选择)
                                        </span>
                                        <span v-else-if="getExistingGroups().has(group.name)" style="float: right; color: #8492a6; font-size: 13px">
                                            (表格中已存在)
                                        </span>
                                    </el-option>
                                </el-select>
                            </div>
                            <div class="group-selection-col" v-if="selectedChannels.includes('新媒体渠道')">
                                <div class="alert-info">
                                    <i class="el-icon-info"></i> 请选择新媒体渠道分组
                                </div>
                                <div class="selection-actions">
                                    <el-button size="small" type="primary" @click="selectAllNewMediaGroups">全选</el-button>
                                    <el-button size="small" @click="clearNewMediaGroups">清空</el-button>
                                </div>
                                <el-select
                                    v-model="selectedNewMediaGroups"
                                    multiple
                                    filterable
                                    placeholder="选择新媒体渠道分组"
                                    style="width: 100%;"
                                    @change="handleNewMediaGroupChange"
                                >
                                    <el-option
                                        v-for="group in newMediaGroups"
                                        :key="group.id"
                                        :label="group.name"
                                        :value="group.id"
                                        :disabled="group.disabled || getExistingGroups().has(group.name)"
                                    >
                                        <span :style="{ color: group.disabled || getExistingGroups().has(group.name) ? '#C0C4CC' : 'inherit' }">{{ group.name }}</span>
                                        <span v-if="group.disabled" style="float: right; color: #8492a6; font-size: 13px">
                                            (已被电商渠道选择)
                                        </span>
                                        <span v-else-if="getExistingGroups().has(group.name)" style="float: right; color: #8492a6; font-size: 13px">
                                            (表格中已存在)
                                        </span>
                                    </el-option>
                                </el-select>
                            </div>
                            <div class="group-selection-col" v-if="selectedChannels.includes('公海渠道')">
                                <div class="alert-info">
                                    <i class="el-icon-info"></i> 请选择公海渠道分组
                                </div>
                                <div class="selection-actions">
                                    <el-button size="small" type="primary" @click="selectAllGonghaiGroups">全选</el-button>
                                    <el-button size="small" @click="clearGonghaiGroups">清空</el-button>
                                </div>
                                <el-select
                                    v-model="selectedGonghaiGroups"
                                    multiple
                                    filterable
                                    placeholder="选择公海渠道分组"
                                    style="width: 100%;"
                                    @change="handleGonghaiGroupChange"
                                >
                                    <el-option
                                        v-for="group in gonghaiGroups"
                                        :key="group.id"
                                        :label="group.name"
                                        :value="group.id"
                                        :disabled="group.disabled || getExistingGroups().has(group.name)"
                                    >
                                        <span :style="{ color: group.disabled || getExistingGroups().has(group.name) ? '#C0C4CC' : 'inherit' }">{{ group.name }}</span>
                                        <span v-if="group.disabled" style="float: right; color: #8492a6; font-size: 13px">
                                            (已被其他渠道选择)
                                        </span>
                                        <span v-else-if="getExistingGroups().has(group.name)" style="float: right; color: #8492a6; font-size: 13px">
                                            (表格中已存在)
                                        </span>
                                    </el-option>
                                </el-select>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 分组规则设置区域 -->
                    <div v-if="selectedChannels.length > 0 && (selectedEcommerceGroups.length > 0 || selectedNewMediaGroups.length > 0 || selectedGonghaiGroups.length > 0)" class="rules-container">
                        <div class="rules-row" :class="{'single-channel': selectedChannels.length === 1}">
                            <!-- 电商渠道分组规则设置 -->
                            <div v-if="selectedChannels.includes('电商渠道') && selectedEcommerceGroups.length > 0" class="rule-settings-section">
                                <h3 class="section-title">
                                    <i class="el-icon-setting"></i>
                                    电商渠道分组规则设置
                                </h3>
                                
                                <div class="channel-reception">
                                    <span class="reception-label">电商渠道预期接待量：</span>
                                    <el-input-number 
                                        v-model="ecommerceReception" 
                                        :min="0" 
                                        :step="10"
                                        controls-position="right"
                                        style="width: 180px;"
                                    ></el-input-number>
                                    <span style="margin: 0 10px;">分配总量：</span>
                                    <div :class="['distribution-total-status', ecommerceDistributionStatus]">
                                        {{ ecommerceDistributionTotal }}
                                    </div>
                                </div>
                                
                                <div class="group-rules-scroll-container">
                                    <div class="group-rules">
                                        <div v-for="groupId in selectedEcommerceGroups" :key="'ecommerce-'+groupId" class="group-rule-card">
                                            <div class="group-rule-header">
                                                <span class="group-name">{{ getGroupName(groupId, ecommerceGroups) }}</span>
                                            </div>
                                            
                                            <div class="group-rule-body">
                                                <div class="rule-form-item">
                                                    <span class="rule-label">分配数量：</span>
                                                    <el-input-number 
                                                        :model-value="ecommerceRulesForms[groupId] && ecommerceRulesForms[groupId][0] ? ecommerceRulesForms[groupId][0].distribution_ratio : 0" 
                                                        :min="0" 
                                                        :step="10"
                                                        controls-position="right"
                                                        style="width: 100px;"
                                                        @change="(val) => updateRuleFormValue(groupId, 'ecommerce', 'distribution_ratio', val)"
                                                    ></el-input-number>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- 新媒体渠道分组规则设置 -->
                            <div v-if="selectedChannels.includes('新媒体渠道') && selectedNewMediaGroups.length > 0" class="rule-settings-section">
                                <h3 class="section-title">
                                    <i class="el-icon-setting"></i>
                                    新媒体渠道分组规则设置
                                </h3>
                                
                                <div class="channel-reception">
                                    <span class="reception-label">新媒体渠道预期接待量：</span>
                                    <el-input-number 
                                        v-model="newMediaReception" 
                                        :min="0" 
                                        :step="10"
                                        controls-position="right"
                                        style="width: 180px;"
                                    ></el-input-number>
                                    <span style="margin: 0 10px;">分配总量：</span>
                                    <div :class="['distribution-total-status', newMediaDistributionStatus]">
                                        {{ newMediaDistributionTotal }}
                                    </div>
                                </div>
                                
                                <div class="group-rules-scroll-container">
                                    <div class="group-rules">
                                        <div v-for="groupId in selectedNewMediaGroups" :key="'newmedia-'+groupId" class="group-rule-card">
                                            <div class="group-rule-header">
                                                <span class="group-name">{{ getGroupName(groupId, newMediaGroups) }}</span>
                                            </div>
                                            
                                            <div class="group-rule-body">
                                                <div class="rule-form-item">
                                                    <span class="rule-label">分配数量：</span>
                                                    <el-input-number 
                                                        :model-value="newMediaRulesForms[groupId] && newMediaRulesForms[groupId][0] ? newMediaRulesForms[groupId][0].distribution_ratio : 0" 
                                                        :min="0" 
                                                        :step="10"
                                                        controls-position="right"
                                                        style="width: 100px;"
                                                        @change="(val) => updateRuleFormValue(groupId, 'newmedia', 'distribution_ratio', val)"
                                                    ></el-input-number>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- 公海渠道分组规则设置 -->
                            <div v-if="selectedChannels.includes('公海渠道') && selectedGonghaiGroups.length > 0" class="rule-settings-section">
                                <h3 class="section-title">
                                    <i class="el-icon-setting"></i>
                                    公海渠道分组规则设置
                                </h3>

                                <div class="channel-reception">
                                    <span class="reception-label">公海渠道预期接待量：</span>
                                    <el-input-number
                                        v-model="gonghaiReception"
                                        :min="0"
                                        :step="10"
                                        controls-position="right"
                                        style="width: 180px;"
                                    ></el-input-number>
                                    <span style="margin: 0 10px;">分配总量：</span>
                                    <div :class="['distribution-total-status', gonghaiDistributionStatus]">
                                        {{ gonghaiDistributionTotal }}
                                    </div>
                                </div>

                                <div class="group-rules-scroll-container">
                                    <div class="group-rules">
                                        <div v-for="groupId in selectedGonghaiGroups" :key="'gonghai-'+groupId" class="group-rule-card">
                                            <div class="group-rule-header">
                                                <span class="group-name">{{ getGroupName(groupId, gonghaiGroups) }}</span>
                                            </div>

                                            <div class="group-rule-body">
                                                <div class="rule-form-item">
                                                    <span class="rule-label">分配数量：</span>
                                                    <el-input-number
                                                        :model-value="gonghaiRulesForms[groupId] && gonghaiRulesForms[groupId][0] ? gonghaiRulesForms[groupId][0].distribution_ratio : 0"
                                                        :min="0"
                                                        :step="10"
                                                        controls-position="right"
                                                        style="width: 100px;"
                                                        @change="(val) => updateRuleFormValue(groupId, 'gonghai', 'distribution_ratio', val)"
                                                    ></el-input-number>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <template #footer>
                    <span class="dialog-footer">
                        <el-button @click="handleCloseDialog">取消</el-button>
                        <el-button type="primary" @click="submitCreateForm">确认</el-button>
                    </span>
                </template>
            </el-dialog>

            <!-- 复制规则对话框 -->
            <el-dialog
                v-model="copyDialogVisible"
                title="复制分发规则"
                width="400px"
                :append-to-body="true"
                :lock-scroll="true"
                class="copy-rule-dialog"
                @close="handleCloseCopyDialog">
                <div class="form-container">
                    <p>请选择源日期：</p>
                    <el-date-picker
                        v-model="copyForm.source_date"
                        type="date"
                        placeholder="选择源日期"
                        format="YYYY-MM-DD"
                        value-format="YYYY-MM-DD"
                        @change="handleCopyDateChange">
                    </el-date-picker>
                    
                    <p style="margin-top: 15px;">请选择目标日期：</p>
                    <el-date-picker
                        v-model="copyForm.target_date"
                        type="date"
                        placeholder="选择目标日期"
                        format="YYYY-MM-DD"
                        value-format="YYYY-MM-DD">
                    </el-date-picker>
        </div>
                <template #footer>
                    <span class="dialog-footer">
                        <el-button @click="copyDialogVisible = false">取消</el-button>
                        <el-button type="primary" @click="submitCopyForm">确认</el-button>
                    </span>
                </template>
            </el-dialog>

            <!-- 编辑规则对话框 -->
            <el-dialog
                v-model="editDialogVisible"
                title="编辑分发规则"
                width="400px"
                :append-to-body="true"
                :lock-scroll="true"
                class="edit-rule-dialog"
                @close="handleCloseEditDialog">
                <div class="form-container">
                    <p>请选择编辑规则：</p>
                    <el-select v-model="editForm.id" placeholder="请选择规则">
                        <el-option
                            v-for="rule in rules"
                            :key="rule.id"
                            :label="rule.group_name"
                            :value="rule.id">
                        </el-option>
                    </el-select>
                </div>
                <template #footer>
                    <span class="dialog-footer">
                        <el-button @click="editDialogVisible = false">取消</el-button>
                        <el-button type="primary" @click="submitEditForm">确认</el-button>
                    </span>
                </template>
            </el-dialog>

            <!-- 添加行对话框 -->
            <el-dialog
                v-model="addRowDialogVisible"
                title="添加行"
                width="400px"
                :append-to-body="true"
                :lock-scroll="true"
                class="add-row-dialog"
                @close="handleCloseAddRowDialog">
                <div class="form-container">
                    <p>请选择添加位置：</p>
                    <el-radio-group v-model="addRowPosition">
                        <el-radio label="before">在当前行上方添加</el-radio>
                        <el-radio label="after">在当前行下方添加</el-radio>
                    </el-radio-group>
                </div>
                <template #footer>
                    <span class="dialog-footer">
                        <el-button @click="addRowDialogVisible = false">取消</el-button>
                        <el-button type="primary" @click="addNewRow">确定</el-button>
                    </span>
                </template>
            </el-dialog>
            
            <!-- 店铺选择对话框 -->
            <el-dialog
                v-model="storeSelectionDialogVisible"
                title="选择店铺"
                width="70%"
                :before-close="cancelStoreSelection"
                destroy-on-close
                :append-to-body="true"
                :lock-scroll="true"
                :close-on-click-modal="false"
                class="store-selection-dialog"
                center
            >
                <div class="store-selection-container">
                    <!-- 搜索框 -->
                    <el-input
                        v-model="storeSearchKeyword"
                        placeholder="搜索店铺名称或缩写"
                        clearable
                        style="margin-bottom: 15px;"
                    >
                        <template #prefix>
                            <i class="fas fa-search"></i>
                        </template>
                    </el-input>
                    
                    <!-- 店铺列表表格 -->
                    <el-table
                        :data="filteredStores"
                        border
                        stripe
                        style="width: 100%; margin-bottom: 15px;"
                        height="350px"
                        :header-cell-style="{background:'#f5f7fa',color:'#000000',fontWeight:'bold'}"
                        highlight-current-row>
                        <el-table-column
                            type="index"
                            label="序号"
                            width="60"
                            align="center">
                        </el-table-column>
                        <el-table-column
                            prop="manager"
                            label="负责人"
                            width="120"
                            align="center">
                        </el-table-column>
                        <el-table-column
                            prop="name"
                            label="店铺名称"
                            width="200"
                            align="center">
                        </el-table-column>
                        <el-table-column
                            label="抖音缩写"
                            width="120"
                            align="center">
                            <template #default="scope">
                                <el-button 
                                    :class="{'selected-store': isStoreSelected(scope.row, 'douyin_abbr')}"
                                    link
                                    type="primary"
                                    @click="selectStore(scope.row, 'douyin_abbr')">
                                    {{ scope.row.douyin_abbr || '-' }}
                                </el-button>
                            </template>
                        </el-table-column>
                        <el-table-column
                            label="视频号缩写"
                            width="120"
                            align="center">
                            <template #default="scope">
                                <el-button
                                    :class="{'selected-store': isStoreSelected(scope.row, 'video_abbr')}"
                                    link
                                    type="primary"
                                    @click="selectStore(scope.row, 'video_abbr')">
                                    {{ scope.row.video_abbr || '-' }}
                                </el-button>
                            </template>
                        </el-table-column>
                        <el-table-column
                            label="小红书缩写"
                            width="120"
                            align="center">
                            <template #default="scope">
                                <el-button
                                    :class="{'selected-store': isStoreSelected(scope.row, 'xiaohongshu_abbr')}"
                                    link
                                    type="primary"
                                    @click="selectStore(scope.row, 'xiaohongshu_abbr')">
                                    {{ scope.row.xiaohongshu_abbr || '-' }}
                                </el-button>
                            </template>
                        </el-table-column>
                        <el-table-column
                            label="快手缩写"
                            width="120"
                            align="center">
                            <template #default="scope">
                                <el-button
                                    :class="{'selected-store': isStoreSelected(scope.row, 'kuaishou_abbr')}"
                                    link
                                    type="primary"
                                    @click="selectStore(scope.row, 'kuaishou_abbr')">
                                    {{ scope.row.kuaishou_abbr || '-' }}
                                </el-button>
                            </template>
                        </el-table-column>
                    </el-table>
                    
                    <!-- 已选择店铺列表 -->
                    <div class="selected-stores-section">
                        <div class="selected-stores-header">
                            <div>已选择 {{ selectedStores.length }} 家店铺:</div>
                            <el-button 
                                v-if="selectedStores.length > 0" 
                                type="danger" 
                                size="small" 
                                link
                                @click="clearSelectedStores"
                            >清空</el-button>
                        </div>
                        <div class="selected-stores-tags" v-if="selectedStores.length > 0">
                            <el-tag
                                v-for="store in selectedStores"
                                :key="store"
                                closable
                                @close="removeSelectedStore(store)"
                                class="store-tag"
                                size="small"
                            >
                                {{ store }}
                            </el-tag>
                        </div>
                        <el-empty v-else description="未选择任何店铺" :image-size="60"></el-empty>
                    </div>
                </div>
                
                <template #footer>
                    <span class="dialog-footer">
                        <el-button @click="cancelStoreSelection">取消</el-button>
                        <el-button type="primary" @click="confirmStoreSelection">确定</el-button>
                    </span>
                </template>
            </el-dialog>
        </div>
    </div>
    <script src="/static/js/fetch.js"></script>
    <script src="/static/js/vue.global.js"></script>
    <script src="/static/js/element-plus.js"></script>
    <script src="/static/js/axios.min.js"></script>

    <script>
        // 资源加载检测和Loading控制类
        class LoadingManager {
            constructor(pageTitle = '系统') {
                this.loadingOverlay = document.getElementById('loading-overlay');
                this.loadingStatus = document.getElementById('loading-status');
                this.loadingText = document.querySelector('.loading-text');
                this.pageTitle = pageTitle;
                this.startTime = performance.now();

                // 设置页面标题
                if (this.loadingText) {
                    this.loadingText.textContent = `正在加载${this.pageTitle}`;
                }

                // 设置简单的加载状态
                if (this.loadingStatus) {
                    this.loadingStatus.textContent = '正在加载资源';
                }
            }

            // 等待单个资源加载
            waitForResource(checkFunction, resourceName, timeout = 5000) {
                return new Promise((resolve, reject) => {
                    const startTime = Date.now();

                    const check = () => {
                        if (checkFunction()) {
                            console.log(`✅ ${resourceName} 已加载`);
                            resolve(resourceName);
                        } else if (Date.now() - startTime > timeout) {
                            console.error(`❌ ${resourceName} 加载超时`);
                            reject(new Error(`${resourceName} 加载超时`));
                        } else {
                            setTimeout(check, 50);
                        }
                    };

                    check();
                });
            }

            // 开始加载检测
            async startLoading(customResources = []) {
                try {
                    console.log(`🚀 开始检测${this.pageTitle}资源加载状态...`);

                    // 默认资源检测
                    const defaultChecks = [
                        { check: () => typeof Vue !== 'undefined', name: 'Vue.js' },
                        { check: () => typeof ElementPlus !== 'undefined', name: 'Element Plus' },
                        { check: () => typeof axios !== 'undefined', name: 'Axios' }
                    ];

                    // 合并自定义资源检测
                    const allChecks = [...defaultChecks, ...customResources];

                    // 逐个检测资源
                    for (const resource of allChecks) {
                        await this.waitForResource(resource.check, resource.name);
                    }

                    // 额外等待确保所有资源完全就绪
                    await new Promise(resolve => setTimeout(resolve, 300));

                    const loadingTime = performance.now() - this.startTime;
                    console.log(`✅ ${this.pageTitle}所有资源加载完成，耗时: ${loadingTime.toFixed(2)}ms`);

                    // 隐藏loading并初始化应用
                    this.hideLoading();
                    this.onLoadingComplete();

                } catch (error) {
                    console.error(`❌ ${this.pageTitle}资源加载失败:`, error);
                    this.showError(error.message);
                }
            }

            // 隐藏loading动画
            hideLoading() {
                if (this.loadingOverlay) {
                    this.loadingOverlay.classList.add('fade-out');
                    setTimeout(() => {
                        this.loadingOverlay.style.display = 'none';
                    }, 500);
                }
            }

            // 显示错误信息
            showError(message) {
                if (this.loadingStatus) {
                    this.loadingStatus.textContent = `加载失败: ${message}`;
                }
                if (this.loadingOverlay) {
                    this.loadingOverlay.style.background = 'linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%)';
                }

                // 5秒后尝试重新加载
                setTimeout(() => {
                    location.reload();
                }, 5000);
            }

            // 加载完成回调（可被重写）
            onLoadingComplete() {
                console.log(`🎉 ${this.pageTitle}初始化完成`);
            }
        }

        // 页面加载完成后开始检测
        document.addEventListener('DOMContentLoaded', function() {
            const loadingManager = new LoadingManager('分发规则管理');
            loadingManager.startLoading();
        });
    </script>

    <script>
        const { createApp, ref, reactive, computed, onMounted, watch, nextTick } = Vue;
        
        const app = createApp({
            setup() {
                // 配置axios默认请求头
                const setupAxios = async () => {
                    // 获取存储的API密钥
                    let secureKey = localStorage.getItem('secure_key');
                    
                    if (!secureKey) {
                        // 如果没有密钥，先获取密钥
                        try {
                            const response = await fetch('/api/secure_key', {
                                method: 'GET',
                                headers: {
                                    'Content-Type': 'application/json',
                                    'company': 'jumosheji'
                                }
                            });
                            
                            if (response.ok) {
                                const data = await response.json();
                                secureKey = btoa(data.key || '');
                                localStorage.setItem('secure_key', secureKey);
                            }
                        } catch (error) {
                            console.error('获取API密钥失败:', error);
                        }
                    }
                    
                    if (secureKey) {
                        // 设置axios默认请求头
                        axios.defaults.headers.common['key'] = secureKey;
                        console.log('API密钥已设置');
                        
                        // 添加响应拦截器来处理错误
                        axios.interceptors.response.use(
                            response => response,
                            error => {
                                console.error('API请求错误:', error);
                                if (error.response) {
                                    console.error('错误状态码:', error.response.status);
                                    console.error('错误数据:', error.response.data);
                                    console.error('错误配置:', error.config);
                                    
                                    // 如果是API密钥相关错误，尝试重新获取密钥
                                    if (error.response.status === 401 || error.response.status === 403) {
                                        console.log('API密钥可能已过期，尝试重新获取...');
                                        localStorage.removeItem('secure_key');
                                        // 递归调用setupAxios重新设置密钥
                                        setTimeout(() => setupAxios(), 1000);
                                    }
                                }
                                return Promise.reject(error);
                            }
                        );
                    }
                };

                // 立即执行axios配置
                setupAxios();

                // 日期格式化函数
                const formatDate = (dateString) => {
                    if (!dateString) return '';
                    const date = new Date(dateString);
                    const year = date.getFullYear();
                    const month = String(date.getMonth() + 1).padStart(2, '0');
                    const day = String(date.getDate()).padStart(2, '0');
                    return `${year}-${month}-${day}`;
                };

                // 基础数据
                const currentDate = ref(formatDate(new Date())); // 当前日期，默认为今天
                const loading = ref(true);
                const hasRules = ref(false);
                
                const createDialogVisible = ref(false);
                const copyDialogVisible = ref(false);
                const editDialogVisible = ref(false);
                const addRowDialogVisible = ref(false); // 添加行对话框可见性
                const addRowPosition = ref('after'); // 添加行位置：before或after
                const currentRow = ref(null); // 当前选中的行
                const activeTab = ref('ecommerce');
                const apiError = ref(false);



                // 表格多选相关数据
                const selectedEcommerceRows = ref([]);
                const selectedNewMediaRows = ref([]);
                const selectedGonghaiRows = ref([]);

                // 批量修改排班相关数据
                const showQuickBatchDialog = ref(false);
                const quickBatchChannelType = ref('ecommerce');
                const quickBatchSelectedRows = ref([]);
                const quickBatchForm = reactive({
                    member: '',
                    shift: '',
                    expectedTotal: null,
                    expectedFree: null,
                    expectedPaid: null,
                    selectedStores: []
                });

                // 批量追加相关数据
                const showBatchAppendDialog = ref(false);
                const batchAppendChannelType = ref('ecommerce');
                const batchAppendSelectedRows = ref([]);
                const batchAppendForm = reactive({
                    expectedTotalAdd: null,
                    expectedTotalReduce: null,
                    expectedFreeAdd: null,
                    expectedFreeReduce: null,
                    expectedPaidAdd: null,
                    expectedPaidReduce: null
                });

                // 规则数据
                const allRules = ref([]);
                const ecommerceRules = ref([]);
                const newMediaRules = ref([]);
                const gonghaiRules = ref([]);
                const ecommerceReception = ref({});
                const newMediaReception = ref({});
                const gonghaiReception = ref({});
                const hasEcommerceRules = ref(false);
                const hasNewMediaRules = ref(false);
                const hasGonghaiRules = ref(false);
                
                // 分组分配数量数据
                const groupDistributionRatios = ref([]);
                
                // 创建规则相关数据
                const selectedChannels = ref([]);
                const ecommerceGroups = ref([]);
                const newMediaGroups = ref([]);
                const gonghaiGroups = ref([]);
                const selectedEcommerceGroups = ref([]);
                const selectedNewMediaGroups = ref([]);
                const selectedGonghaiGroups = ref([]);
                
                // 渠道接待数表单
                const ecommerceReceptionForm = reactive({
                    expected_reception: 0,
                    expected_free_reception: 0,
                    expected_paid_reception: 0
                });
                
                const newMediaReceptionForm = reactive({
                    expected_reception: 0,
                    expected_free_reception: 0,
                    expected_paid_reception: 0
                });

                const gonghaiReceptionForm = reactive({
                    expected_reception: 0,
                    expected_free_reception: 0,
                    expected_paid_reception: 0
                });

                // 分发规则表单
                const ecommerceRulesForms = reactive({});
                const newMediaRulesForms = reactive({});
                const gonghaiRulesForms = reactive({});
                
                // 分配总量计算
                const ecommerceDistributionTotal = computed(() => {
                    let total = 0;
                    if (selectedEcommerceGroups.value && selectedEcommerceGroups.value.length > 0) {
                        selectedEcommerceGroups.value.forEach(groupId => {
                            if (ecommerceRulesForms[groupId] && ecommerceRulesForms[groupId][0]) {
                                total += Number(ecommerceRulesForms[groupId][0].distribution_ratio || 0);
                            }
                        });
                    }
                    return total;
                });
                
                const newMediaDistributionTotal = computed(() => {
                    let total = 0;
                    if (selectedNewMediaGroups.value && selectedNewMediaGroups.value.length > 0) {
                        selectedNewMediaGroups.value.forEach(groupId => {
                            if (newMediaRulesForms[groupId] && newMediaRulesForms[groupId][0]) {
                                total += Number(newMediaRulesForms[groupId][0].distribution_ratio || 0);
                            }
                        });
                    }
                    return total;
                });

                const gonghaiDistributionTotal = computed(() => {
                    let total = 0;
                    if (selectedGonghaiGroups.value && selectedGonghaiGroups.value.length > 0) {
                        selectedGonghaiGroups.value.forEach(groupId => {
                            if (gonghaiRulesForms[groupId] && gonghaiRulesForms[groupId][0]) {
                                total += Number(gonghaiRulesForms[groupId][0].distribution_ratio || 0);
                            }
                        });
                    }
                    return total;
                });

                // 分配总量状态计算
                const ecommerceDistributionStatus = computed(() => {
                    const total = ecommerceDistributionTotal.value;
                    const expected = ecommerceReception.value;
                    
                    if (total < expected) return 'warning';
                    if (total === expected) return 'success';
                    if (total > expected) return 'danger';
                    return '';
                });
                
                const newMediaDistributionStatus = computed(() => {
                    const total = newMediaDistributionTotal.value;
                    const expected = newMediaReception.value;

                    if (total < expected) return 'warning';
                    if (total === expected) return 'success';
                    if (total > expected) return 'danger';
                    return '';
                });

                const gonghaiDistributionStatus = computed(() => {
                    const total = gonghaiDistributionTotal.value;
                    const expected = gonghaiReception.value;

                    if (total < expected) return 'warning';
                    if (total === expected) return 'success';
                    if (total > expected) return 'danger';
                    return '';
                });

                // 预设数据
                const groupLeaders = reactive({});
                const groupMembers = reactive({});
                const stores = ref([]);
                const allSalesGroups = ref([]);
                
                // 复制规则表单
                const copyForm = reactive({
                    source_date: '',
                    target_date: ''
                });
                
                // 编辑表单
                const editForm = reactive({
                    id: null,
                    date: '',
                    channel: '',
                    group_id: null,
                    group_name: '',
                    leader_id: null,
                    member_id: null,
                    store_id: null,
                    shift: '',
                    expected_reception: 0,
                    start_time: '',
                    end_time: ''
                });
                
                // 准备分发规则表单
                const prepareRulesForms = async () => {
                    // 清空旧数据
                    Object.keys(ecommerceRulesForms).forEach(key => {
                        delete ecommerceRulesForms[key];
                    });
                    
                    Object.keys(newMediaRulesForms).forEach(key => {
                        delete newMediaRulesForms[key];
                    });
                    
                    // 获取店铺数据
                    await fetchStores();
                    
                    // 获取分组的负责人和成员数据
                    await fetchGroupLeadersAndMembers();
                    
                    // 初始化电商渠道分发规则表单
                    for (const groupId of selectedEcommerceGroups.value) {
                        ecommerceRulesForms[groupId] = [];
                        
                        // 获取该分组的成员
                        const members = getGroupMembers(groupId, 'ecommerce');
                        const leader = getGroupLeaders(groupId, 'ecommerce')[0]?.name || '';
                        
                        // 为每个成员创建一个表单
                        for (const member of members) {
                            addRuleForm(groupId, 'ecommerce', {
                                member: member.name,
                                leader: leader
                            });
                        }
                    }
                    
                    // 初始化新媒体渠道分发规则表单
                    for (const groupId of selectedNewMediaGroups.value) {
                        newMediaRulesForms[groupId] = [];
                        
                        // 获取该分组的成员
                        const members = getGroupMembers(groupId, 'newmedia');
                        const leader = getGroupLeaders(groupId, 'newmedia')[0]?.name || '';
                        
                        // 为每个成员创建一个表单
                        for (const member of members) {
                            addRuleForm(groupId, 'newmedia', {
                                member: member.name,
                                leader: leader
                            });
                        }
                    }
                };
                
                // 获取店铺数据
                const fetchStores = async () => {
                    try {
                        const response = await axios.get('/api/presets/shops/detail');
                        stores.value = response.data.map(shop => ({
                            id: shop.id,
                            name: shop.name
                        })) || [];
                    } catch (error) {
                        console.error('获取店铺数据失败:', error);
                        ElementPlus.ElMessage.error('获取店铺数据失败: ' + (error.response?.data?.detail || error.message));
                        stores.value = [];
                    }

                    try {
                        const response = await axios.get('/api/presets/shops/detail');
                        stores.value = response.data.map(shop => ({
                            id: shop.id,
                            name: shop.name,
                            manager: shop.manager || '',
                            douyin_abbr: shop.douyin_abbr || '',
                            video_abbr: shop.video_abbr || '',
                            xiaohongshu_abbr: shop.xiaohongshu_abbr || '',
                            kuaishou_abbr: shop.kuaishou_abbr || ''
                        })) || [];
                        return true;
                    } catch (error) {
                        console.error('获取店铺数据失败:', error);
                        ElementPlus.ElMessage.error('获取店铺数据失败: ' + (error.response?.data?.detail || error.message));
                        stores.value = [];
                        return false;
                    }
                };
                
                // 获取分组的负责人和成员数据
                const fetchGroupLeadersAndMembers = async () => {
                    try {
                        // 获取所有销售组数据
                        const response = await axios.get('/api/presets/sales');
                        const salesGroups = response.data.sales || [];
                        
                        // 清空现有数据
                        Object.keys(groupLeaders).forEach(key => delete groupLeaders[key]);
                        Object.keys(groupMembers).forEach(key => delete groupMembers[key]);
                        
                        // 处理电商渠道分组
                        for (const groupId of selectedEcommerceGroups.value) {
                            // 查找对应的销售组
                            const groupData = salesGroups.find(group => group.id === groupId) || {};
                            
                            // 设置负责人数据
                            const leaderName = groupData.leader || '';
                            groupLeaders[`ecommerce-${groupId}`] = [{
                                id: 'leader-' + groupId,
                                name: leaderName
                            }];
                            
                            // 设置成员数据 - 将逗号分隔的成员字符串转换为数组
                            const membersStr = groupData.members || '';
                            const memberNames = membersStr.split(',').filter(name => name.trim() !== '');
                            groupMembers[`ecommerce-${groupId}`] = memberNames.map((name, index) => ({
                                id: `member-${groupId}-${index}`,
                                name: name.trim()
                            }));
                        }
                        
                        // 处理新媒体渠道分组
                        for (const groupId of selectedNewMediaGroups.value) {
                            // 查找对应的销售组
                            const groupData = salesGroups.find(group => group.id === groupId) || {};
                            
                            // 设置负责人数据
                            const leaderName = groupData.leader || '';
                            groupLeaders[`newmedia-${groupId}`] = [{
                                id: 'leader-' + groupId,
                                name: leaderName
                            }];
                            
                            // 设置成员数据 - 将逗号分隔的成员字符串转换为数组
                            const membersStr = groupData.members || '';
                            const memberNames = membersStr.split(',').filter(name => name.trim() !== '');
                            groupMembers[`newmedia-${groupId}`] = memberNames.map((name, index) => ({
                                id: `member-${groupId}-${index}`,
                                name: name.trim()
                            }));
                        }
                        
                        console.log('获取分组负责人和成员数据成功:', { groupLeaders, groupMembers });
                    } catch (error) {
                        console.error('获取分组负责人和成员数据失败:', error);
                        ElementPlus.ElMessage.error('获取分组负责人和成员数据失败: ' + (error.response?.data?.detail || error.message));
                        
                        // 确保即使出错也有空数据结构
                        for (const groupId of selectedEcommerceGroups.value) {
                            groupLeaders[`ecommerce-${groupId}`] = [];
                            groupMembers[`ecommerce-${groupId}`] = [];
                        }
                        
                        for (const groupId of selectedNewMediaGroups.value) {
                            groupLeaders[`newmedia-${groupId}`] = [];
                            groupMembers[`newmedia-${groupId}`] = [];
                        }
                    }
                };
                
                // 获取分组名称
                const getGroupName = (groupId, groupsList) => {
                    // 确保groupsList是数组且不为空
                    if (!groupsList || !Array.isArray(groupsList)) {
                        console.warn(`获取分组名称失败: groupsList不是有效数组`, groupsList);
                        return `未知分组(${groupId})`;
                    }
                    
                    // 确保groupId是有效值
                    if (groupId === null || groupId === undefined) {
                        console.warn(`获取分组名称失败: groupId无效`, groupId);
                        return '未知分组';
                    }
                    
                    // 查找分组
                    const group = groupsList.find(g => g && g.id === groupId);
                    return group && group.name ? group.name : `未知分组(${groupId})`;
                };
                
                // 获取分组负责人
                const getGroupLeaders = (groupId, channelType) => {
                    return groupLeaders[`${channelType}-${groupId}`] || [];
                };
                
                // 获取表格中已存在的分组
                const getExistingGroups = () => {
                    const existingGroups = new Set();
                    if (ecommerceRules.value && ecommerceRules.value.length > 0) {
                        ecommerceRules.value.forEach(rule => {
                            existingGroups.add(rule.group_name);
                        });
                    }
                    if (newMediaRules.value && newMediaRules.value.length > 0) {
                        newMediaRules.value.forEach(rule => {
                            existingGroups.add(rule.group_name);
                        });
                    }
                    return existingGroups;
                };
                
                // 获取分组成员
                const getGroupMembers = (groupNameOrId, channelType) => {
                    console.log(`获取分组成员: ${groupNameOrId}, ${channelType}`);
                    
                    // 如果传入的是分组ID，直接从缓存中获取
                    if (groupMembers[`${channelType}-${groupNameOrId}`]) {
                        return groupMembers[`${channelType}-${groupNameOrId}`] || [];
                    }
                    
                    // 如果传入的是分组名称，需要先找到对应的分组ID
                    const groups = channelType === 'ecommerce' ? ecommerceGroups.value :
                                 (channelType === 'gonghai' ? gonghaiGroups.value : newMediaGroups.value);
                    const group = groups.find(g => g.name === groupNameOrId);
                    
                    if (group && groupMembers[`${channelType}-${group.id}`]) {
                        return groupMembers[`${channelType}-${group.id}`] || [];
                    }
                    
                    // 如果缓存中没有数据，尝试从销售预设中获取
                    let matchedSalesGroup = null;
                    const salesGroups = allSalesGroups.value || [];
                    
                    // 首先按ID匹配
                    if (groupNameOrId && typeof groupNameOrId === 'string' && groupNameOrId.length > 0) {
                        matchedSalesGroup = salesGroups.find(sg => sg.id === groupNameOrId);
                        
                        // 如果按ID没找到，尝试按名称匹配
                        if (!matchedSalesGroup) {
                            matchedSalesGroup = salesGroups.find(sg => sg.group === groupNameOrId);
                        }
                    }
                    
                    if (matchedSalesGroup) {
                        console.log('找到销售组数据:', matchedSalesGroup);
                        const membersStr = matchedSalesGroup.members || '';
                        const memberNames = membersStr.split(',').filter(name => name.trim() !== '');
                        const membersData = memberNames.map((name, index) => ({
                            id: `member-${matchedSalesGroup.id}-${index}`,
                            name: name.trim()
                        }));
                        
                        // 如果有分组ID，缓存结果
                        if (group) {
                            groupMembers[`${channelType}-${group.id}`] = membersData;
                        }
                        
                        return membersData;
                    }
                    
                    // 如果在预设数据中找不到，尝试从表格数据中查找
                    const tableData = channelType === 'ecommerce' ? ecommerceRules.value : newMediaRules.value;
                    const groupRules = tableData.filter(rule => rule.group_name === groupNameOrId);
                    
                    if (groupRules.length > 0) {
                        // 从规则中提取所有不同的成员名称
                        const memberNames = [...new Set(groupRules.map(rule => rule.member).filter(name => name))];
                        return memberNames.map((name, index) => ({
                            id: `member-${groupNameOrId}-${index}`,
                            name: name
                        }));
                    }
                    
                    console.warn(`未找到分组 ${groupNameOrId} 的成员数据`);
                    return [];
                };
                
                // 添加规则表单
                const addRuleForm = (groupId, channelType, initialData = {}) => {
                    const defaultForm = {
                        group_name: getGroupName(groupId, channelType),
                        leader: '',
                        member: '',
                        shift: '',
                        expected_reception: 0,
                        expected_free_reception: 0,
                        expected_paid_reception: 0,
                        store: stores.value.length > 0 ? stores.value[0].name : '',
                        time_range_start: new Date(new Date().setHours(9, 0, 0, 0)),
                        time_range_end: new Date(new Date().setHours(18, 0, 0, 0)),
                        remarks: ''
                    };
                    
                    const formData = { ...defaultForm, ...initialData };
                    
                    if (channelType === 'ecommerce') {
                        if (!ecommerceRulesForms[groupId]) {
                            ecommerceRulesForms[groupId] = [];
                        }
                        ecommerceRulesForms[groupId].push(formData);
                    } else {
                        if (!newMediaRulesForms[groupId]) {
                            newMediaRulesForms[groupId] = [];
                        }
                        newMediaRulesForms[groupId].push(formData);
                    }
                };
                
                // 移除规则表单
                const removeRuleForm = (groupId, index, channelType) => {
                    if (channelType === 'ecommerce') {
                        ecommerceRulesForms[groupId].splice(index, 1);
                    } else {
                        newMediaRulesForms[groupId].splice(index, 1);
                    }
                };
                
                // 更新规则接待数
                const updateRuleReception = (row, channelType) => {
                    if (row?.expected_reception != null && row?.expected_free_reception != null) {
                        row.expected_paid_reception = Math.max(0, row.expected_reception - row.expected_free_reception);
                    }
                };
                
                // 计算属性：是否可以进行下一步
                const canProceed = computed(() => {
                    if (selectedChannels.value.length === 0) {
                        return false;
                    }
                    
                    if (selectedChannels.value.includes('电商渠道') && selectedEcommerceGroups.value.length === 0) {
                        return false;
                    }
                    
                    if (selectedChannels.value.includes('新媒体渠道') && selectedNewMediaGroups.value.length === 0) {
                        return false;
                    }
                    
                    return true;
                });
                
                // 计算属性：是否可以复制
                const canCopy = computed(() => {
                    return copyForm.source_date && copyForm.target_date;
                });
                
                // 计算属性：是否可以保存编辑
                const canEdit = computed(() => {
                    return editForm.leader_id && 
                           editForm.member_id && 
                           editForm.shift && 
                           editForm.expected_reception > 0 && 
                           editForm.start_time && 
                           editForm.end_time;
                });
                
                // 编辑规则
                const editRule = (rule) => {
                    // 填充编辑表单
                    editForm.id = rule.id;
                    editForm.date = rule.date;
                    editForm.channel = rule.channel;
                    editForm.group_id = rule.group_id;
                    editForm.group_name = rule?.group_name;
                    editForm.leader_id = rule?.leader_id;
                    editForm.member_id = rule?.member_id;
                    editForm.store_id = rule?.store_id;
                    editForm.shift = rule?.shift;
                    editForm.expected_reception = rule?.expected_reception;
                    editForm.start_time = rule?.start_time;
                    editForm.end_time = rule?.end_time;
                    
                    // 显示编辑对话框
                    editDialogVisible.value = true;
                    
                    // 确保组成员数据已加载
                    fetchGroupMembers(rule.group_id);
                };
                
                // 获取组成员数据
                const fetchGroupMembers = async (groupId) => {
                    if (!groupMembers[groupId]) {
                        try {
                            const response = await axios.get(`/api/groups/${groupId}/members`);
                            groupMembers[groupId] = response.data || [];
                        } catch (error) {
                            console.error('获取组成员失败:', error);
                            ElementPlus.ElMessage.error('获取组成员失败: ' + (error.response?.data?.detail || error.message));
                        }
                    }
                };
                
                // 提交编辑表单
                const submitEditForm = async () => {
                    try {
                        // 提交数据
                        await axios.put(`/api/distribution/rules/${editForm.id}`, {
                            channel_type: editForm.channel,
                            group_name: editForm.group_name,
                            leader: editForm.leader_id,
                            member: editForm.member_id,
                            store: editForm.store_id,
                            shift: editForm.shift,
                            expected_reception: editForm.expected_reception,
                            expected_free_reception: Math.floor(editForm.expected_reception * 0.7), // 假设免费接待数为总接待数的70%
                            expected_paid_reception: Math.floor(editForm.expected_reception * 0.3), // 假设付费接待数为总接待数的30%
                            time_range_start: editForm.start_time,
                            time_range_end: editForm.end_time
                        });
                        
                        // 提示成功
                        ElementPlus.ElMessage.success('更新分发规则成功');
                        
                        // 关闭对话框
                        handleCloseDialog();
                        
                        // 重新获取规则列表
                        fetchRules();
                    } catch (error) {
                        console.error('更新分发规则失败:', error);
                        ElementPlus.ElMessage.error('更新分发规则失败: ' + (error.response?.data?.detail || error.message));
                    }
                };





                // 表格多选相关方法
                const handleEcommerceSelectionChange = (selection) => {
                    selectedEcommerceRows.value = selection;
                };

                const handleNewMediaSelectionChange = (selection) => {
                    selectedNewMediaRows.value = selection;
                };

                const handleGonghaiSelectionChange = (selection) => {
                    selectedGonghaiRows.value = selection;
                };

                const selectAllEcommerceRows = () => {
                    nextTick(() => {
                        if (ecommerceTable.value) {
                            ecommerceTable.value.toggleAllSelection();
                        }
                    });
                };

                const clearEcommerceSelection = () => {
                    selectedEcommerceRows.value = [];
                    nextTick(() => {
                        if (ecommerceTable.value) {
                            ecommerceTable.value.clearSelection();
                        }
                    });
                };

                const selectAllNewMediaRows = () => {
                    nextTick(() => {
                        if (newMediaTable.value) {
                            newMediaTable.value.toggleAllSelection();
                        }
                    });
                };

                const clearNewMediaSelection = () => {
                    selectedNewMediaRows.value = [];
                    nextTick(() => {
                        if (newMediaTable.value) {
                            newMediaTable.value.clearSelection();
                        }
                    });
                };

                const selectAllGonghaiRows = () => {
                    nextTick(() => {
                        if (gonghaiTable.value) {
                            gonghaiTable.value.toggleAllSelection();
                        }
                    });
                };

                const clearGonghaiSelection = () => {
                    selectedGonghaiRows.value = [];
                    nextTick(() => {
                        if (gonghaiTable.value) {
                            gonghaiTable.value.clearSelection();
                        }
                    });
                };

                // 批量修改排班相关方法
                const showQuickBatchEdit = (channelType) => {
                    const selectedRows = channelType === 'ecommerce' ? selectedEcommerceRows.value :
                                       (channelType === 'gonghai' ? selectedGonghaiRows.value : selectedNewMediaRows.value);

                    if (selectedRows.length === 0) {
                        ElementPlus.ElMessage.warning('请先选择要修改的记录');
                        return;
                    }

                    quickBatchChannelType.value = channelType;
                    quickBatchSelectedRows.value = selectedRows;
                    showQuickBatchDialog.value = true;

                    // 重置表单
                    Object.assign(quickBatchForm, {
                        member: '',
                        shift: '',
                        expectedTotal: null,
                        expectedFree: null,
                        expectedPaid: null,
                        selectedStores: []
                    });
                };

                const getQuickBatchAvailableMembers = () => {
                    const channelType = quickBatchChannelType.value;
                    const allMembers = [];

                    // 获取选中行所在分组的成员
                    const groupNames = [...new Set(quickBatchSelectedRows.value.map(row => row.group_name))];

                    groupNames.forEach(groupName => {
                        const members = getGroupMembers(groupName, channelType);
                        allMembers.push(...members);
                    });

                    // 去重
                    const uniqueMembers = allMembers.filter((member, index, self) =>
                        index === self.findIndex(m => m.name === member.name)
                    );

                    return uniqueMembers;
                };

                const applyQuickBatchEdit = async () => {
                    if (quickBatchSelectedRows.value.length === 0) {
                        ElementPlus.ElMessage.warning('没有选中的记录');
                        return;
                    }

                    try {
                        const promises = [];

                        for (const row of quickBatchSelectedRows.value) {
                            // 修改人员（仅新媒体渠道）
                            if (quickBatchChannelType.value === 'newmedia' && quickBatchForm.member) {
                                promises.push(updateRuleMember(row.id, quickBatchForm.member));
                            }

                            // 修改班次
                            if (quickBatchForm.shift) {
                                promises.push(updateRuleShift(row.id, quickBatchForm.shift));
                            }

                            // 修改预计接待数（仅电商渠道）
                            if (quickBatchChannelType.value === 'ecommerce' && quickBatchForm.expectedTotal !== null && quickBatchForm.expectedTotal !== undefined) {
                                promises.push(updateRuleExpectedReception(row.id, quickBatchForm.expectedTotal));
                            }

                            // 修改预计免费接待数（仅新媒体渠道）
                            if (quickBatchChannelType.value === 'newmedia' && quickBatchForm.expectedFree !== null && quickBatchForm.expectedFree !== undefined) {
                                promises.push(updateRuleFreeReception(row.id, quickBatchForm.expectedFree));
                            }

                            // 修改预计付费接待数（仅新媒体渠道）
                            if (quickBatchChannelType.value === 'newmedia' && quickBatchForm.expectedPaid !== null && quickBatchForm.expectedPaid !== undefined) {
                                promises.push(updateRulePaidReception(row.id, quickBatchForm.expectedPaid));
                            }

                            // 修改店铺
                            if (quickBatchForm.selectedStores.length > 0) {
                                const storeNames = quickBatchForm.selectedStores.map(store => store.name).join(', ');
                                promises.push(updateRuleStore(row.id, storeNames));
                            }
                        }

                        await Promise.all(promises);

                        ElementPlus.ElMessage.success(`成功批量修改 ${quickBatchSelectedRows.value.length} 条记录`);
                        showQuickBatchDialog.value = false;

                        // 清空选择
                        if (quickBatchChannelType.value === 'ecommerce') {
                            clearEcommerceSelection();
                        } else {
                            clearNewMediaSelection();
                        }

                        // 刷新数据
                        await fetchRules();

                    } catch (error) {
                        console.error('批量修改排班失败:', error);
                        ElementPlus.ElMessage.error('批量修改排班失败: ' + (error.response?.data?.detail || error.message));
                    }
                };

                // 批量删除相关方法
                const showBatchDeleteConfirm = (channelType) => {
                    const selectedRows = channelType === 'ecommerce' ? selectedEcommerceRows.value :
                                       (channelType === 'gonghai' ? selectedGonghaiRows.value : selectedNewMediaRows.value);
                    const channelName = channelType === 'ecommerce' ? '电商渠道' :
                                      (channelType === 'gonghai' ? '公海渠道' : '新媒体渠道');

                    if (selectedRows.length === 0) {
                        ElementPlus.ElMessage.warning('请先选择要删除的记录');
                        return;
                    }

                    ElementPlus.ElMessageBox.confirm(
                        `确定要删除选中的 ${selectedRows.length} 条${channelName}分发规则吗？此操作不可恢复！`,
                        '批量删除确认',
                        {
                            confirmButtonText: '确定删除',
                            cancelButtonText: '取消',
                            type: 'warning',
                            dangerouslyUseHTMLString: true
                        }
                    ).then(() => {
                        batchDeleteRules(channelType, selectedRows);
                    }).catch(() => {
                        ElementPlus.ElMessage.info('已取消删除');
                    });
                };

                const batchDeleteRules = async (channelType, selectedRows) => {
                    const loading = ElementPlus.ElLoading.service({
                        lock: true,
                        text: '正在删除...',
                        background: 'rgba(0, 0, 0, 0.7)'
                    });

                    try {
                        const deletePromises = selectedRows.map(row =>
                            axios.delete(`/api/distribution/rules/${row.id}`)
                        );

                        await Promise.all(deletePromises);

                        ElementPlus.ElMessage.success(`成功删除 ${selectedRows.length} 条记录`);

                        // 清空选择状态
                        if (channelType === 'ecommerce') {
                            clearEcommerceSelection();
                        } else {
                            clearNewMediaSelection();
                        }

                        // 刷新数据
                        await fetchRules();

                    } catch (error) {
                        console.error('批量删除失败:', error);
                        ElementPlus.ElMessage.error('批量删除失败: ' + (error.response?.data?.detail || error.message));
                    } finally {
                        loading.close();
                    }
                };

                // 批量追加相关方法
                const showBatchAppendEdit = (channelType) => {
                    const selectedRows = channelType === 'ecommerce' ? selectedEcommerceRows.value :
                                       (channelType === 'gonghai' ? selectedGonghaiRows.value : selectedNewMediaRows.value);

                    if (selectedRows.length === 0) {
                        ElementPlus.ElMessage.warning('请先选择要追加的记录');
                        return;
                    }

                    batchAppendChannelType.value = channelType;
                    batchAppendSelectedRows.value = [...selectedRows];

                    // 重置表单
                    Object.assign(batchAppendForm, {
                        expectedTotalAdd: null,
                        expectedTotalReduce: null,
                        expectedFreeAdd: null,
                        expectedFreeReduce: null,
                        expectedPaidAdd: null,
                        expectedPaidReduce: null
                    });

                    showBatchAppendDialog.value = true;
                };

                const applyBatchAppendEdit = async () => {
                    if (batchAppendSelectedRows.value.length === 0) {
                        ElementPlus.ElMessage.warning('没有选中的记录');
                        return;
                    }

                    try {
                        const promises = [];

                        for (const row of batchAppendSelectedRows.value) {
                            // 电商渠道的接待数追加/减少
                            if (batchAppendChannelType.value === 'ecommerce') {
                                if (batchAppendForm.expectedTotalAdd !== null && batchAppendForm.expectedTotalAdd !== undefined) {
                                    const newValue = (row.expected_total || 0) + batchAppendForm.expectedTotalAdd;
                                    promises.push(updateRuleExpectedReception(row.id, newValue));
                                }
                                if (batchAppendForm.expectedTotalReduce !== null && batchAppendForm.expectedTotalReduce !== undefined) {
                                    const newValue = Math.max(0, (row.expected_total || 0) - batchAppendForm.expectedTotalReduce);
                                    promises.push(updateRuleExpectedReception(row.id, newValue));
                                }
                            }

                            // 新媒体渠道的免费接待数追加/减少
                            if (batchAppendChannelType.value === 'newmedia') {
                                if (batchAppendForm.expectedFreeAdd !== null && batchAppendForm.expectedFreeAdd !== undefined) {
                                    const newValue = (row.expected_free || 0) + batchAppendForm.expectedFreeAdd;
                                    promises.push(updateRuleFreeReception(row.id, newValue));
                                }
                                if (batchAppendForm.expectedFreeReduce !== null && batchAppendForm.expectedFreeReduce !== undefined) {
                                    const newValue = Math.max(0, (row.expected_free || 0) - batchAppendForm.expectedFreeReduce);
                                    promises.push(updateRuleFreeReception(row.id, newValue));
                                }

                                // 新媒体渠道的付费接待数追加/减少
                                if (batchAppendForm.expectedPaidAdd !== null && batchAppendForm.expectedPaidAdd !== undefined) {
                                    const newValue = (row.expected_paid || 0) + batchAppendForm.expectedPaidAdd;
                                    promises.push(updateRulePaidReception(row.id, newValue));
                                }
                                if (batchAppendForm.expectedPaidReduce !== null && batchAppendForm.expectedPaidReduce !== undefined) {
                                    const newValue = Math.max(0, (row.expected_paid || 0) - batchAppendForm.expectedPaidReduce);
                                    promises.push(updateRulePaidReception(row.id, newValue));
                                }
                            }
                        }

                        if (promises.length === 0) {
                            ElementPlus.ElMessage.warning('请至少填写一个追加或减少的数值');
                            return;
                        }

                        await Promise.all(promises);

                        ElementPlus.ElMessage.success(`成功批量追加 ${batchAppendSelectedRows.value.length} 条记录`);
                        showBatchAppendDialog.value = false;

                        // 清空选择状态
                        if (batchAppendChannelType.value === 'ecommerce') {
                            clearEcommerceSelection();
                        } else {
                            clearNewMediaSelection();
                        }

                        // 刷新数据
                        await fetchRules();

                    } catch (error) {
                        console.error('批量追加失败:', error);
                        ElementPlus.ElMessage.error('批量追加失败: ' + (error.response?.data?.detail || error.message));
                    }
                };

                // 更新规则店铺
                const updateRuleStore = async (ruleId, storeName) => {
                    try {
                        console.log(`开始更新规则 ${ruleId} 的店铺为: ${storeName}`);

                        const response = await axios.put(`/api/distribution/rules/${ruleId}`, {
                            store: storeName
                        });

                        console.log('更新店铺成功:', response.data);

                        // 查找当前规则数据
                        let rule = null;

                        // 先在电商渠道规则中查找
                        if (ecommerceRules.value && ecommerceRules.value.length > 0) {
                            rule = ecommerceRules.value.find(r => r.id === ruleId);
                        }

                        // 如果在电商渠道规则中未找到，则在新媒体渠道规则中查找
                        if (!rule && newMediaRules.value && newMediaRules.value.length > 0) {
                            rule = newMediaRules.value.find(r => r.id === ruleId);
                        }

                        if (rule) {
                            // 更新本地数据
                            rule.store = storeName;

                            // 强制更新视图
                            if (rule.channel_type === '电商渠道') {
                                ecommerceRules.value = [...ecommerceRules.value];
                            } else {
                                newMediaRules.value = [...newMediaRules.value];
                            }
                        }

                        return response.data;
                    } catch (error) {
                        console.error('更新店铺失败:', error);
                        throw error;
                    }
                };

                // 批量修改排班店铺选择相关方法
                const openQuickBatchStoreSelection = () => {
                    // 使用现有的店铺选择对话框
                    storeSelectionDialogVisible.value = true;
                    // 设置标记，表示是批量修改排班模式
                    isQuickBatchStoreSelection.value = true;
                    // 预选已选择的店铺缩写
                    selectedStores.value = quickBatchForm.selectedStores.map(store => store.name);
                };

                const removeQuickBatchStoreSelection = (store) => {
                    const index = quickBatchForm.selectedStores.findIndex(s => s.id === store.id);
                    if (index > -1) {
                        quickBatchForm.selectedStores.splice(index, 1);
                    }
                };


                
                // 提交创建表单
                const submitCreateForm = async () => {
                    try {
                        // 验证表单
                        if (!currentDate.value) {
                            ElementPlus.ElMessage.warning('请选择日期');
                            return;
                        }
                        
                        if (selectedChannels.value.length === 0) {
                            ElementPlus.ElMessage.warning('请至少选择一个渠道');
                            return;
                        }
                        
                        // 检查电商渠道是否选择了分组
                        if (selectedChannels.value.includes('电商渠道') && selectedEcommerceGroups.value.length === 0) {
                            ElementPlus.ElMessage.warning('请为电商渠道选择至少一个分组');
                            return;
                        }
                        
                        // 检查新媒体渠道是否选择了分组
                        if (selectedChannels.value.includes('新媒体渠道') && selectedNewMediaGroups.value.length === 0) {
                            ElementPlus.ElMessage.warning('请为新媒体渠道选择至少一个分组');
                            return;
                        }
                        
                        // 获取分组的负责人和成员数据
                        await fetchGroupLeadersAndMembers();
                        
                        // 准备提交数据
                        const channelReceptions = [];
                        const distributionRules = [];
                        
                        // 处理电商渠道数据
                        if (selectedChannels.value.includes('电商渠道')) {
                            // 添加电商渠道预期接待量
                            channelReceptions.push({
                                rule_date: currentDate.value,
                                channel_type: '电商渠道',
                                expected_reception: ecommerceReception.value,
                                expected_free_reception: 0,
                                expected_paid_reception: 0
                            });
                            
                            // 添加电商渠道分配规则
                            for (const groupId of selectedEcommerceGroups.value) {
                                const group = ecommerceGroups.value.find(g => g.id === groupId);
                                if (!group) continue;
                                
                                // 获取该分组的分配规则
                                const rule = ecommerceRulesForms[groupId] && ecommerceRulesForms[groupId][0];
                                if (!rule) continue;
                                
                                // 获取该分组的负责人和成员
                                const leader = getGroupLeaders(groupId, 'ecommerce')[0]?.name || '';
                                const members = getGroupMembers(groupId, 'ecommerce');
                                
                                if (members.length === 0) {
                                    // 如果没有成员，创建一个默认规则
                                    distributionRules.push({
                                        rule_date: currentDate.value,
                                        channel_type: '电商渠道',
                                        group_name: group.name,
                                        leader: leader,
                                        member: '',
                                        shift: '',
                                        expected_reception: rule.distribution_ratio || 0,
                                        expected_free_reception: 0,
                                        expected_paid_reception: 0,
                                        store: '',
                                        time_range_start: '00:00:00',
                                        time_range_end: '00:00:00',
                                        remarks: ''
                                    });
                                } else {
                                    // 为每个成员创建一条规则
                                    for (let i = 0; i < members.length; i++) {
                                        const member = members[i];
                                        // 计算每个成员的预计接待数，确保总和等于分配数量
                                        // 使用余数分配法，将余数分配给前几个成员
                                        const totalRatio = rule.distribution_ratio || 0;
                                        const memberCount = members.length;
                                        const baseValue = Math.floor(totalRatio / memberCount);
                                        const remainder = totalRatio % memberCount;
                                        const memberExpectedReception = baseValue + (i < remainder ? 1 : 0);
                                        
                                        // 移除免费和付费接待数的计算
                                        
                                        distributionRules.push({
                                            rule_date: currentDate.value,
                                            channel_type: '电商渠道',
                                            group_name: group.name,
                                            leader: leader,
                                            member: member.name,
                                            shift: '',
                                            expected_reception: memberExpectedReception,
                                            expected_free_reception: 0,
                                            expected_paid_reception: 0,
                                            store: '',
                                            time_range_start: '00:00:00',
                                            time_range_end: '00:00:00',
                                            remarks: ''
                                        });
                                    }
                                }
                            }
                        }
                        
                        // 处理新媒体渠道数据
                        if (selectedChannels.value.includes('新媒体渠道')) {
                            // 添加新媒体渠道预期接待量
                            channelReceptions.push({
                                rule_date: currentDate.value,
                                channel_type: '新媒体渠道',
                                expected_reception: newMediaReception.value,
                                expected_free_reception: 0,
                                expected_paid_reception: 0
                            });
                            
                            // 添加新媒体渠道分配规则
                            for (const groupId of selectedNewMediaGroups.value) {
                                const group = newMediaGroups.value.find(g => g.id === groupId);
                                if (!group) continue;
                                
                                // 获取该分组的分配规则
                                const rule = newMediaRulesForms[groupId] && newMediaRulesForms[groupId][0];
                                if (!rule) continue;
                                
                                // 获取该分组的负责人和成员
                                const leader = getGroupLeaders(groupId, 'newmedia')[0]?.name || '';
                                const members = getGroupMembers(groupId, 'newmedia');
                                
                                if (members.length === 0) {
                                    // 如果没有成员，创建一个默认规则
                                    distributionRules.push({
                                        rule_date: currentDate.value,
                                        channel_type: '新媒体渠道',
                                        group_name: group.name,
                                        leader: leader,
                                        member: '',
                                        shift: '',
                                        expected_reception: rule.distribution_ratio || 0,
                                        expected_free_reception: 0,
                                        expected_paid_reception: 0,
                                        store: '',
                                        time_range_start: '00:00:00',
                                        time_range_end: '00:00:00',
                                        remarks: ''
                                    });
                                } else {
                                    // 为每个成员创建一条规则
                                    for (let i = 0; i < members.length; i++) {
                                        const member = members[i];
                                        // 计算每个成员的预计接待数，确保总和等于分配数量
                                        // 使用余数分配法，将余数分配给前几个成员
                                        const totalRatio = rule.distribution_ratio || 0;
                                        const memberCount = members.length;
                                        const baseValue = Math.floor(totalRatio / memberCount);
                                        const remainder = totalRatio % memberCount;
                                        const memberExpectedReception = baseValue + (i < remainder ? 1 : 0);
                                        
                                        // 移除免费和付费接待数的计算
                                        
                                        distributionRules.push({
                                            rule_date: currentDate.value,
                                            channel_type: '新媒体渠道',
                                            group_name: group.name,
                                            leader: leader,
                                            member: member.name,
                                            shift: '',
                                            expected_reception: 0,
                                            expected_free_reception: 0,
                                            expected_paid_reception: 0,
                                            store: '',
                                            time_range_start: '00:00:00',
                                            time_range_end: '00:00:00',
                                            remarks: ''
                                        });
                                    }
                                }
                            }
                        }
                        
                        console.log('提交数据:', {
                            rule_date: currentDate.value,
                            channel_receptions: channelReceptions,
                            distribution_rules: distributionRules
                        });
                        
                        // 发送请求
                        const response = await axios.post('/api/distribution/rules', {
                            rule_date: currentDate.value,
                            channel_receptions: channelReceptions,
                            distribution_rules: distributionRules
                        });
                        
                        console.log('创建规则响应:', response);
                        
                        // 保存分组分配数量
                        try {
                            // 保存电商渠道分组分配数量
                            for (const groupId of selectedEcommerceGroups.value) {
                                const group = ecommerceGroups.value.find(g => g.id === groupId);
                                if (!group) continue;
                                
                                const rule = ecommerceRulesForms[groupId] && ecommerceRulesForms[groupId][0];
                                if (!rule) continue;
                                
                                await axios.post('/api/distribution/group-ratios', {
                                    rule_date: currentDate.value,
                                    channel_type: '电商渠道',
                                    group_name: group.name,
                                    distribution_ratio: rule.distribution_ratio || 0
                                });
                            }
                            
                            // 保存新媒体渠道分组分配数量
                            for (const groupId of selectedNewMediaGroups.value) {
                                const group = newMediaGroups.value.find(g => g.id === groupId);
                                if (!group) continue;
                                
                                const rule = newMediaRulesForms[groupId] && newMediaRulesForms[groupId][0];
                                if (!rule) continue;
                                
                                await axios.post('/api/distribution/group-ratios', {
                                    rule_date: currentDate.value,
                                    channel_type: '新媒体渠道',
                                    group_name: group.name,
                                    distribution_ratio: rule.distribution_ratio || 0
                                });
                            }
                            
                            console.log('保存分组分配数量成功');
                        } catch (error) {
                            console.error('保存分组分配数量失败:', error);
                            // 不影响主流程，继续执行
                        }
                        
                        // 显示成功消息
                        ElementPlus.ElMessage.success('创建规则成功');
                        
                        // 关闭对话框
                        createDialogVisible.value = false;
                        
                        // 刷新规则列表
                        fetchRules();
                    } catch (error) {
                        console.error('创建规则失败:', error);
                        ElementPlus.ElMessage.error('创建规则失败: ' + (error.response?.data?.detail || error.message));
                    }
                };
                
                // 页面加载时获取数据
                onMounted(async () => {
                    // 设置默认值
                    ecommerceRules.value = [];
                    newMediaRules.value = [];
                    hasEcommerceRules.value = false;
                    hasNewMediaRules.value = false;
                    hasRules.value = false;
                    ecommerceReception.value = 0;
                    newMediaReception.value = 0;
                    gonghaiReception.value = 0;
                    
                    // 确保API密钥已设置
                    await setupAxios();
                    
                    // 等待一小段时间确保axios配置生效
                    await new Promise(resolve => setTimeout(resolve, 100));
                    
                    // 获取分发规则
                    await fetchRules();
                    
                    // 获取店铺数据
                    await fetchStores();
                    
                    // 获取所有销售组数据
                    await fetchAllSalesGroups();
                    
                    // 确保表头固定
                    nextTick(() => {
                        ensureFixedHeader();
                    });
                });
                
                // 方法
                const fetchRules = async () => {
                    loading.value = true;
                    
                    try {
                        // 获取分发规则
                        const response = await axios.get(`/api/distribution/rules?rule_date=${currentDate.value}`);
                        
                        // 解析分组名称，提取后缀数字
                        const parseGroupName = (name) => {
                            const match = name.match(/^(.+?)(\d+)?$/);
                            if (match) {
                                const prefix = match[1];
                                const suffix = match[2] ? parseInt(match[2]) : null;
                                return { prefix, suffix };
                            }
                            return { prefix: name, suffix: null };
                        };
                        
                        // 自定义排序函数
                        const sortByGroupName = (a, b) => {
                            const aGroup = parseGroupName(a.group_name);
                            const bGroup = parseGroupName(b.group_name);
                            
                            // 如果前缀不同，按前缀排序
                            if (aGroup.prefix !== bGroup.prefix) {
                                const result = aGroup.prefix.localeCompare(bGroup.prefix, 'zh-CN');
                                console.log('前缀不同，按前缀排序:', result);
                                return result;
                            }
                            
                            // 如果一个有后缀，一个没有后缀，没有后缀的排在前面
                            if (aGroup.suffix === null && bGroup.suffix !== null) {
                                console.log('a没有后缀，b有后缀，a排前面');
                                return -1;
                            }
                            if (aGroup.suffix !== null && bGroup.suffix === null) {
                                console.log('a有后缀，b没有后缀，b排前面');
                                return 1;
                            }
                            
                            // 如果都有后缀，按后缀数字大小排序
                            if (aGroup.suffix !== null && bGroup.suffix !== null) {
                                const result = aGroup.suffix - bGroup.suffix;
                                console.log('都有后缀，按数字排序:', result);
                                return result;
                            }
                            
                            // 默认情况下按完整名称排序
                            const result = a.group_name.localeCompare(b.group_name, 'zh-CN');
                            console.log('使用默认排序:', result);
                            return result;
                        };
                        
                        // 处理电商渠道规则
                        let ecommerceData = response.data.filter(rule => rule.channel_type === '电商渠道');
                        // 按分组名称排序，考虑特殊排序逻辑
                        ecommerceData.sort(sortByGroupName);
                        // 为每行数据添加编辑相关的属性
                        ecommerceData.forEach(rule => {
                            rule.showEditIcon = false;
                            rule.showMemberEditIcon = false;
                            rule.showShiftEditIcon = false;
                            rule.showExpectedReceptionEditIcon = false;
                            rule.isEditing = false;
                            rule.isMemberEditing = false;
                            rule.isShiftEditing = false;
                            rule.isExpectedReceptionEditing = false;
                            rule.loadingMembers = false;
                            rule.tempDistributionRatio = getGroupSettingDistributionRatio(rule.group_name, 'ecommerce');
                            rule.tempMember = rule.member;
                            rule.tempShift = rule.shift;
                            rule.tempExpectedReception = rule.expected_total || 0;
                            
                            // 处理时间字段
                            if (rule.time_range && (!rule.time_range_start || !rule.time_range_end)) {
                                // 如果只有time_range字段，拆分成start和end
                                const [start, end] = rule.time_range.split('-').map(t => t.trim());
                                rule.time_range_start = start + ':00';
                                rule.time_range_end = end + ':00';
                            } else if (rule.time_range_start && rule.time_range_end) {
                                // 如果有start和end字段，格式化time_range
                                rule.time_range = `${formatTime(rule.time_range_start)}-${formatTime(rule.time_range_end)}`;
                            } else {
                                // 设置默认值
                                rule.time_range_start = '';
                                rule.time_range_end = '';
                                rule.time_range = '';
                            }
                        });
                        ecommerceRules.value = ecommerceData;
                        hasEcommerceRules.value = ecommerceRules.value.length > 0;
                        
                        // 处理新媒体渠道规则
                        let newMediaData = response.data.filter(rule => rule.channel_type === '新媒体渠道');
                        // 按分组名称排序，考虑特殊排序逻辑
                        newMediaData.sort(sortByGroupName);
                        // 为每行数据添加编辑相关的属性
                        newMediaData.forEach(rule => {
                            rule.showEditIcon = false;
                            rule.showMemberEditIcon = false;
                            rule.showShiftEditIcon = false;
                            rule.showExpectedReceptionEditIcon = false;
                            rule.isEditing = false;
                            rule.isMemberEditing = false;
                            rule.isShiftEditing = false;
                            rule.isExpectedReceptionEditing = false;
                            rule.loadingMembers = false;
                            rule.tempDistributionRatio = getGroupSettingDistributionRatio(rule.group_name, 'newmedia');
                            rule.tempMember = rule.member;
                            rule.tempShift = rule.shift;
                            rule.tempExpectedReception = rule.expected_total || 0;
                            
                            // 处理时间字段
                            if (rule.time_range && (!rule.time_range_start || !rule.time_range_end)) {
                                // 如果只有time_range字段，拆分成start和end
                                const [start, end] = rule.time_range.split('-').map(t => t.trim());
                                rule.time_range_start = start + ':00';
                                rule.time_range_end = end + ':00';
                            } else if (rule.time_range_start && rule.time_range_end) {
                                // 如果有start和end字段，格式化time_range
                                rule.time_range = `${formatTime(rule.time_range_start)}-${formatTime(rule.time_range_end)}`;
                            } else {
                                // 设置默认值
                                rule.time_range_start = '';
                                rule.time_range_end = '';
                                rule.time_range = '';
                            }
                        });
                        newMediaRules.value = newMediaData;
                        hasNewMediaRules.value = newMediaRules.value.length > 0;

                        // 处理公海渠道规则（复用电商渠道的接口，所以过滤电商渠道数据）
                        let gonghaiData = response.data.filter(rule => rule.channel_type === '电商渠道');
                        // 按分组名称排序，考虑特殊排序逻辑
                        gonghaiData.sort(sortByGroupName);
                        // 为每行数据添加编辑相关的属性
                        gonghaiData.forEach(rule => {
                            rule.showEditIcon = false;
                            rule.showMemberEditIcon = false;
                            rule.showShiftEditIcon = false;
                            rule.showExpectedReceptionEditIcon = false;
                            rule.isEditing = false;
                            rule.isMemberEditing = false;
                            rule.isShiftEditing = false;
                            rule.isExpectedReceptionEditing = false;
                            rule.loadingMembers = false;
                            rule.tempDistributionRatio = getGroupSettingDistributionRatio(rule.group_name, 'gonghai');
                            rule.tempMember = rule.member;
                            rule.tempShift = rule.shift;
                            rule.tempExpectedReception = rule.expected_total || 0;

                            // 处理时间字段
                            if (rule.time_range && (!rule.time_range_start || !rule.time_range_end)) {
                                // 如果只有time_range字段，拆分成start和end
                                const [start, end] = rule.time_range.split('-').map(t => t.trim());
                                rule.time_range_start = start + ':00';
                                rule.time_range_end = end + ':00';
                            } else if (rule.time_range_start && rule.time_range_end) {
                                // 如果有start和end字段，生成time_range
                                rule.time_range = `${rule.time_range_start.substring(0, 5)}-${rule.time_range_end.substring(0, 5)}`;
                            } else {
                                // 如果都没有，设置默认值
                                rule.time_range_start = '09:00:00';
                                rule.time_range_end = '18:00:00';
                                rule.time_range = '09:00-18:00';
                            }

                            // 确保time_range字段不为空
                            if (!rule.time_range) {
                                rule.time_range = '';
                            }
                        });
                        gonghaiRules.value = gonghaiData;
                        hasGonghaiRules.value = gonghaiRules.value.length > 0;

                        // 更新是否有规则的标志
                        hasRules.value = hasEcommerceRules.value || hasNewMediaRules.value || hasGonghaiRules.value;
                        
                        // 获取分组分配数量数据
                        try {
                            const ratiosResponse = await axios.get(`/api/distribution/group-ratios?rule_date=${currentDate.value}`);
                            console.log('获取分组分配数量数据成功:', ratiosResponse.data);
                            
                            // 存储分组分配数量数据
                            groupDistributionRatios.value = ratiosResponse.data;
                            
                            // 处理电商渠道分组分配数量
                            const ecommerceRatios = ratiosResponse.data.filter(ratio => ratio.channel_type === '电商渠道');
                            ecommerceRatios.forEach(ratio => {
                                // 查找对应分组的ID
                                const group = ecommerceGroups.value.find(g => g.name === ratio.group_name);
                                if (!group) return;
                                
                                // 初始化规则表单
                                if (!ecommerceRulesForms[group.id]) {
                                    ecommerceRulesForms[group.id] = [];
                                }
                                
                                if (!ecommerceRulesForms[group.id][0]) {
                                    ecommerceRulesForms[group.id][0] = {
                                        group_id: group.id,
                                        group_name: ratio.group_name,
                                        distribution_ratio: ratio.distribution_ratio
                                    };
                                } else {
                                    // 更新分配数量
                                    ecommerceRulesForms[group.id][0].distribution_ratio = ratio.distribution_ratio;
                                }
                            });
                            
                            // 处理新媒体渠道分组分配数量
                            const newMediaRatios = ratiosResponse.data.filter(ratio => ratio.channel_type === '新媒体渠道');
                            newMediaRatios.forEach(ratio => {
                                // 查找对应分组的ID
                                const group = newMediaGroups.value.find(g => g.name === ratio.group_name);
                                if (!group) return;
                                
                                // 初始化规则表单
                                if (!newMediaRulesForms[group.id]) {
                                    newMediaRulesForms[group.id] = [];
                                }
                                
                                if (!newMediaRulesForms[group.id][0]) {
                                    newMediaRulesForms[group.id][0] = {
                                        group_id: group.id,
                                        group_name: ratio.group_name,
                                        distribution_ratio: ratio.distribution_ratio
                                    };
                                } else {
                                    // 更新分配数量
                                    newMediaRulesForms[group.id][0].distribution_ratio = ratio.distribution_ratio;
                                }
                            });
                            
                            // 更新表格数据中的tempDistributionRatio
                            ecommerceRules.value.forEach(rule => {
                                rule.tempDistributionRatio = getGroupSettingDistributionRatio(rule.group_name, 'ecommerce');
                            });
                            
                            newMediaRules.value.forEach(rule => {
                                rule.tempDistributionRatio = getGroupSettingDistributionRatio(rule.group_name, 'newmedia');
                            });
                            
                        } catch (error) {
                            console.error('获取分组分配数量失败:', error);
                            // 不影响主流程，继续执行
                        }
                        
                        // 初始化规则表单并设置分配数量
                        if (hasRules.value) {
                            // 初始化电商渠道规则表单
                            if (hasEcommerceRules.value) {
                                // 获取所有电商渠道分组
                                const ecommerceGroupNames = [...new Set(ecommerceRules.value.map(rule => rule.group_name))];
                                
                                // 为每个分组计算分配数量
                                ecommerceGroupNames.forEach(groupName => {
                                    // 查找对应分组的ID
                                    const group = ecommerceGroups.value.find(g => g.name === groupName);
                                    if (!group) return;
                                    
                                    // 如果没有从API获取到分配数量，则从表格数据中计算
                                    if (!ecommerceRulesForms[group.id] || !ecommerceRulesForms[group.id][0]) {
                                        // 计算该分组的总预计接待数
                                        const groupRules = ecommerceRules.value.filter(rule => rule.group_name === groupName);
                                        const totalExpectedReception = groupRules.reduce((sum, rule) => sum + (rule.expected_total || 0), 0);
                                        
                                        // 初始化规则表单
                                        if (!ecommerceRulesForms[group.id]) {
                                            ecommerceRulesForms[group.id] = [];
                                        }
                                        
                                        if (!ecommerceRulesForms[group.id][0]) {
                                            ecommerceRulesForms[group.id][0] = {
                                                group_id: group.id,
                                                group_name: groupName,
                                                distribution_ratio: totalExpectedReception
                                            };
                                        } else {
                                            // 更新分配数量
                                            ecommerceRulesForms[group.id][0].distribution_ratio = totalExpectedReception;
                                        }
                                    }
                                });
                            }
                            
                            // 初始化新媒体渠道规则表单
                            if (hasNewMediaRules.value) {
                                // 获取所有新媒体渠道分组
                                const newMediaGroupNames = [...new Set(newMediaRules.value.map(rule => rule.group_name))];
                                
                                // 为每个分组计算分配数量
                                newMediaGroupNames.forEach(groupName => {
                                    // 查找对应分组的ID
                                    const group = newMediaGroups.value.find(g => g.name === groupName);
                                    if (!group) return;
                                    
                                    // 如果没有从API获取到分配数量，则从表格数据中计算
                                    if (!newMediaRulesForms[group.id] || !newMediaRulesForms[group.id][0]) {
                                        // 计算该分组的总预计接待数
                                        const groupRules = newMediaRules.value.filter(rule => rule.group_name === groupName);
                                        const totalExpectedReception = groupRules.reduce((sum, rule) => sum + (rule.expected_total || 0), 0);
                                        
                                        // 初始化规则表单
                                        if (!newMediaRulesForms[group.id]) {
                                            newMediaRulesForms[group.id] = [];
                                        }
                                        
                                        if (!newMediaRulesForms[group.id][0]) {
                                            newMediaRulesForms[group.id][0] = {
                                                group_id: group.id,
                                                group_name: groupName,
                                                distribution_ratio: totalExpectedReception
                                            };
                                        } else {
                                            // 更新分配数量
                                            newMediaRulesForms[group.id][0].distribution_ratio = totalExpectedReception;
                                        }
                                    }
                                });
                            }
                        }
                        
                        try {
                            // 获取渠道接待数
                            const receptionResponse = await axios.get(`/api/distribution/channel-receptions?rule_date=${currentDate.value}`);
                            
                            // 处理电商渠道接待数
                            const ecommerceReceptionData = receptionResponse.data.find(item => item.channel_type === '电商渠道');
                            if (ecommerceReceptionData) {
                                // 更新电商渠道接待数数据对象，包含所有字段
                                ecommerceReception.value = {
                                    expected_reception: ecommerceReceptionData.expected_reception || 0,
                                    expected_free_reception: ecommerceReceptionData.expected_free_reception || 0,
                                    expected_paid_reception: ecommerceReceptionData.expected_paid_reception || 0,
                                    current_reception: ecommerceReceptionData.current_reception || 0,
                                    current_free_reception: ecommerceReceptionData.current_free_reception || 0,
                                    current_paid_reception: ecommerceReceptionData.current_paid_reception || 0,
                                    actual_reception: ecommerceReceptionData.actual_reception || 0,
                                    actual_free_reception: ecommerceReceptionData.actual_free_reception || 0,
                                    actual_paid_reception: ecommerceReceptionData.actual_paid_reception || 0
                                };
                            } else {
                                // 设置默认值
                                ecommerceReception.value = {
                                    expected_reception: 0,
                                    expected_free_reception: 0,
                                    expected_paid_reception: 0,
                                    current_reception: 0,
                                    current_free_reception: 0,
                                    current_paid_reception: 0,
                                    actual_reception: 0,
                                    actual_free_reception: 0,
                                    actual_paid_reception: 0
                                };
                            }
                            
                            // 处理新媒体渠道接待数
                            const newMediaReceptionData = receptionResponse.data.find(item => item.channel_type === '新媒体渠道');
                            if (newMediaReceptionData) {
                                // 更新新媒体渠道接待数数据对象，包含所有字段
                                newMediaReception.value = {
                                    expected_reception: newMediaReceptionData.expected_reception || 0,
                                    expected_free_reception: newMediaReceptionData.expected_free_reception || 0,
                                    expected_paid_reception: newMediaReceptionData.expected_paid_reception || 0,
                                    current_reception: newMediaReceptionData.current_reception || 0,
                                    current_free_reception: newMediaReceptionData.current_free_reception || 0,
                                    current_paid_reception: newMediaReceptionData.current_paid_reception || 0,
                                    actual_reception: newMediaReceptionData.actual_reception || 0,
                                    actual_free_reception: newMediaReceptionData.actual_free_reception || 0,
                                    actual_paid_reception: newMediaReceptionData.actual_paid_reception || 0
                                };
                            } else {
                                // 设置默认值
                                newMediaReception.value = {
                                    expected_reception: 0,
                                    expected_free_reception: 0,
                                    expected_paid_reception: 0,
                                    current_reception: 0,
                                    current_free_reception: 0,
                                    current_paid_reception: 0,
                                    actual_reception: 0,
                                    actual_free_reception: 0,
                                    actual_paid_reception: 0
                                };
                            }

                            // 处理公海渠道接待数（复用电商渠道的数据）
                            const gonghaiReceptionData = receptionResponse.data.find(item => item.channel_type === '电商渠道');
                            if (gonghaiReceptionData) {
                                // 更新公海渠道接待数数据对象，包含所有字段
                                gonghaiReception.value = {
                                    expected_reception: gonghaiReceptionData.expected_reception || 0,
                                    expected_free_reception: gonghaiReceptionData.expected_free_reception || 0,
                                    expected_paid_reception: gonghaiReceptionData.expected_paid_reception || 0,
                                    current_reception: gonghaiReceptionData.current_reception || 0,
                                    current_free_reception: gonghaiReceptionData.current_free_reception || 0,
                                    current_paid_reception: gonghaiReceptionData.current_paid_reception || 0,
                                    actual_reception: gonghaiReceptionData.actual_reception || 0,
                                    actual_free_reception: gonghaiReceptionData.actual_free_reception || 0,
                                    actual_paid_reception: gonghaiReceptionData.actual_paid_reception || 0
                                };
                            } else {
                                // 设置默认值
                                gonghaiReception.value = {
                                    expected_reception: 0,
                                    expected_free_reception: 0,
                                    expected_paid_reception: 0,
                                    current_reception: 0,
                                    current_free_reception: 0,
                                    current_paid_reception: 0,
                                    actual_reception: 0,
                                    actual_free_reception: 0,
                                    actual_paid_reception: 0
                                };
                            }
                        } catch (error) {
                            // 设置默认值
                            ecommerceReception.value = {
                                expected_reception: 0,
                                expected_free_reception: 0,
                                expected_paid_reception: 0,
                                current_reception: 0,
                                current_free_reception: 0,
                                current_paid_reception: 0,
                                actual_reception: 0,
                                actual_free_reception: 0,
                                actual_paid_reception: 0
                            };
                            newMediaReception.value = {
                                expected_reception: 0,
                                expected_free_reception: 0,
                                expected_paid_reception: 0,
                                current_reception: 0,
                                current_free_reception: 0,
                                current_paid_reception: 0,
                                actual_reception: 0,
                                actual_free_reception: 0,
                                actual_paid_reception: 0
                            };
                            gonghaiReception.value = {
                                expected_reception: 0,
                                expected_free_reception: 0,
                                expected_paid_reception: 0,
                                current_reception: 0,
                                current_free_reception: 0,
                                current_paid_reception: 0,
                                actual_reception: 0,
                                actual_free_reception: 0,
                                actual_paid_reception: 0
                            };
                            apiError.value = true;
                        }
                    } catch (error) {
                        console.error('获取分发规则失败:', error);
                        ElementPlus.ElMessage.error('获取分发规则失败: ' + (error.response?.data?.detail || error.message));
                        // 设置默认值
                        ecommerceRules.value = [];
                        newMediaRules.value = [];
                        gonghaiRules.value = [];
                        hasEcommerceRules.value = false;
                        hasNewMediaRules.value = false;
                        hasGonghaiRules.value = false;
                        hasRules.value = false;
                        apiError.value = true;
                    } finally {
                        loading.value = false;
                    }
                };
                
                const formatTime = (timeStr) => {
                    if (!timeStr) return '';
                    
                    // 处理时间格式，如果是ISO格式，则转换为HH:MM
                    if (timeStr.includes('T')) {
                        const date = new Date(timeStr);
                        return `${date.getHours().toString().padStart(2, '0')}:${date.getMinutes().toString().padStart(2, '0')}`;
                    }
                    
                    // 如果是带秒的时间格式 (HH:MM:SS)，则去掉秒
                    if (timeStr.includes(':') && timeStr.split(':').length === 3) {
                        return timeStr.substring(0, 5);
                    }
                    
                    return timeStr;
                };
                
                const tableRowClassName = ({ row }) => {
                    if (row.shift === '晚班') {
                        return 'warning-row';
                    } else if (row.shift === '白班') {
                        return 'success-row';
                    }  else if (row.shift === '午班') {
                        return 'primary-row';
                    } else if (row.shift === '全天') {
                        return 'danger-row';
                    }
                    return '';
                };
                
                const deleteRule = async (ruleId) => {
                    try {
                        await ElementPlus.ElMessageBox.confirm(
                            '确定要删除这条分发规则吗？',
                            '警告',
                            {
                                confirmButtonText: '确定',
                                cancelButtonText: '取消',
                                type: 'warning',
                            }
                        );
                        
                        // 发送删除请求
                        await axios.delete(`/api/distribution/rules/${ruleId}`);
                        
                        // 提示成功
                        ElementPlus.ElMessage.success('删除分发规则成功');
                        
                        // 重新获取规则列表
                        fetchRules();
                    } catch (error) {
                        if (error !== 'cancel') {
                            console.error('删除分发规则失败:', error);
                            ElementPlus.ElMessage.error('删除分发规则失败: ' + (error.response?.data?.detail || error.message));
                        }
                    }
                };
                
                const handleDateChange = () => {
                    fetchRules();
                };
                
                const setToday = () => {
                    currentDate.value = formatDate(new Date());
                    fetchRules();
                };
                
                const showCreateDialog = () => {
                    createDialogVisible.value = true;
                    selectedChannels.value = [];
                    selectedEcommerceGroups.value = [];
                    selectedNewMediaGroups.value = [];
                    selectedGonghaiGroups.value = [];

                    // 重置接待数表单
                    Object.assign(ecommerceReceptionForm, {
                        expected_reception: 0,
                        expected_free_reception: 0,
                        expected_paid_reception: 0
                    });

                    Object.assign(newMediaReceptionForm, {
                        expected_reception: 0,
                        expected_free_reception: 0,
                        expected_paid_reception: 0
                    });

                    Object.assign(gonghaiReceptionForm, {
                        expected_reception: 0,
                        expected_free_reception: 0,
                        expected_paid_reception: 0
                    });
                    
                    // 获取预设数据
                    fetchPresetGroups();
                };
                
                const showCopyDialog = () => {
                    copyDialogVisible.value = true;
                    copyForm.source_date = currentDate.value;
                    copyForm.target_date = '';
                };
                
                const handleCloseDialog = () => {
                    createDialogVisible.value = false;
                    copyDialogVisible.value = false;
                    editDialogVisible.value = false;
                    selectedChannels.value = [];
                    selectedEcommerceGroups.value = [];
                    selectedNewMediaGroups.value = [];
                    selectedGonghaiGroups.value = [];
                    ecommerceReceptionForm.expected_reception = 0;
                    ecommerceReceptionForm.expected_free_reception = 0;
                    newMediaReceptionForm.expected_reception = 0;
                    newMediaReceptionForm.expected_free_reception = 0;
                    gonghaiReceptionForm.expected_reception = 0;
                    gonghaiReceptionForm.expected_free_reception = 0;
                    ecommerceRulesForms.value = {};
                    newMediaRulesForms.value = {};
                    gonghaiRulesForms.value = {};
                };
                
                // 获取预设数据
                const fetchPresetGroups = async () => {
                    try {
                        // 从销售预设管理中获取分组数据
                        const response = await axios.get('/api/presets/sales');
                        console.log('销售预设API响应:', response);
                        
                        // 检查响应数据格式
                        const salesGroups = response.data.sales || [];
                        console.log('销售分组数据:', salesGroups);
                        
                        // 确保salesGroups是数组
                        if (!Array.isArray(salesGroups)) {
                            console.error('销售分组数据不是数组:', salesGroups);
                            ElementPlus.ElMessage.error('获取预设分组失败: 数据格式不正确');
                            
                            // 使用空数组作为默认值
                            ecommerceGroups.value = [];
                            newMediaGroups.value = [];
                            return;
                        }
                        
                        // 将所有分组数据都加载到电商和新媒体分组列表中
                        const allGroups = salesGroups.map(group => ({
                            id: group.id,
                            name: group.group,
                            disabled: false
                        }));
                        
                        // 确保使用响应式数组更新
                        ecommerceGroups.value = JSON.parse(JSON.stringify(allGroups));
                        newMediaGroups.value = JSON.parse(JSON.stringify(allGroups));
                        
                        console.log('所有可用分组:', allGroups);
                        console.log('电商渠道分组:', ecommerceGroups.value);
                        console.log('新媒体渠道分组:', newMediaGroups.value);
                            
                        // 如果没有获取到数据，显示提示信息
                        if (allGroups.length === 0) {
                            console.warn('未获取到任何分组数据');
                            ElementPlus.ElMessage.warning('未获取到任何分组数据，请先在表单预设功能中添加销售预设');
                        }
                    } catch (error) {
                        console.error('获取预设分组失败:', error);
                        ElementPlus.ElMessage.error('获取预设分组失败: ' + (error.response?.data?.detail || error.message));
                        ecommerceGroups.value = [];
                        newMediaGroups.value = [];
                    }
                };
                
                // 处理分组选择变化
                const handleEcommerceGroupChange = (value) => {
                    console.log('电商渠道分组选择变化:', value);
                    
                    // 确保选中的值被正确设置
                    selectedEcommerceGroups.value = value;
                    
                    // 更新新媒体渠道可选分组，移除已被电商渠道选择的分组
                    updateAvailableGroups();
                };
                
                const handleNewMediaGroupChange = (value) => {
                    console.log('新媒体渠道分组选择变化:', value);

                    // 确保选中的值被正确设置
                    selectedNewMediaGroups.value = value;

                    // 更新电商渠道可选分组，移除已被新媒体渠道选择的分组
                    updateAvailableGroups();
                };

                const handleGonghaiGroupChange = (value) => {
                    console.log('公海渠道分组选择变化:', value);

                    // 确保选中的值被正确设置
                    selectedGonghaiGroups.value = value;

                    // 更新其他渠道可选分组，移除已被公海渠道选择的分组
                    updateAvailableGroups();
                };

                // 更新可用分组列表，防止重复选择
                const updateAvailableGroups = () => {
                    try {
                        // 获取所有销售组数据
                        axios.get('/api/presets/sales').then(res => {
                            // 确保销售组数据是数组
                            const salesGroups = Array.isArray(res.data.sales) ? res.data.sales : [];
                            
                            if (salesGroups.length === 0) {
                                console.warn('未获取到销售组数据');
                                return;
                            }
                            
                            const allGroups = salesGroups.map(group => ({
                                id: group.id,
                                name: group.group,
                                disabled: false
                            }));
                            
                            // 确保所有渠道分组选择是数组
                            const safeEcommerceGroups = Array.isArray(selectedEcommerceGroups.value) ? selectedEcommerceGroups.value : [];
                            const safeNewMediaGroups = Array.isArray(selectedNewMediaGroups.value) ? selectedNewMediaGroups.value : [];
                            const safeGonghaiGroups = Array.isArray(selectedGonghaiGroups.value) ? selectedGonghaiGroups.value : [];

                            // 标记已被各渠道选择的分组
                            const ecommerceSelected = selectedChannels.value.includes('电商渠道')
                                ? new Set(safeEcommerceGroups)
                                : new Set();

                            const newMediaSelected = selectedChannels.value.includes('新媒体渠道')
                                ? new Set(safeNewMediaGroups)
                                : new Set();

                            const gonghaiSelected = selectedChannels.value.includes('公海渠道')
                                ? new Set(safeGonghaiGroups)
                                : new Set();

                            console.log('已选电商分组:', Array.from(ecommerceSelected));
                            console.log('已选新媒体分组:', Array.from(newMediaSelected));
                            console.log('已选公海分组:', Array.from(gonghaiSelected));

                            // 更新电商渠道可选分组列表
                            ecommerceGroups.value = allGroups.map(group => ({
                                ...group,
                                disabled: newMediaSelected.has(group.id) || gonghaiSelected.has(group.id)
                            }));

                            // 更新新媒体渠道可选分组列表
                            newMediaGroups.value = allGroups.map(group => ({
                                ...group,
                                disabled: ecommerceSelected.has(group.id) || gonghaiSelected.has(group.id)
                            }));

                            // 更新公海渠道可选分组列表
                            gonghaiGroups.value = allGroups.map(group => ({
                                ...group,
                                disabled: ecommerceSelected.has(group.id) || newMediaSelected.has(group.id)
                            }));
                        }).catch(error => {
                            console.error('更新可用分组失败:', error);
                            ElementPlus.ElMessage.error('获取销售组数据失败: ' + (error.response?.data?.detail || error.message));
                        });
                    } catch (error) {
                        console.error('更新可用分组出错:', error);
                        ElementPlus.ElMessage.error('更新可用分组出错: ' + error.message);
                    }
                };
                
                // 向导步骤控制
                const nextStep = () => {
                    if (selectedChannels.value.length === 0) {
                        return;
                    }
                    
                    if (selectedChannels.value.includes('电商渠道') && selectedEcommerceGroups.value.length === 0) {
                        return;
                    }
                    
                    if (selectedChannels.value.includes('新媒体渠道') && selectedNewMediaGroups.value.length === 0) {
                        return;
                    }
                    
                    // 最后一步，提交表单
                    submitCreateForm();
                };
                
                const prevStep = () => {
                    if (selectedChannels.value.length === 0) {
                        return;
                    }
                    
                    if (selectedChannels.value.includes('电商渠道') && selectedEcommerceGroups.value.length === 0) {
                        return;
                    }
                    
                    if (selectedChannels.value.includes('新媒体渠道') && selectedNewMediaGroups.value.length === 0) {
                        return;
                    }
                };
                
                // 更新接待数表单
                const updateEcommerceReceptionForm = () => {
                    ecommerceReceptionForm.expected_paid_reception = Math.max(0, 
                        ecommerceReceptionForm.expected_reception - ecommerceReceptionForm.expected_free_reception);
                };
                
                const updateNewMediaReceptionForm = () => {
                    newMediaReceptionForm.expected_paid_reception = Math.max(0, 
                        newMediaReceptionForm.expected_reception - newMediaReceptionForm.expected_free_reception);
                };
                
                // 提交复制表单
                const submitCopyForm = async () => {
                    try {
                        // 提交数据
                        await axios.post('/api/distribution/copy', {
                            source_date: copyForm.source_date,
                            target_date: copyForm.target_date
                        });
                        
                        // 提示成功
                        ElementPlus.ElMessage.success('复制分发规则成功');
                        
                        // 关闭对话框
                        copyDialogVisible.value = false;
                        
                        // 如果目标日期是当前日期，则刷新规则列表
                        if (copyForm.target_date === currentDate.value) {
                            fetchRules();
                        }
                    } catch (error) {
                        console.error('复制分发规则失败:', error);
                        ElementPlus.ElMessage.error('复制分发规则失败: ' + (error.response?.data?.detail || error.message));
                    }
                };
                
                // 删除当日所有规则
                const deleteAllRules = async () => {
                    try {
                        await ElementPlus.ElMessageBox.confirm(
                            `确定要删除 ${currentDate.value} 的所有分发规则吗？`,
                            '警告',
                            {
                                confirmButtonText: '确定',
                                cancelButtonText: '取消',
                                type: 'warning',
                            }
                        );
                        
                        // 显示加载提示
                        const loadingInstance = ElementPlus.ElLoading.service({
                            lock: true,
                            text: '正在删除规则，请稍候...',
                            background: 'rgba(0, 0, 0, 0.7)'
                        });
                        
                        try {
                        // 获取当日所有规则
                        const response = await axios.get(`/api/distribution/rules?rule_date=${currentDate.value}`);
                        const rules = response.data || [];
                        
                        // 逐个删除规则
                        for (const rule of rules) {
                            await axios.delete(`/api/distribution/rules/${rule.id}`);
                        }
                            
                            // 删除分组分配数量
                            try {
                                const ratioResponse = await axios.delete(`/api/distribution/group-ratios?rule_date=${currentDate.value}`);
                                console.log('删除分组分配数量成功:', ratioResponse.data);
                            } catch (error) {
                                console.error('删除分组分配数量失败:', error);
                                // 不影响主流程，继续执行
                            }
                            
                            // 删除渠道接待数
                            try {
                                // 获取电商渠道和新媒体渠道的接待数记录
                                const channelReceptionsResponse = await axios.get(`/api/distribution/channel-receptions?rule_date=${currentDate.value}`);
                                const channelReceptions = channelReceptionsResponse.data || [];
                                
                                // 删除每个渠道的接待数记录
                                for (const reception of channelReceptions) {
                                    await axios.delete(`/api/distribution/reception/${currentDate.value}/${reception.channel_type}`);
                                    console.log(`删除 ${reception.channel_type} 渠道接待数成功`);
                                }
                            } catch (error) {
                                console.error('删除渠道接待数失败:', error);
                                // 不影响主流程，继续执行
                            }
                            
                            // 关闭加载提示
                            loadingInstance.close();
                        
                        // 提示成功
                        ElementPlus.ElMessage.success('删除所有分发规则成功');
                            
                            // 清空分组分配数量数据
                            groupDistributionRatios.value = [];
                        
                        // 重新获取规则列表
                        fetchRules();
                        } catch (error) {
                            // 关闭加载提示
                            loadingInstance.close();
                            
                            console.error('删除所有分发规则失败:', error);
                            ElementPlus.ElMessage.error('删除所有分发规则失败: ' + (error.response?.data?.detail || error.message));
                        }
                    } catch (error) {
                        if (error !== 'cancel') {
                            console.error('删除所有分发规则失败:', error);
                            ElementPlus.ElMessage.error('删除所有分发规则失败: ' + (error.response?.data?.detail || error.message));
                        }
                    }
                };
                
                // 监听电商渠道分组选择变化，动态创建规则表单
                Vue.watch(selectedEcommerceGroups, (newVal, oldVal) => {
                    console.log('电商渠道分组选择变化:', newVal, oldVal);
                    
                    if (!newVal || !Array.isArray(newVal)) {
                        console.warn('电商渠道分组选择无效:', newVal);
                        return;
                    }
                    
                    // 处理新增的分组
                    for (const groupId of newVal) {
                        if (!ecommerceRulesForms[groupId]) {
                            // 为新分组创建规则表单
                            const groupName = getGroupName(groupId, ecommerceGroups.value || []);
                            ecommerceRulesForms[groupId] = [{
                                group_id: groupId,
                                group_name: groupName,
                                distribution_ratio: 0 // 默认分配比例为0
                            }];
                        }
                    }
                    
                    // 处理移除的分组
                    for (const groupId in ecommerceRulesForms) {
                        if (!newVal.includes(parseInt(groupId))) {
                            delete ecommerceRulesForms[groupId];
                        }
                    }
                });
                
                // 监听新媒体渠道分组选择变化，动态创建规则表单
                Vue.watch(selectedNewMediaGroups, (newVal, oldVal) => {
                    console.log('新媒体渠道分组选择变化:', newVal, oldVal);
                    
                    if (!newVal || !Array.isArray(newVal)) {
                        console.warn('新媒体渠道分组选择无效:', newVal);
                        return;
                    }
                    
                    // 处理新增的分组
                    for (const groupId of newVal) {
                        if (!newMediaRulesForms[groupId]) {
                            // 为新分组创建规则表单
                            const groupName = getGroupName(groupId, newMediaGroups.value || []);
                            newMediaRulesForms[groupId] = [{
                                group_id: groupId,
                                group_name: groupName,
                                distribution_ratio: 0 // 默认分配比例为0
                            }];
                        }
                    }
                    
                    // 处理移除的分组
                    for (const groupId in newMediaRulesForms) {
                        if (!newVal.includes(parseInt(groupId))) {
                            delete newMediaRulesForms[groupId];
                        }
                    }
                });
                
                // 批量选择和清空方法
                const selectAllEcommerceGroups = () => {
                    // 获取所有未被禁用的电商渠道分组
                    const availableGroups = ecommerceGroups.value
                        .filter(group => !group.disabled)
                        .map(group => group.id);
                    
                    // 全选
                    selectedEcommerceGroups.value = availableGroups;
                    
                    // 更新可用分组
                    updateAvailableGroups();
                    
                    // 提示用户
                    ElementPlus.ElMessage.success(`已选择 ${availableGroups.length} 个电商渠道分组`);
                };
                
                const clearEcommerceGroups = () => {
                    // 清空选择
                    selectedEcommerceGroups.value = [];
                    
                    // 更新可用分组
                    updateAvailableGroups();
                    
                    // 提示用户
                    ElementPlus.ElMessage.info('已清空电商渠道分组选择');
                };
                
                const selectAllNewMediaGroups = () => {
                    // 获取所有未被禁用的新媒体渠道分组
                    const availableGroups = newMediaGroups.value
                        .filter(group => !group.disabled)
                        .map(group => group.id);
                    
                    // 全选
                    selectedNewMediaGroups.value = availableGroups;
                    
                    // 更新可用分组
                    updateAvailableGroups();
                    
                    // 提示用户
                    ElementPlus.ElMessage.success(`已选择 ${availableGroups.length} 个新媒体渠道分组`);
                };
                
                const clearNewMediaGroups = () => {
                    // 清空选择
                    selectedNewMediaGroups.value = [];

                    // 更新可用分组
                    updateAvailableGroups();

                    // 提示用户
                    ElementPlus.ElMessage.info('已清空新媒体渠道分组选择');
                };

                const selectAllGonghaiGroups = () => {
                    // 获取所有未被禁用的公海渠道分组
                    const availableGroups = gonghaiGroups.value
                        .filter(group => !group.disabled)
                        .map(group => group.id);

                    // 全选
                    selectedGonghaiGroups.value = availableGroups;

                    // 更新可用分组
                    updateAvailableGroups();

                    // 提示用户
                    ElementPlus.ElMessage.success(`已选择 ${availableGroups.length} 个公海渠道分组`);
                };

                const clearGonghaiGroups = () => {
                    // 清空选择
                    selectedGonghaiGroups.value = [];

                    // 更新可用分组
                    updateAvailableGroups();

                    // 提示用户
                    ElementPlus.ElMessage.info('已清空公海渠道分组选择');
                };

                // 监听渠道选择变化
                const handleChannelChange = (value) => {
                    console.log('渠道选择变化:', value);
                    
                    // 检查电商渠道是否被取消选择
                    if (!value.includes('电商渠道') && selectedEcommerceGroups.value.length > 0) {
                        // 清空电商渠道分组选择
                        selectedEcommerceGroups.value = [];
                        console.log('电商渠道被取消选择，清空电商渠道分组选择');
                    }
                    
                    // 检查新媒体渠道是否被取消选择
                    if (!value.includes('新媒体渠道') && selectedNewMediaGroups.value.length > 0) {
                        // 清空新媒体渠道分组选择
                        selectedNewMediaGroups.value = [];
                        console.log('新媒体渠道被取消选择，清空新媒体渠道分组选择');
                    }

                    // 检查公海渠道是否被取消选择
                    if (!value.includes('公海渠道') && selectedGonghaiGroups.value.length > 0) {
                        // 清空公海渠道分组选择
                        selectedGonghaiGroups.value = [];
                        console.log('公海渠道被取消选择，清空公海渠道分组选择');
                    }

                    // 更新可用分组
                    updateAvailableGroups();
                };
                
                // 更新规则表单值
                const updateRuleFormValue = (groupId, channelType, field, value) => {
                    console.log(`更新${channelType}渠道分组${groupId}的${field}值为${value}`);
                    
                    // 确保表单对象存在
                    if (channelType === 'ecommerce') {
                        if (!ecommerceRulesForms[groupId]) {
                            ecommerceRulesForms[groupId] = [];
                        }
                        
                        if (!ecommerceRulesForms[groupId][0]) {
                            ecommerceRulesForms[groupId][0] = {
                                group_id: groupId,
                                group_name: getGroupName(groupId, ecommerceGroups.value || []),
                                distribution_ratio: value
                            };
                        } else {
                            // 直接设置字段值
                            ecommerceRulesForms[groupId][0][field] = value;
                        }
                        
                        // 强制更新以确保UI反映变化
                        ecommerceRulesForms[groupId] = [...ecommerceRulesForms[groupId]];
                    } else {
                        if (!newMediaRulesForms[groupId]) {
                            newMediaRulesForms[groupId] = [];
                        }
                        
                        if (!newMediaRulesForms[groupId][0]) {
                            newMediaRulesForms[groupId][0] = {
                                group_id: groupId,
                                group_name: getGroupName(groupId, newMediaGroups.value || []),
                                distribution_ratio: value
                            };
                        } else {
                            // 直接设置字段值
                            newMediaRulesForms[groupId][0][field] = value;
                        }
                        
                        // 强制更新以确保UI反映变化
                        newMediaRulesForms[groupId] = [...newMediaRulesForms[groupId]];
                    }
                    
                    // 打印更新后的值，用于调试
                    if (channelType === 'ecommerce') {
                        console.log('更新后的电商规则表单:', JSON.stringify(ecommerceRulesForms[groupId]));
                    } else {
                        console.log('更新后的新媒体规则表单:', JSON.stringify(newMediaRulesForms[groupId]));
                    }
                };
                
                // 滚动表格函数
                const scrollTableDown = (tableId) => {
                    const tableContainer = document.getElementById(tableId);
                    if (tableContainer) {
                        const currentScroll = tableContainer.scrollTop;
                        const scrollStep = 100; // 每次滚动100px
                        
                        // 平滑滚动
                        const scrollAnimation = setInterval(() => {
                            if (tableContainer.scrollTop < tableContainer.scrollHeight - tableContainer.clientHeight) {
                                tableContainer.scrollTop += scrollStep;
                                if (tableContainer.scrollTop >= currentScroll + 300 || 
                                    tableContainer.scrollTop >= tableContainer.scrollHeight - tableContainer.clientHeight) {
                                    clearInterval(scrollAnimation);
                                }
                            } else {
                                clearInterval(scrollAnimation);
                            }
                        }, 50);
                    }
                };
                
                // 表格单元格合并方法
                const leaderSpanMethod = ({ row, column, rowIndex, columnIndex }) => {
                    // 获取当前激活的标签页对应的表格数据
                    const tableData = activeTab.value === 'ecommerce' ? ecommerceRules.value :
                                    (activeTab.value === 'gonghai' ? gonghaiRules.value : newMediaRules.value);
                    
                    // 处理分组列（索引为2）的合并
                    if (columnIndex === 2) {
                        // 查找当前行在表格数据中的索引
                        const currentGroupName = row.group_name;
                        
                        // 查找相同分组的连续行
                        let count = 0;
                        let startIndex = -1;
                        
                        // 查找当前行在数据中的位置
                        for (let i = 0; i < tableData.length; i++) {
                            if (tableData[i].id === row.id) {
                                startIndex = i;
                                    break;
                                }
                            }
                        
                        if (startIndex === -1) return; // 未找到当前行
                        
                        // 向前查找不同分组的行
                        let prevDifferentIndex = startIndex;
                        while (prevDifferentIndex > 0) {
                            if (tableData[prevDifferentIndex - 1].group_name !== currentGroupName) {
                                break;
                            }
                            prevDifferentIndex--;
                        }
                        
                        // 向后查找不同分组的行
                        let nextDifferentIndex = startIndex;
                        while (nextDifferentIndex < tableData.length - 1) {
                            if (tableData[nextDifferentIndex + 1].group_name !== currentGroupName) {
                                    break;
                                }
                            nextDifferentIndex++;
                        }
                        
                        // 计算相同分组的行数
                        count = nextDifferentIndex - prevDifferentIndex + 1;
                        
                        // 如果是该组中的第一行，则合并单元格
                        if (startIndex === prevDifferentIndex) {
                            return {
                                rowspan: count,
                                colspan: 1
                            };
                        } else {
                            return {
                                rowspan: 0,
                                colspan: 0
                            };
                        }
                    }
                    
                    // 处理分配数量列（索引现在为3，原先为4）
                    if (columnIndex === 3) {
                        // 查找当前行在表格数据中的索引
                        const currentGroupName = row.group_name;
                        
                        // 查找相同分组的连续行
                        let count = 0;
                        let startIndex = -1;
                        
                        // 查找当前行在数据中的位置
                        for (let i = 0; i < tableData.length; i++) {
                            if (tableData[i].id === row.id) {
                                startIndex = i;
                                    break;
                                }
                            }
                        
                        if (startIndex === -1) return; // 未找到当前行
                        
                        // 向前查找不同分组的行
                        let prevDifferentIndex = startIndex;
                        while (prevDifferentIndex > 0) {
                            if (tableData[prevDifferentIndex - 1].group_name !== currentGroupName) {
                                break;
                            }
                            prevDifferentIndex--;
                        }
                        
                        // 向后查找不同分组的行
                        let nextDifferentIndex = startIndex;
                        while (nextDifferentIndex < tableData.length - 1) {
                            if (tableData[nextDifferentIndex + 1].group_name !== currentGroupName) {
                                break;
                            }
                            nextDifferentIndex++;
                        }
                        
                        // 计算相同分组的行数
                        count = nextDifferentIndex - prevDifferentIndex + 1;
                        
                        // 如果是该组中的第一行，则合并单元格
                        if (startIndex === prevDifferentIndex) {
                            return {
                                rowspan: count,
                                colspan: 1
                            };
                        } else {
                            return {
                                rowspan: 0,
                                colspan: 0
                            };
                        }
                    }
                };
                
                // 判断是否是分组中的第一行
                const isFirstInGroup = (row, index, channelType) => {
                    const tableData = channelType === 'ecommerce' ? ecommerceRules.value :
                                    (channelType === 'gonghai' ? gonghaiRules.value : newMediaRules.value);
                    
                    // 查找当前行在数据中的位置
                    let rowIndex = -1;
                    for (let i = 0; i < tableData.length; i++) {
                        if (tableData[i].id === row.id) {
                            rowIndex = i;
                            break;
                        }
                    }
                    
                    if (rowIndex === -1) return false; // 未找到当前行
                    
                    // 如果是第一行，或者前一行的分组名称不同，则是分组中的第一行
                    return rowIndex === 0 || tableData[rowIndex].group_name !== tableData[rowIndex - 1].group_name;
                };

                // 获取分组中的成员数量
                const getGroupMemberCount = (groupName, channelType) => {
                    const tableData = channelType === 'ecommerce' ? ecommerceRules.value :
                                    (channelType === 'gonghai' ? gonghaiRules.value : newMediaRules.value);
                    // 计算具有相同分组名称的行数
                    return tableData.filter(item => item.group_name === groupName).length;
                };

                // 获取分组的分配数量
                const getGroupDistributionRatio = (groupName, channelType) => {
                    // 优先从表格数据中计算
                    const tableData = channelType === 'ecommerce' ? ecommerceRules.value :
                                    (channelType === 'gonghai' ? gonghaiRules.value : newMediaRules.value);
                    const groupRules = tableData.filter(item => item.group_name === groupName);
                    
                    if (groupRules.length > 0) {
                        // 计算该分组的总预计接待数
                        const totalExpectedReception = groupRules.reduce((sum, rule) => sum + (rule.expected_total || 0), 0);
                        return totalExpectedReception;
                    }
                    
                    // 如果表格数据中没有找到，尝试从规则表单中获取
                    const rulesForms = channelType === 'ecommerce' ? ecommerceRulesForms : newMediaRulesForms;
                    
                    // 查找对应分组的ID
                    const groups = channelType === 'ecommerce' ? ecommerceGroups.value : newMediaGroups.value;
                    const group = groups.find(g => g.name === groupName);
                    
                    if (group && rulesForms[group.id] && rulesForms[group.id][0] && rulesForms[group.id][0].distribution_ratio) {
                        return rulesForms[group.id][0].distribution_ratio;
                    }
                    
                    return 0;
                };

                // 确保表头固定
                const ensureFixedHeader = () => {
                    // 使用CSS sticky定位，不需要JavaScript来处理滚动事件
                    // 这个函数可以保留，但不再需要添加滚动事件监听器
                };

                // 显示添加行对话框
                const showAddRowDialog = (row) => {
                    currentRow.value = row;
                    addRowDialogVisible.value = true;
                };

                // 关闭添加行对话框
                const handleCloseAddRowDialog = () => {
                    addRowDialogVisible.value = false;
                    currentRow.value = null;
                    addRowPosition.value = 'after';
                };

                // 添加新行
                const addNewRow = async () => {
                    if (!currentRow.value) {
                        ElementPlus.ElMessage.error('未选择行');
                        return;
                    }
                    
                    try {
                        // 显示加载提示
                        const loadingInstance = ElementPlus.ElLoading.service({
                            lock: true,
                            text: '正在添加行，请稍候...',
                            background: 'rgba(0, 0, 0, 0.7)'
                        });
                        
                        // 准备新行数据
                        const newRow = {
                            rule_date: currentDate.value,
                            channel_type: currentRow.value.channel_type,
                            group_name: currentRow.value.group_name,
                            leader: currentRow.value.leader,
                            member: '',
                            shift: '',  // 默认为空，显示为"无"
                            expected_reception: 0,
                            expected_free_reception: 0,
                            expected_paid_reception: 0,
                            store: '',
                            time_range_start: '00:00',
                            time_range_end: '00:00',
                            remarks: ''
                        };
                        
                        // 创建新行
                        const response = await axios.post('/api/distribution/rules', {
                            rule_date: currentDate.value,
                            channel_receptions: [],
                            distribution_rules: [newRow]
                        });
                        
                        // 关闭加载提示
                        loadingInstance.close();
                        
                        // 提示成功
                        ElementPlus.ElMessage.success('添加行成功');
                        
                        // 关闭对话框
                        addRowDialogVisible.value = false;
                        
                        // 重新获取规则列表
                        fetchRules();
                    } catch (error) {
                        // 关闭加载提示
                        if (loadingInstance) {
                            loadingInstance.close();
                        }
                        
                        console.error('添加行失败:', error);
                        ElementPlus.ElMessage.error('添加行失败: ' + (error.response?.data?.detail || error.message));
                    }
                };

                // 更新预计免费接待数
                const updateExpectedFree = async (row) => {
                    try {
                        await axios.put(`/api/distribution/rules/${row.id}`, {
                            channel_type: row?.channel_type,
                            group_name: row?.group_name,
                            leader: row?.leader,
                            member: row?.member,
                            shift: row?.shift,
                            expected_reception: row?.expected_total,
                            expected_free_reception: row?.expected_free,
                            expected_paid_reception: row?.expected_paid,
                            store: row?.store,
                            time_range_start: row.time_range.split('-')[0] + ':00',
                            time_range_end: row.time_range.split('-')[1] + ':00',
                            remarks: row?.remark || ''
                        });
                        ElementPlus.ElMessage.success('更新成功');
                    } catch (error) {
                        console.error('更新失败:', error);
                        ElementPlus.ElMessage.error('更新失败: ' + (error.response?.data?.detail || error.message));
                    }
                };
                
                // 更新预计付费接待数
                const updateExpectedPaid = async (row) => {
                    try {
                        await axios.put(`/api/distribution/rules/${row.id}`, {
                            channel_type: row?.channel_type,
                            group_name: row?.group_name,
                            leader: row?.leader,
                            member: row?.member,
                            shift: row?.shift,
                            expected_reception: row?.expected_total,
                            expected_free_reception: row?.expected_free,
                            expected_paid_reception: row?.expected_paid,
                            store: row?.store,
                            time_range_start: row.time_range.split('-')[0] + ':00',
                            time_range_end: row.time_range.split('-')[1] + ':00',
                            remarks: row?.remark || ''
                        });
                        ElementPlus.ElMessage.success('更新成功');
                    } catch (error) {
                        console.error('更新失败:', error);
                        ElementPlus.ElMessage.error('更新失败: ' + (error.response?.data?.detail || error.message));
                    }
                };

                // 获取分组的设定分配数量
                const getGroupSettingDistributionRatio = (groupName, channelType) => {
                    console.log(`获取分配数量: ${groupName}, ${channelType}`);
                    
                    // 转换渠道类型为API使用的格式（公海渠道复用电商渠道接口）
                    const apiChannelType = channelType === 'ecommerce' ? '电商渠道' :
                                         (channelType === 'gonghai' ? '电商渠道' : '新媒体渠道');
                    
                    // 从groupDistributionRatios中获取
                    const ratio = groupDistributionRatios.value.find(
                        r => r.group_name === groupName && r.channel_type === apiChannelType
                    );
                    
                    if (ratio) {
                        console.log(`从API数据中获取分配数量: ${ratio.distribution_ratio}`);
                        return ratio.distribution_ratio;
                    }
                    
                    // 如果没有找到，尝试从表格数据中获取
                    const tableData = channelType === 'ecommerce' ? ecommerceRules.value : newMediaRules.value;
                    const groupRules = tableData.filter(rule => rule.group_name === groupName);
                    
                    // 如果在表格数据中找到了该分组的规则
                    if (groupRules.length > 0) {
                        // 查找对应分组的ID
                        const groups = channelType === 'ecommerce' ? ecommerceGroups.value : newMediaGroups.value;
                        const group = groups.find(g => g.name === groupName);
                        
                        if (group) {
                            // 从规则表单中获取
                            const rulesForms = channelType === 'ecommerce' ? ecommerceRulesForms : newMediaRulesForms;
                            
                            if (rulesForms[group.id] && rulesForms[group.id][0] && rulesForms[group.id][0].distribution_ratio !== undefined) {
                                console.log(`从规则表单中获取分配数量: ${rulesForms[group.id][0].distribution_ratio}`);
                                return rulesForms[group.id][0].distribution_ratio;
                            } else {
                                // 如果规则表单中没有分配数量，则尝试从表格数据中计算
                                const totalExpectedReception = groupRules.reduce((sum, rule) => sum + (rule.expected_total || 0), 0);
                                console.log(`从表格数据中计算分配数量: ${totalExpectedReception}`);
                                
                                // 更新规则表单中的分配数量
                                if (!rulesForms[group.id]) {
                                    rulesForms[group.id] = [];
                                }
                                
                                if (!rulesForms[group.id][0]) {
                                    rulesForms[group.id][0] = {
                                        group_id: group.id,
                                        group_name: groupName,
                                        distribution_ratio: totalExpectedReception
                                    };
                                } else {
                                    rulesForms[group.id][0].distribution_ratio = totalExpectedReception;
                                }
                                
                                return totalExpectedReception;
                            }
                        }
                    }
                    
                    console.log(`未找到分配数量，返回0`);
                    return 0;
                };
                
                // 获取分组的实际分配数量（从表格数据中计算）
                const getGroupActualDistributionRatio = (groupName, channelType) => {
                    // 从表格数据中计算
                    const tableData = channelType === 'ecommerce' ? ecommerceRules.value : newMediaRules.value;
                    const groupRules = tableData.filter(item => item.group_name === groupName);
                    
                    if (groupRules.length > 0) {
                        // 计算该分组的总预计接待数
                        const totalExpectedReception = groupRules.reduce((sum, rule) => sum + (rule.expected_total || 0), 0);
                        return totalExpectedReception;
                    }
                    
                    return 0;
                };

                // 计算表格中所有规则的预计接待数总和
                const calculateCurrentTotalReception = (channelType) => {
                    const rules = channelType === 'ecommerce' ? ecommerceRules.value : newMediaRules.value;
                    if (!rules || rules.length === 0) return 0;
                    return rules.reduce((sum, rule) => sum + (rule.expected_total || 0), 0);
                };

                // 计算表格中所有规则的预计免费接待数总和
                const calculateCurrentFreeReception = (channelType) => {
                    const rules = channelType === 'ecommerce' ? ecommerceRules.value : newMediaRules.value;
                    if (!rules || rules.length === 0) return 0;
                    return rules.reduce((sum, rule) => sum + (rule.expected_free || 0), 0);
                };

                // 计算表格中所有规则的预计付费接待数总和
                const calculateCurrentPaidReception = (channelType) => {
                    const rules = channelType === 'ecommerce' ? ecommerceRules.value : newMediaRules.value;
                    if (!rules || rules.length === 0) return 0;
                    return rules.reduce((sum, rule) => sum + (rule.expected_paid || 0), 0);
                };

                // 计算表格中所有规则的实际接待数总和
                const calculateActualTotalReception = (channelType) => {
                    const rules = channelType === 'ecommerce' ? ecommerceRules.value :
                                (channelType === 'gonghai' ? gonghaiRules.value : newMediaRules.value);
                    if (!rules || rules.length === 0) return 0;
                    return rules.reduce((sum, rule) => sum + (rule.actual_total || 0), 0);
                };

                // 计算表格中所有规则的实际免费接待数总和
                const calculateActualFreeReception = (channelType) => {
                    const rules = channelType === 'ecommerce' ? ecommerceRules.value :
                                (channelType === 'gonghai' ? gonghaiRules.value : newMediaRules.value);
                    if (!rules || rules.length === 0) return 0;
                    return rules.reduce((sum, rule) => sum + (rule.actual_free || 0), 0);
                };

                // 计算表格中所有规则的实际付费接待数总和
                const calculateActualPaidReception = (channelType) => {
                    const rules = channelType === 'ecommerce' ? ecommerceRules.value :
                                (channelType === 'gonghai' ? gonghaiRules.value : newMediaRules.value);
                    if (!rules || rules.length === 0) return 0;
                    return rules.reduce((sum, rule) => sum + (rule.actual_paid || 0), 0);
                };

                // 更新分组的设定分配数量
                const updateGroupSettingDistributionRatio = async (groupName, channelType, value) => {
                    try {
                        console.log(`开始更新分配数量: ${groupName}, ${channelType}, ${value}`);

                        // 直接从表格数据中获取分组信息
                        const tableData = channelType === 'ecommerce' ? ecommerceRules.value :
                                        (channelType === 'gonghai' ? gonghaiRules.value : newMediaRules.value);
                        const groupRules = tableData.filter(rule => rule.group_name === groupName);
                        
                        if (groupRules.length === 0) {
                            console.error(`在表格数据中未找到分组: ${groupName}`);
                            ElementPlus.ElMessage.error(`未找到分组: ${groupName}，无法更新分配数量`);
                            return;
                        }
                        
                        console.log(`在表格数据中找到分组: ${groupName}，共有 ${groupRules.length} 条规则`);
                        
                        // 直接使用分组名称作为ID
                        const groupId = groupName;
                        console.log(`使用分组名称作为ID: ${groupId}`);
                        
                        // 更新规则表单中的分配数量
                        const rulesForms = channelType === 'ecommerce' ? ecommerceRulesForms : newMediaRulesForms;
                        
                        if (!rulesForms[groupId]) {
                            console.log(`初始化规则表单: ${groupId}`);
                            rulesForms[groupId] = [];
                        }
                        
                        if (!rulesForms[groupId][0]) {
                            console.log(`初始化规则表单项: ${groupId}[0]`);
                            rulesForms[groupId][0] = {
                                group_id: groupId,
                                group_name: groupName,
                                distribution_ratio: value
                            };
                        } else {
                            console.log(`更新规则表单项: ${groupId}[0].distribution_ratio = ${value}`);
                            rulesForms[groupId][0].distribution_ratio = value;
                        }
                        
                        // 保存到数据库
                        try {
                            // 转换渠道类型为API需要的格式
                            const apiChannelType = channelType === 'ecommerce' ? '电商渠道' : '新媒体渠道';
                            console.log(`API渠道类型: ${apiChannelType}`);
                            
                            const response = await axios.post('/api/distribution/group-ratios', {
                                rule_date: currentDate.value,
                                channel_type: apiChannelType,
                                group_name: groupName,
                                distribution_ratio: value
                            });
                            
                            console.log('保存分配数量成功:', response.data);
                            
                            // 更新groupDistributionRatios变量
                            const existingRatioIndex = groupDistributionRatios.value.findIndex(
                                r => r.group_name === groupName && r.channel_type === apiChannelType
                            );
                            
                            if (existingRatioIndex !== -1) {
                                // 如果已存在，则更新
                                console.log(`更新groupDistributionRatios[${existingRatioIndex}].distribution_ratio = ${value}`);
                                groupDistributionRatios.value[existingRatioIndex].distribution_ratio = value;
                            } else {
                                // 如果不存在，则添加
                                console.log(`添加新的分配数量记录到groupDistributionRatios`);
                                groupDistributionRatios.value.push({
                                    id: response.data.id,
                                    rule_date: currentDate.value,
                                    channel_type: apiChannelType,
                                    group_name: groupName,
                                    distribution_ratio: value,
                                    created_at: response.data.created_at,
                                    updated_at: response.data.updated_at
                                });
                            }
                            
                            // 更新表格数据中的tempDistributionRatio
                            const tableRules = channelType === 'ecommerce' ? ecommerceRules.value : newMediaRules.value;
                            tableRules.forEach(rule => {
                                if (rule.group_name === groupName) {
                                    rule.tempDistributionRatio = value;
                                }
                            });
                            
                            // 强制更新视图
                            if (channelType === 'ecommerce') {
                                // 创建一个新的数组引用，触发Vue的响应式更新
                                console.log('更新电商渠道视图');
                                ecommerceRules.value = [...ecommerceRules.value];
                            } else {
                                // 创建一个新的数组引用，触发Vue的响应式更新
                                console.log('更新新媒体渠道视图');
                                newMediaRules.value = [...newMediaRules.value];
                            }
                            
                            console.log(`已更新${channelType === 'ecommerce' ? '电商' : '新媒体'}渠道 ${groupName} 的分配数量为: ${value}`);
                            
                            // 显示成功消息
                            ElementPlus.ElMessage.success(`已更新${channelType === 'ecommerce' ? '电商' : '新媒体'}渠道 ${groupName} 的分配数量为: ${value}`);
                        } catch (error) {
                            console.error('保存分配数量失败:', error);
                            ElementPlus.ElMessage.error('保存分配数量失败: ' + (error.response?.data?.detail || error.message));
                        }
                    } catch (error) {
                        console.error(`更新分配数量时发生错误:`, error);
                        ElementPlus.ElMessage.error(`更新分配数量时发生错误: ${error.message}`);
                    }
                };

                // 更新规则的人员
                const updateRuleMember = async (ruleId, member) => {
                    try {
                        console.log(`开始更新规则人员: ${ruleId}, ${member}`);
                        
                        // 查找当前规则数据
                        let rule = null;
                        let tableData = [];
                        
                        // 先在电商渠道规则中查找
                        if (ecommerceRules.value && ecommerceRules.value.length > 0) {
                            rule = ecommerceRules.value.find(r => r.id === ruleId);
                            tableData = ecommerceRules.value;
                        }
                        
                        // 如果在电商渠道规则中未找到，则在新媒体渠道规则中查找
                        if (!rule && newMediaRules.value && newMediaRules.value.length > 0) {
                            rule = newMediaRules.value.find(r => r.id === ruleId);
                            tableData = newMediaRules.value;
                        }
                        
                        if (!rule) {
                            console.error(`未找到规则: ${ruleId}`);
                            ElementPlus.ElMessage.error(`未找到规则: ${ruleId}，无法更新人员`);
                            return;
                        }
                        
                        console.log('找到规则:', rule);
                        
                        // 验证同一分组内是否有相同人员和相同班次的组合
                        const sameGroupRules = tableData.filter(r => 
                            r.group_name === rule.group_name && r.id !== ruleId
                        );
                        
                        const duplicateMemberAndShift = sameGroupRules.find(r => 
                            r.member === member && r.shift === rule.shift
                        );
                        if (duplicateMemberAndShift) {
                            console.error(`分组 ${rule.group_name} 内已存在相同的人员和班次组合: ${member} - ${rule.shift}`);
                            ElementPlus.ElMessage.error(`分组 ${rule.group_name} 内已存在相同的人员和班次组合: ${member} - ${rule.shift}，无法保存`);
                            return;
                        }
                        
                        // 发送更新请求，包含完整的规则数据
                        const requestData = {
                            channel_type: rule.channel_type,
                            group_name: rule.group_name,
                            leader: rule.leader,
                            member: member,
                            shift: rule.shift || '',
                            expected_reception: rule.expected_reception || rule.expected_total || 0,
                            expected_free_reception: rule.expected_free_reception || rule.expected_free || 0,
                            expected_paid_reception: rule.expected_paid_reception || rule.expected_paid || 0,
                            store: rule.store || '',
                            time_range_start: rule.time_range_start || (rule.time_range ? rule.time_range.split('-')[0] + ':00' : '00:00:00'),
                            time_range_end: rule.time_range_end || (rule.time_range ? rule.time_range.split('-')[1] + ':00' : '00:00:00'),
                            remarks: rule.remarks || rule.remark || ''
                        };
                        
                        console.log('发送更新请求数据:', requestData);
                        
                        const response = await axios.put(`/api/distribution/rules/${ruleId}`, requestData);
                        
                        console.log('更新规则人员成功:', response.data);
                        
                        // 更新本地数据
                        rule.member = member;
                        
                        // 强制更新视图
                        if (rule.channel_type === '电商渠道') {
                            ecommerceRules.value = [...ecommerceRules.value];
                        } else {
                            newMediaRules.value = [...newMediaRules.value];
                        }
                        
                        // 显示成功消息
                        ElementPlus.ElMessage.success('更新人员成功');
                    } catch (error) {
                        console.error('更新规则人员失败:', error);
                        ElementPlus.ElMessage.error('更新规则人员失败: ' + (error.response?.data?.detail || error.message));
                    }
                };
                
                // 更新规则的班次
                const updateRuleShift = async (ruleId, shift) => {
                    try {
                        console.log(`开始更新规则班次: ${ruleId}, ${shift}`);
                        
                        // 查找当前规则数据
                        let rule = null;
                        let tableData = [];
                        
                        // 先在电商渠道规则中查找
                        if (ecommerceRules.value && ecommerceRules.value.length > 0) {
                            rule = ecommerceRules.value.find(r => r.id === ruleId);
                            tableData = ecommerceRules.value;
                        }
                        
                        // 如果在电商渠道规则中未找到，则在新媒体渠道规则中查找
                        if (!rule && newMediaRules.value && newMediaRules.value.length > 0) {
                            rule = newMediaRules.value.find(r => r.id === ruleId);
                            tableData = newMediaRules.value;
                        }
                        
                        if (!rule) {
                            console.error(`未找到规则: ${ruleId}`);
                            ElementPlus.ElMessage.error(`未找到规则: ${ruleId}，无法更新班次`);
                            return;
                        }
                        
                        console.log('找到规则:', rule);
                        
                        // 验证同一分组内是否有相同人员和相同班次的组合
                        const sameGroupRules = tableData.filter(r => 
                            r.group_name === rule.group_name && r.id !== ruleId
                        );
                        
                        const duplicateMemberAndShift = sameGroupRules.find(r => 
                            r.member === rule.member && r.shift === shift
                        );
                        if (duplicateMemberAndShift) {
                            console.error(`分组 ${rule.group_name} 内已存在相同的人员和班次组合: ${rule.member} - ${shift}`);
                            ElementPlus.ElMessage.error(`分组 ${rule.group_name} 内已存在相同的人员和班次组合: ${rule.member} - ${shift}，无法保存`);
                            return;
                        }
                        
                        // 根据班次设置时间段
                        let timeRangeStart = '00:00:00';
                        let timeRangeEnd = '00:00:00';
                        
                        if (shift === '白班') {
                            timeRangeStart = '09:00:00';
                            timeRangeEnd = '18:30:00';
                        } else if (shift === '午班') {
                            timeRangeStart = '13:30:00';
                            timeRangeEnd = '23:59:59';
                        } else if (shift === '晚班') {
                            timeRangeStart = '18:30:00';
                            timeRangeEnd = '23:59:59';
                        } else if (shift === '全天') {
                            timeRangeStart = '09:00:00';
                            timeRangeEnd = '23:59:59';
                        }
                        
                        // 发送更新请求，包含完整的规则数据
                        const response = await axios.put(`/api/distribution/rules/${ruleId}`, {
                            channel_type: rule?.channel_type,
                            group_name: rule?.group_name,
                            leader: rule?.leader,
                            member: rule?.member,
                            shift: shift,
                            expected_reception: rule?.expected_total || 0,
                            expected_free_reception: rule?.expected_free || 0,
                            expected_paid_reception: rule?.expected_paid || 0,
                            store: rule.store || '',
                            time_range_start: `${currentDate.value}T${timeRangeStart}`,
                            time_range_end: `${currentDate.value}T${timeRangeEnd}`,
                            remarks: rule?.remark || ''
                        });
                        
                        console.log('更新规则班次成功:', response.data);
                        
                        // 更新本地数据
                        rule.shift = shift;
                        rule.time_range_start = `${currentDate.value}T${timeRangeStart}`;
                        rule.time_range_end = `${currentDate.value}T${timeRangeEnd}`;
                        rule.time_range = `${timeRangeStart.substring(0, 5)}-${timeRangeEnd.substring(0, 5)}`;
                        
                        console.log('更新后的时间段:', rule.time_range_start, rule.time_range_end, rule.time_range);
                        
                        // 强制更新视图
                        if (rule.channel_type === '电商渠道') {
                            ecommerceRules.value = [...ecommerceRules.value];
                        } else {
                            newMediaRules.value = [...newMediaRules.value];
                        }
                        
                        // 显示成功消息
                        ElementPlus.ElMessage.success('更新班次成功');
                    } catch (error) {
                        console.error('更新规则班次失败:', error);
                        ElementPlus.ElMessage.error('更新规则班次失败: ' + (error.response?.data?.detail || error.message));
                    }
                };

                // 获取所有销售组数据，用于处理人员编辑
                const fetchAllSalesGroups = async () => {
                    try {
                        const response = await axios.get('/api/presets/sales');
                        allSalesGroups.value = response.data.sales || [];
                        console.log('获取所有销售组数据成功:', allSalesGroups.value);
                    } catch (error) {
                        console.error('获取销售组数据失败:', error);
                        ElementPlus.ElMessage.error('获取销售组数据失败: ' + (error.response?.data?.detail || error.message));
                    }
                };

                // 更新规则的预计接待数
                const updateRuleExpectedReception = async (ruleId, expectedReception) => {
                    try {
                        console.log(`开始更新规则预计接待数: ${ruleId}, ${expectedReception}`);
                        
                        // 查找当前规则数据
                        let rule = null;
                        let tableData = [];
                        
                        // 先在电商渠道规则中查找
                        if (ecommerceRules.value && ecommerceRules.value.length > 0) {
                            rule = ecommerceRules.value.find(r => r.id === ruleId);
                            tableData = ecommerceRules.value;
                        }
                        
                        // 如果在电商渠道规则中未找到，则在新媒体渠道规则中查找
                        if (!rule && newMediaRules.value && newMediaRules.value.length > 0) {
                            rule = newMediaRules.value.find(r => r.id === ruleId);
                            tableData = newMediaRules.value;
                        }
                        
                        if (!rule) {
                            console.error(`未找到规则: ${ruleId}`);
                            ElementPlus.ElMessage.error(`未找到规则: ${ruleId}，无法更新预计接待数`);
                            return;
                        }
                        
                        console.log('找到规则:', rule);
                        
                        // 生成请求数据
                        const requestData = {
                            channel_type: rule?.channel_type,
                            group_name: rule?.group_name,
                            leader: rule?.leader,
                            member: rule?.member,
                            shift: rule?.shift,
                            expected_reception: parseInt(expectedReception),
                            expected_free_reception: 0,
                            expected_paid_reception: 0,
                            store: rule?.store,
                            time_range_start: rule?.time_range_start || (rule?.time_range ? rule?.time_range.split('-')[0] + ':00' : '00:00:00'),
                            time_range_end: rule?.time_range_end || (rule?.time_range ? rule?.time_range.split('-')[1] + ':00' : '00:00:00'),
                            remarks: rule.remarks || rule.remark || ''
                        };
                        
                        console.log('发送更新请求数据:', JSON.stringify(requestData));
                        
                        try {
                            const response = await axios.put(`/api/distribution/rules/${ruleId}`, requestData);
                            console.log('更新规则预计接待数成功, 服务器响应:', response.data);
                            
                            // 更新本地数据
                            if (rule) {
                                rule.expected_total = parseInt(expectedReception);
                                rule.expected_reception = parseInt(expectedReception);
                            }

                            // 强制更新视图
                            if (rule?.channel_type === '电商渠道') {
                                ecommerceRules.value = [...ecommerceRules.value];
                            } else {
                                newMediaRules.value = [...newMediaRules.value];
                            }
                            
                            // 验证更新是否成功
                            const verifyResponse = await axios.get(`/api/distribution/rules/${ruleId}`);
                            console.log('验证更新结果:', verifyResponse.data);
                            
                            ElementPlus.ElMessage.success(`成功更新规则预计接待数: ${rule.channel_type} ${rule.group_name}`);
                        } catch (innerError) {
                            console.error('API调用失败:', innerError);
                            ElementPlus.ElMessage.error(`API调用失败: ${innerError.message}`);
                            if (innerError.response) {
                                console.error('服务器响应:', innerError.response.data);
                            }
                            throw innerError;
                        }
                        
                    } catch (error) {
                        console.error('更新规则预计接待数失败:', error);
                        ElementPlus.ElMessage.error(`更新规则预计接待数失败: ${error.message}`);
                    }
                };

                const updateRuleFreeReception = async (ruleId, expectedFreeReception) => {
                    try {
                        console.log(`开始更新规则预计免费接待数: ${ruleId}, ${expectedFreeReception}`);
                        
                        // 查找当前规则数据
                        let rule = null;
                        let tableData = [];
                        
                        // 先在电商渠道规则中查找
                        if (ecommerceRules.value && ecommerceRules.value.length > 0) {
                            rule = ecommerceRules.value.find(r => r.id === ruleId);
                            tableData = ecommerceRules.value;
                        }
                        
                        // 如果在电商渠道规则中未找到，则在新媒体渠道规则中查找
                        if (!rule && newMediaRules.value && newMediaRules.value.length > 0) {
                            rule = newMediaRules.value.find(r => r.id === ruleId);
                            tableData = newMediaRules.value;
                        }
                        
                        if (!rule) {
                            console.error(`未找到规则: ${ruleId}`);
                            ElementPlus.ElMessage.error(`未找到规则: ${ruleId}，无法更新预计免费接待数`);
                            return;
                        }
                        
                        console.log('找到规则:', rule);
                        
                        // 生成请求数据
                        const requestData = {
                            channel_type: rule.channel_type,
                            group_name: rule.group_name,
                            leader: rule.leader,
                            member: rule.member,
                            shift: rule.shift || '',
                            expected_reception: rule.expected_reception || rule.expected_total || 0,
                            expected_free_reception: parseInt(expectedFreeReception),
                            expected_paid_reception: rule.expected_paid_reception || rule.expected_paid || 0,
                            store: rule.store || '',
                            time_range_start: rule.time_range_start || (rule.time_range ? rule.time_range.split('-')[0] + ':00' : '00:00:00'),
                            time_range_end: rule.time_range_end || (rule.time_range ? rule.time_range.split('-')[1] + ':00' : '00:00:00'),
                            remarks: rule.remarks || rule.remark || ''
                        };
                        
                        console.log('发送更新请求数据:', JSON.stringify(requestData));
                        
                        try {
                            const response = await axios.put(`/api/distribution/rules/${ruleId}`, requestData);
                            console.log('更新规则预计免费接待数成功, 服务器响应:', response.data);
                            
                            // 更新本地数据
                            rule.expected_free = parseInt(expectedFreeReception);
                            rule.expected_free_reception = parseInt(expectedFreeReception);
                            
                            // 强制更新视图
                            if (rule.channel_type === '电商渠道') {
                                ecommerceRules.value = [...ecommerceRules.value];
                            } else {
                                newMediaRules.value = [...newMediaRules.value];
                            }
                            
                            // 验证更新是否成功
                            const verifyResponse = await axios.get(`/api/distribution/rules/${ruleId}`);
                            console.log('验证更新结果:', verifyResponse.data);
                            
                            ElementPlus.ElMessage.success(`成功更新规则预计免费接待数: ${rule.channel_type} ${rule.group_name}`);
                        } catch (innerError) {
                            console.error('API调用失败:', innerError);
                            ElementPlus.ElMessage.error(`API调用失败: ${innerError.message}`);
                            if (innerError.response) {
                                console.error('服务器响应:', innerError.response.data);
                            }
                            throw innerError;
                        }
                        
                    } catch (error) {
                        console.error('更新规则预计免费接待数失败:', error);
                        ElementPlus.ElMessage.error(`更新规则预计免费接待数失败: ${error.message}`);
                    }
                };

                const updateRulePaidReception = async (ruleId, expectedPaidReception) => {
                    try {
                        console.log(`开始更新规则预计付费接待数: ${ruleId}, ${expectedPaidReception}`);
                        
                        // 查找当前规则数据
                        let rule = null;
                        let tableData = [];
                        
                        // 先在电商渠道规则中查找
                        if (ecommerceRules.value && ecommerceRules.value.length > 0) {
                            rule = ecommerceRules.value.find(r => r.id === ruleId);
                            tableData = ecommerceRules.value;
                        }
                        
                        // 如果在电商渠道规则中未找到，则在新媒体渠道规则中查找
                        if (!rule && newMediaRules.value && newMediaRules.value.length > 0) {
                            rule = newMediaRules.value.find(r => r.id === ruleId);
                            tableData = newMediaRules.value;
                        }
                        
                        if (!rule) {
                            console.error(`未找到规则: ${ruleId}`);
                            ElementPlus.ElMessage.error(`未找到规则: ${ruleId}，无法更新预计付费接待数`);
                            return;
                        }
                        
                        console.log('找到规则:', rule);
                        
                        // 生成请求数据
                        const requestData = {
                            channel_type: rule.channel_type,
                            group_name: rule.group_name,
                            leader: rule.leader,
                            member: rule.member,
                            shift: rule.shift || '',
                            expected_reception: rule.expected_reception || rule.expected_total || 0,
                            expected_free_reception: rule.expected_free_reception || rule.expected_free || 0,
                            expected_paid_reception: parseInt(expectedPaidReception),
                            store: rule.store || '',
                            time_range_start: rule.time_range_start || (rule.time_range ? rule.time_range.split('-')[0] + ':00' : '00:00:00'),
                            time_range_end: rule.time_range_end || (rule.time_range ? rule.time_range.split('-')[1] + ':00' : '00:00:00'),
                            remarks: rule.remarks || rule.remark || ''
                        };
                        
                        console.log('发送更新请求数据:', JSON.stringify(requestData));
                        
                        try {
                            const response = await axios.put(`/api/distribution/rules/${ruleId}`, requestData);
                            console.log('更新规则预计付费接待数成功, 服务器响应:', response.data);
                            
                            // 更新本地数据
                            rule.expected_paid = parseInt(expectedPaidReception);
                            rule.expected_paid_reception = parseInt(expectedPaidReception);
                            
                            // 强制更新视图
                            if (rule.channel_type === '电商渠道') {
                                ecommerceRules.value = [...ecommerceRules.value];
                            } else {
                                newMediaRules.value = [...newMediaRules.value];
                            }
                            
                            // 验证更新是否成功
                            const verifyResponse = await axios.get(`/api/distribution/rules/${ruleId}`);
                            console.log('验证更新结果:', verifyResponse.data);
                            
                            ElementPlus.ElMessage.success(`成功更新规则预计付费接待数: ${rule.channel_type} ${rule.group_name}`);
                        } catch (innerError) {
                            console.error('API调用失败:', innerError);
                            ElementPlus.ElMessage.error(`API调用失败: ${innerError.message}`);
                            if (innerError.response) {
                                console.error('服务器响应:', innerError.response.data);
                            }
                            throw innerError;
                        }
                        
                    } catch (error) {
                        console.error('更新规则预计付费接待数失败:', error);
                        ElementPlus.ElMessage.error(`更新规则预计付费接待数失败: ${error.message}`);
                    }
                };

                // 添加上方字段编辑相关的变量
                const showEcommerceExpectedReceptionEditIcon = ref(false);
                const isEditingEcommerceExpectedReception = ref(false);
                const tempEcommerceExpectedReception = ref(0);
                
                const showEcommerceExpectedFreeReceptionEditIcon = ref(false);
                const isEditingEcommerceExpectedFreeReception = ref(false);
                const tempEcommerceExpectedFreeReception = ref(0);
                
                const showEcommerceExpectedPaidReceptionEditIcon = ref(false);
                const isEditingEcommerceExpectedPaidReception = ref(false);
                const tempEcommerceExpectedPaidReception = ref(0);
                
                const showNewMediaExpectedReceptionEditIcon = ref(false);
                const isEditingNewMediaExpectedReception = ref(false);
                const tempNewMediaExpectedReception = ref(0);
                
                const showNewMediaExpectedFreeReceptionEditIcon = ref(false);
                const isEditingNewMediaExpectedFreeReception = ref(false);
                const tempNewMediaExpectedFreeReception = ref(0);
                
                const showNewMediaExpectedPaidReceptionEditIcon = ref(false);
                const isEditingNewMediaExpectedPaidReception = ref(false);
                const tempNewMediaExpectedPaidReception = ref(0);

                const showGonghaiExpectedReceptionEditIcon = ref(false);
                const isEditingGonghaiExpectedReception = ref(false);
                const tempGonghaiExpectedReception = ref(0);

                // 更新渠道预计接待数
                const updateChannelExpectedReception = async (channelType, expectedReception) => {
                    try {
                        console.log(`开始更新${channelType === 'ecommerce' ? '电商' : (channelType === 'gonghai' ? '公海' : '新媒体')}渠道预计接待数: ${expectedReception}`);
                        const channelName = channelType === 'ecommerce' ? '电商渠道' : (channelType === 'gonghai' ? '电商渠道' : '新媒体渠道');

                        // 获取当前日期
                        const date = currentDate.value || formatDate(new Date());

                        // 发送API请求更新渠道接待数
                        const response = await axios.put(`/api/distribution/reception/${date}/${channelName}`, {
                            expected_reception: parseInt(expectedReception)
                        });

                        console.log('更新渠道预计接待数成功:', response.data);

                        // 更新本地数据
                        if (channelType === 'ecommerce') {
                            ecommerceReception.value.expected_reception = parseInt(expectedReception);
                            isEditingEcommerceExpectedReception.value = false;
                        } else if (channelType === 'gonghai') {
                            gonghaiReception.value.expected_reception = parseInt(expectedReception);
                            isEditingGonghaiExpectedReception.value = false;
                        } else {
                            newMediaReception.value.expected_reception = parseInt(expectedReception);
                            isEditingNewMediaExpectedReception.value = false;
                        }

                        ElementPlus.ElMessage.success(`成功更新${channelName}预计接待数`);
                    } catch (error) {
                        console.error(`更新${channelType === 'ecommerce' ? '电商' : (channelType === 'gonghai' ? '公海' : '新媒体')}渠道预计接待数失败:`, error);
                        ElementPlus.ElMessage.error(`更新预计接待数失败: ${error.message}`);
                    }
                };
                
                // 更新渠道预计免费接待数
                const updateChannelExpectedFreeReception = async (channelType, expectedFreeReception) => {
                    try {
                        console.log(`开始更新${channelType === 'ecommerce' ? '电商' : '新媒体'}渠道预计免费接待数: ${expectedFreeReception}`);
                        const channelName = channelType === 'ecommerce' ? '电商渠道' : '新媒体渠道';
                        
                        // 获取当前日期
                        const date = currentDate.value || formatDate(new Date());
                        
                        // 发送API请求更新渠道接待数
                        const response = await axios.put(`/api/distribution/reception/${date}/${channelName}`, {
                            expected_free_reception: parseInt(expectedFreeReception)
                        });
                        
                        console.log('更新渠道预计免费接待数成功:', response.data);
                        
                        // 更新本地数据
                        if (channelType === 'ecommerce') {
                            ecommerceReception.value.expected_free_reception = parseInt(expectedFreeReception);
                            isEditingEcommerceExpectedFreeReception.value = false;
                        } else {
                            newMediaReception.value.expected_free_reception = parseInt(expectedFreeReception);
                            isEditingNewMediaExpectedFreeReception.value = false;
                        }
                        
                        ElementPlus.ElMessage.success(`成功更新${channelName}预计免费接待数`);
                    } catch (error) {
                        console.error(`更新${channelType === 'ecommerce' ? '电商' : '新媒体'}渠道预计免费接待数失败:`, error);
                        ElementPlus.ElMessage.error(`更新预计免费接待数失败: ${error.message}`);
                    }
                };
                
                // 更新渠道预计付费接待数
                const updateChannelExpectedPaidReception = async (channelType, expectedPaidReception) => {
                    try {
                        console.log(`开始更新${channelType === 'ecommerce' ? '电商' : '新媒体'}渠道预计付费接待数: ${expectedPaidReception}`);
                        const channelName = channelType === 'ecommerce' ? '电商渠道' : '新媒体渠道';
                        
                        // 获取当前日期
                        const date = currentDate.value || formatDate(new Date());
                        
                        // 发送API请求更新渠道接待数
                        const response = await axios.put(`/api/distribution/reception/${date}/${channelName}`, {
                            expected_paid_reception: parseInt(expectedPaidReception)
                        });
                        
                        console.log('更新渠道预计付费接待数成功:', response.data);
                        
                        // 更新本地数据
                        if (channelType === 'ecommerce') {
                            ecommerceReception.value.expected_paid_reception = parseInt(expectedPaidReception);
                            isEditingEcommerceExpectedPaidReception.value = false;
                        } else {
                            newMediaReception.value.expected_paid_reception = parseInt(expectedPaidReception);
                            isEditingNewMediaExpectedPaidReception.value = false;
                        }
                        
                        ElementPlus.ElMessage.success(`成功更新${channelName}预计付费接待数`);
                    } catch (error) {
                        console.error(`更新${channelType === 'ecommerce' ? '电商' : '新媒体'}渠道预计付费接待数失败:`, error);
                        ElementPlus.ElMessage.error(`更新预计付费接待数失败: ${error.message}`);
                    }
                };

              // 添加getCurrentReceptionStyle函数到setup中
                const getCurrentReceptionStyle = (channelType) => {
                    const currentReception = calculateCurrentTotalReception(channelType);
                    const expectedReception = channelType === 'ecommerce' 
                        ? (ecommerceReception.value?.expected_reception || 0) 
                        : (newMediaReception.value?.expected_reception || 0);
                    
                    if (currentReception > expectedReception) {
                        return 'color: #F56C6C; font-weight: bold;'; // 红色
                    } else if (currentReception < expectedReception) {
                        return 'color: #E6A23C; font-weight: bold;'; // 橙色
                    } else if (currentReception === expectedReception && expectedReception > 0) {
                        return 'color: #67C23A; font-weight: bold;'; // 绿色
                    }
                    return '';
                };

                // 添加getCurrentFreeReceptionStyle函数到setup中
                const getCurrentFreeReceptionStyle = (channelType) => {
                    const currentFreeReception = calculateCurrentFreeReception(channelType);
                    const expectedFreeReception = channelType === 'ecommerce' 
                        ? (ecommerceReception.value?.expected_free_reception || 0) 
                        : (newMediaReception.value?.expected_free_reception || 0);
                    
                    if (currentFreeReception > expectedFreeReception) {
                        return 'color: #F56C6C; font-weight: bold;'; // 红色
                    } else if (currentFreeReception < expectedFreeReception) {
                        return 'color: #E6A23C; font-weight: bold;'; // 橙色
                    } else if (currentFreeReception === expectedFreeReception && expectedFreeReception > 0) {
                        return 'color: #67C23A; font-weight: bold;'; // 绿色
                    }
                    return '';
                };

                // 添加getCurrentPaidReceptionStyle函数到setup中
                const getCurrentPaidReceptionStyle = (channelType) => {
                    const currentPaidReception = calculateCurrentPaidReception(channelType);
                    const expectedPaidReception = channelType === 'ecommerce' 
                        ? (ecommerceReception.value?.expected_paid_reception || 0) 
                        : (newMediaReception.value?.expected_paid_reception || 0);
                    
                    if (currentPaidReception > expectedPaidReception) {
                        return 'color: #F56C6C; font-weight: bold;'; // 红色
                    } else if (currentPaidReception < expectedPaidReception) {
                        return 'color: #E6A23C; font-weight: bold;'; // 橙色
                    } else if (currentPaidReception === expectedPaidReception && expectedPaidReception > 0) {
                        return 'color: #67C23A; font-weight: bold;'; // 绿色
                    }
                    return '';
                };

                // 店铺选择相关变量
                const storeSelectionDialogVisible = ref(false);
                const storeSearchKeyword = ref('');
                const currentEditingRule = ref(null);
                const selectedStores = ref([]); // 添加存储已选择店铺的数组
                const isQuickBatchStoreSelection = ref(false); // 标记是否是批量修改排班的店铺选择

                // 表格引用
                const ecommerceTable = ref(null);
                const newMediaTable = ref(null);
                const gonghaiTable = ref(null);

                // 店铺筛选 - 根据店铺名称和缩写进行筛选
                const filteredStores = computed(() => {
                    if (!storeSearchKeyword.value) {
                        return stores.value;
                    }
                    const keyword = storeSearchKeyword.value.toLowerCase();
                    return stores.value.filter(store => 
                        store.name.toLowerCase().includes(keyword) ||
                        (store.douyin_abbr && store.douyin_abbr.toLowerCase().includes(keyword)) ||
                        (store.video_abbr && store.video_abbr.toLowerCase().includes(keyword)) ||
                        (store.xiaohongshu_abbr && store.xiaohongshu_abbr.toLowerCase().includes(keyword)) ||
                        (store.kuaishou_abbr && store.kuaishou_abbr.toLowerCase().includes(keyword)) ||
                        (store.manager && store.manager.toLowerCase().includes(keyword))
                    );
                });

                // 打开店铺选择对话框
                const showStoreSelection = (row) => {
                    currentEditingRule.value = row;
                    selectedStores.value = []; // 清空已选择的店铺
                    
                    // 如果当前行已有店铺，预先选中
                    if (row.store) {
                        // 分割逗号分隔的店铺名称
                        const existingStores = row.store.split(',').map(store => store.trim()).filter(store => store);
                        selectedStores.value = existingStores;
                    }
                    
                    storeSelectionDialogVisible.value = true;
                    storeSearchKeyword.value = ''; // 清空搜索关键词
                    
                    // 每次打开对话框时都重新获取店铺数据，确保数据同步
                    fetchStores().then(() => {
                        ElementPlus.ElMessage.success('店铺数据已更新');
                    }).catch(() => {
                        ElementPlus.ElMessage.warning('使用本地店铺数据');
                    });
                };

                // 选择店铺
                const selectStore = async (store, abbrType) => {
                    // 如果缩写类型不是允许的类型，直接返回
                    if (!['douyin_abbr', 'video_abbr', 'xiaohongshu_abbr', 'kuaishou_abbr'].includes(abbrType)) {
                        return;
                    }
                    
                    // 获取要添加的店铺缩写
                    const storeAbbr = store[abbrType] || '';
                    if (!storeAbbr) {
                        ElementPlus.ElMessage.warning('该店铺没有对应的缩写');
                        return;
                    }

                    // 检查是否已经选择
                    const storeIndex = selectedStores.value.findIndex(item => item === storeAbbr);
                    if (storeIndex !== -1) {
                        // 如果已选择，则移除（切换选择状态）
                        selectedStores.value.splice(storeIndex, 1);
                        ElementPlus.ElMessage.info(`已取消选择: ${storeAbbr}`);
                    } else {
                        // 如果未选择，则添加
                        selectedStores.value.push(storeAbbr);
                        ElementPlus.ElMessage.success(`已选择: ${storeAbbr}`);
                    }
                };

                // 判断店铺是否已选择
                const isStoreSelected = (store, abbrType) => {
                    const storeAbbr = store[abbrType] || '';
                    return selectedStores.value.includes(storeAbbr);
                };

                // 确认店铺选择（点击确定按钮时触发）
                const confirmStoreSelection = () => {
                    // 如果是批量修改排班模式
                    if (isQuickBatchStoreSelection.value) {
                        // 对于批量修改排班，我们需要将选中的缩写转换为店铺对象
                        // 但这里我们直接将选中的缩写作为店铺名称存储（保持原有逻辑）
                        const storeString = selectedStores.value.join(', ');

                        // 创建一个临时的店铺对象数组用于显示
                        const selectedStoreObjects = selectedStores.value.map(abbr => ({ name: abbr }));
                        quickBatchForm.selectedStores = selectedStoreObjects;

                        // 关闭对话框并重置标记
                        storeSelectionDialogVisible.value = false;
                        isQuickBatchStoreSelection.value = false;

                        ElementPlus.ElMessage.success(`已选择 ${selectedStores.value.length} 个店铺`);
                        return;
                    }

                    if (!currentEditingRule.value) {
                        ElementPlus.ElMessage.error('未找到当前编辑的规则');
                        return;
                    }
                    
                    // 将选中的店铺用逗号连接成字符串
                    const storeString = selectedStores.value.join(', ');
                    
                    // 设置正在加载
                    const loading = ElementPlus.ElLoading.service({
                        lock: true,
                        text: '正在保存...',
                        background: 'rgba(0, 0, 0, 0.7)'
                    });
                    
                    try {
                        // 准备要提交的数据
                        const ruleData = {
                            id: currentEditingRule.value.id,
                            channel_type: currentEditingRule.value.channel_type,
                            group_name: currentEditingRule.value.group_name,
                            leader: currentEditingRule.value.leader || '',
                            member: currentEditingRule.value.member || '',
                            shift: currentEditingRule.value.shift || '',
                            store: storeString, // 允许空字符串，表示没有选择店铺
                            expected_reception: currentEditingRule.value.expected_total || 0,
                            expected_free_reception: currentEditingRule.value.expected_free || 0,
                            expected_paid_reception: currentEditingRule.value.expected_paid || 0,
                            time_range_start: currentEditingRule.value.time_range_start || '00:00:00',
                            time_range_end: currentEditingRule.value.time_range_end || '00:00:00',
                            remarks: currentEditingRule.value.remarks || ''
                        };
                        
                        // 打印要提交的数据，用于调试
                        console.log('准备更新店铺:', ruleData);
                        
                        // 更新规则数据中的店铺字段
                        axios.put(`/api/distribution/rules/${currentEditingRule.value.id}`, ruleData)
                            .then(response => {
                                // 更新成功
                                ElementPlus.ElMessage.success(selectedStores.value.length > 0 ? '更新店铺成功' : '已清空所有店铺');
                                
                                // 更新当前规则的店铺字段
                                currentEditingRule.value.store = storeString;
                                
                                // 隐藏对话框
                                storeSelectionDialogVisible.value = false;
                                
                                // 重新获取规则列表
                                fetchRules();
                            })
                            .catch(error => {
                                // 更新失败
                                console.error('更新店铺失败:', error);
                                ElementPlus.ElMessage.error('更新店铺失败: ' + (error.response?.data?.detail || error.message));
                            })
                            .finally(() => {
                                // 结束加载状态
                                loading.close();
                            });
                    } catch (error) {
                        // 处理异常
                        console.error('更新店铺时发生异常:', error);
                        ElementPlus.ElMessage.error('更新店铺时发生异常: ' + error.message);
                        loading.close();
                    }
                };

                // 监控stores数组，确保数据完整
                watch(stores, async (newStores) => {
                    // 检查第一个店铺是否有完整的字段
                    if (newStores.length > 0 && (!newStores[0].manager || !newStores[0].douyin_abbr)) {
                        console.log('店铺数据不完整，正在重新获取完整数据...');
                        try {
                            const response = await axios.get('/api/presets/shops/detail');
                            stores.value = response.data.map(shop => ({
                                id: shop.id,
                                name: shop.name,
                                manager: shop.manager || '',
                                douyin_abbr: shop.douyin_abbr || '',
                                video_abbr: shop.video_abbr || '',
                                xiaohongshu_abbr: shop.xiaohongshu_abbr || '',
                                kuaishou_abbr: shop.kuaishou_abbr || ''
                            })) || [];
                        } catch (error) {
                            console.error('补充获取店铺数据失败:', error);
                        }
                    }
                }, { immediate: true });

                // 取消店铺选择对话框
                const cancelStoreSelection = () => {
                    storeSelectionDialogVisible.value = false;
                    // 不清空selectedStores和currentEditingRule，保持状态
                };

                // 清空所有店铺
                const clearAllStores = () => {
                    if (selectedStores.value.length === 0) {
                        ElementPlus.ElMessage.info('当前未选择任何店铺');
                        return;
                    }
                    
                    ElementPlus.ElMessageBox.confirm(
                        '确定要清空所有已选择的店铺吗？',
                        '清空确认',
                        {
                            confirmButtonText: '确定',
                            cancelButtonText: '取消',
                            type: 'warning'
                        }
                    ).then(() => {
                        selectedStores.value = [];
                        ElementPlus.ElMessage.success('已清空所有选择');
                    }).catch(() => {
                        ElementPlus.ElMessage.info('已取消操作');
                    });
                };

                // 从已选列表中移除店铺
                const removeSelectedStore = (store) => {
                    const storeIndex = selectedStores.value.findIndex(item => item === store);
                    if (storeIndex !== -1) {
                        selectedStores.value.splice(storeIndex, 1);
                        ElementPlus.ElMessage.info(`已移除: ${store}`);
                    }
                };

                // 批量删除选中的店铺标签（清空操作）
                const clearSelectedStores = () => {
                    if (selectedStores.value.length === 0) {
                        ElementPlus.ElMessage.info('当前未选择任何店铺');
                        return;
                    }
                    
                    ElementPlus.ElMessageBox.confirm(
                        '确定要清空所有已选择的店铺标签吗？',
                        '清空确认',
                        {
                            confirmButtonText: '确定',
                            cancelButtonText: '取消',
                            type: 'warning'
                        }
                    ).then(() => {
                        selectedStores.value = [];
                        ElementPlus.ElMessage.success('已清空所有标签');
                    }).catch(() => {
                        ElementPlus.ElMessage.info('已取消操作');
                    });
                };

                // 获取分组内预计接待数总和
                const getGroupExpectedTotalReception = (groupName, channelType) => {
                    const tableData = channelType === 'ecommerce' ? ecommerceRules.value :
                                    (channelType === 'gonghai' ? gonghaiRules.value : newMediaRules.value);
                    const groupRules = tableData.filter(item => item.group_name === groupName);

                    if (groupRules.length > 0) {
                        // 计算分组内的总预计接待数
                        const totalExpected = groupRules.reduce((sum, rule) => {
                            let expectedTotal = 0;

                            // 对于新媒体渠道，使用预计免费接待数和预计付费接待数的总和
                            if (channelType === 'newmedia') {
                                expectedTotal = (rule.expected_free_reception || rule.expected_free || 0) +
                                               (rule.expected_paid_reception || rule.expected_paid || 0);
                            } else {
                                // 对于电商渠道和公海渠道，直接使用expected_total或expected_reception字段
                                expectedTotal = rule.expected_total || rule.expected_reception || 0;
                            }
                            
                            return sum + expectedTotal;
                        }, 0);
                        return totalExpected;
                    }
                    
                    return 0;
                };
                
                // 获取分组内实际接待数总和
                const getGroupActualTotalReception = (groupName, channelType) => {
                    const tableData = channelType === 'ecommerce' ? ecommerceRules.value :
                                    (channelType === 'gonghai' ? gonghaiRules.value : newMediaRules.value);
                    const groupRules = tableData.filter(item => item.group_name === groupName);
                    
                    if (groupRules.length > 0) {
                        // 计算分组内的总实际接待数
                        const totalActual = groupRules.reduce((sum, rule) => {
                            // 直接使用actual_reception字段
                            const actualTotal = rule.actual_reception || 
                                               ((rule.actual_free_reception || rule.actual_free || 0) + 
                                                (rule.actual_paid_reception || rule.actual_paid || 0));
                            return sum + actualTotal;
                        }, 0);
                        return totalActual;
                    }
                    
                    return 0;
                };

                return {
                    currentDate,
                    loading,
                    hasRules,
                    createDialogVisible,
                    copyDialogVisible,
                    editDialogVisible,
                    addRowDialogVisible,
                    addRowPosition,
                    currentRow,
                    activeTab,
                    ecommerceRules,
                    newMediaRules,
                    gonghaiRules,
                    ecommerceReception,
                    newMediaReception,
                    gonghaiReception,
                    hasEcommerceRules,
                    hasNewMediaRules,
                    hasGonghaiRules,
                    selectedChannels,
                    ecommerceGroups,
                    newMediaGroups,
                    gonghaiGroups,
                    selectedEcommerceGroups,
                    selectedNewMediaGroups,
                    selectedGonghaiGroups,
                    ecommerceReceptionForm,
                    newMediaReceptionForm,
                    gonghaiReceptionForm,
                    ecommerceRulesForms,
                    newMediaRulesForms,
                    gonghaiRulesForms,
                    ecommerceDistributionTotal,
                    newMediaDistributionTotal,
                    gonghaiDistributionTotal,
                    ecommerceDistributionStatus,
                    newMediaDistributionStatus,
                    gonghaiDistributionStatus,
                    stores,
                    copyForm,
                    editForm,
                    canProceed,
                    canCopy,
                    canEdit,
                    apiError,
                    groupDistributionRatios,
                    handleDateChange,
                    setToday,
                    showCreateDialog,
                    showCopyDialog,
                    handleCloseDialog,
                    formatTime,
                    tableRowClassName,
                    fetchRules,
                    editRule,
                    deleteRule,
                    deleteAllRules,
                    handleEcommerceGroupChange,
                    handleNewMediaGroupChange,
                    handleGonghaiGroupChange,
                    updateEcommerceReceptionForm,
                    updateNewMediaReceptionForm,
                    getGroupName,
                    getGroupLeaders,
                    getGroupMembers,
                    getExistingGroups,
                    addRuleForm,
                    removeRuleForm,
                    updateRuleReception,
                    submitCopyForm,
                    submitEditForm,
                    submitCreateForm,
                    nextStep,
                    prevStep,
                    selectAllEcommerceGroups,
                    clearEcommerceGroups,
                    selectAllNewMediaGroups,
                    clearNewMediaGroups,
                    selectAllGonghaiGroups,
                    clearGonghaiGroups,
                    handleChannelChange,
                    updateRuleFormValue,
                    scrollTableDown,
                    leaderSpanMethod,
                    isFirstInGroup,
                    getGroupMemberCount,
                    getGroupDistributionRatio,
                    showAddRowDialog,
                    handleCloseAddRowDialog,
                    addNewRow,
                    updateExpectedFree,
                    updateExpectedPaid,
                    getGroupSettingDistributionRatio,
                    getGroupActualDistributionRatio,
                    updateGroupSettingDistributionRatio,
                    updateRuleMember,
                    updateRuleShift,
                    fetchAllSalesGroups,
                    updateRuleExpectedReception,
                    updateRuleFreeReception,
                    updateRulePaidReception,
                    updateChannelExpectedReception,
                    updateChannelExpectedFreeReception,
                    updateChannelExpectedPaidReception,
                    
                    // 添加编辑相关的变量
                    showEcommerceExpectedReceptionEditIcon,
                    isEditingEcommerceExpectedReception,
                    tempEcommerceExpectedReception,
                    showEcommerceExpectedFreeReceptionEditIcon,
                    isEditingEcommerceExpectedFreeReception,
                    tempEcommerceExpectedFreeReception,
                    showEcommerceExpectedPaidReceptionEditIcon,
                    isEditingEcommerceExpectedPaidReception,
                    tempEcommerceExpectedPaidReception,
                    showNewMediaExpectedReceptionEditIcon,
                    isEditingNewMediaExpectedReception,
                    tempNewMediaExpectedReception,
                    showNewMediaExpectedFreeReceptionEditIcon,
                    isEditingNewMediaExpectedFreeReception,
                    tempNewMediaExpectedFreeReception,
                    showNewMediaExpectedPaidReceptionEditIcon,
                    isEditingNewMediaExpectedPaidReception,
                    tempNewMediaExpectedPaidReception,
                    showGonghaiExpectedReceptionEditIcon,
                    isEditingGonghaiExpectedReception,
                    tempGonghaiExpectedReception,

                    // 添加计算总和的函数
                    calculateCurrentTotalReception,
                    calculateCurrentFreeReception,
                    calculateCurrentPaidReception,
                    calculateActualTotalReception,
                    calculateActualFreeReception,
                    calculateActualPaidReception,
                    getCurrentReceptionStyle,
                    getCurrentFreeReceptionStyle,
                    getCurrentPaidReceptionStyle,
                    storeSelectionDialogVisible,
                    storeSearchKeyword,
                    currentEditingRule,
                    selectedStores,
                    filteredStores,
                    showStoreSelection,
                    selectStore,
                    confirmStoreSelection,
                    cancelStoreSelection,
                    removeSelectedStore,
                    isStoreSelected,
                    clearSelectedStores,
                    getGroupExpectedTotalReception,
                    getGroupActualTotalReception,

                    // 批量修改排班相关变量和方法
                    selectedEcommerceRows,
                    selectedNewMediaRows,
                    selectedGonghaiRows,
                    showQuickBatchDialog,
                    quickBatchChannelType,
                    quickBatchSelectedRows,
                    quickBatchForm,
                    handleEcommerceSelectionChange,
                    handleNewMediaSelectionChange,
                    handleGonghaiSelectionChange,
                    selectAllEcommerceRows,
                    clearEcommerceSelection,
                    selectAllNewMediaRows,
                    clearNewMediaSelection,
                    selectAllGonghaiRows,
                    clearGonghaiSelection,
                    showQuickBatchEdit,
                    getQuickBatchAvailableMembers,
                    applyQuickBatchEdit,
                    showBatchDeleteConfirm,
                    batchDeleteRules,
                    showBatchAppendEdit,
                    showBatchAppendDialog,
                    batchAppendChannelType,
                    batchAppendSelectedRows,
                    batchAppendForm,
                    applyBatchAppendEdit,
                    updateRuleStore,
                    openQuickBatchStoreSelection,
                    removeQuickBatchStoreSelection,
                    isQuickBatchStoreSelection,

                    // 表格引用
                    ecommerceTable,
                    newMediaTable,
                    gonghaiTable
                };
            }
        });
        
        app.use(ElementPlus);
        app.mount('#app');
    </script>


</body>
</html>
{% endraw %}