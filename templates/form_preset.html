<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>表单预设</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css">
    <style>
        :root {
            --primary-color: #1a73e8;
            --secondary-color: #f8f9fa;
            --border-radius: 8px;
            --box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
        }

        body {
            padding: 0;
            margin: 0;
            background-color: #f0f2f5;
            min-height: 100vh;
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", <PERSON><PERSON>, "Helvetica Neue", Arial, sans-serif;
        }

        .page-container {
            padding: 24px;
            max-width: 1800px;
            margin: 0 auto;
        }

        .navbar {
            background-color: white !important;
            box-shadow: var(--box-shadow);
            margin-bottom: 24px;
            border-radius: var(--border-radius);
        }

        .navbar-brand {
            font-weight: 600;
            color: var(--primary-color) !important;
        }

        .card {
            border: none;
            border-radius: var(--border-radius);
            box-shadow: var(--box-shadow);
            margin-bottom: 24px;
            background: white;
        }

        .card-header {
            background-color: white;
            border-bottom: 1px solid rgba(0, 0, 0, 0.05);
            padding: 20px;
            font-size: 18px;
            font-weight: 600;
            color: #2c3e50;
        }

        .card-body {
            padding: 24px;
        }

        .form-control, .btn {
            border-radius: 6px;
        }

        .form-control:focus {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 0.2rem rgba(26, 115, 232, 0.25);
        }

        .btn-primary {
            background-color: var(--primary-color);
            border-color: var(--primary-color);
        }

        .btn-primary:hover {
            background-color: #1557b0;
            border-color: #1557b0;
        }

        .form-row {
            display: grid;
            grid-template-columns: repeat(6, 1fr);
            gap: 16px;
            margin-bottom: 20px;
        }

        /* 店铺表单输入框宽度控制 */
        #addShopForm .form-group:nth-child(1) {
            width: 240px;  /* 店铺名称输入框 */
        }

        #addShopForm .form-group:nth-child(2) {
            width: 120px;  /* 负责人输入框 */
        }

        #addShopForm .form-group:nth-child(3),
        #addShopForm .form-group:nth-child(4),
        #addShopForm .form-group:nth-child(5),
        #addShopForm .form-group:nth-child(6) {
            width: 140px;  /* 各平台缩写输入框 */
        }

        #addShopForm .form-row {
            display: flex;
            flex-wrap: nowrap;
            gap: 16px;
            margin-bottom: 20px;
            overflow-x: auto;
        }

        #addShopForm .form-group {
            flex-shrink: 0;
        }
        
        /* 电商店铺表单样式 */
        #addEcommerceForm .form-row {
            display: flex;
            flex-wrap: wrap;
            gap: 16px;
            margin-bottom: 20px;
        }
        
        #addEcommerceForm .form-group:nth-child(1) {
            width: 200px;  /* 平台输入框 */
            flex-shrink: 0;
        }
        
        #addEcommerceForm .form-group:nth-child(2) {
            width: calc(100% - 216px);  /* 店铺输入框，减去平台输入框宽度和间距 */
            min-width: 600px;  /* 最小宽度确保足够宽 */
            flex-grow: 1;
        }
        
        .form-actions-space-between {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        /* 编辑电商店铺模态框中的店铺输入框 */
        #editEcommerceStores {
            width: 100%;
            min-width: 500px;
        }

        .form-label {
            font-weight: 500;
            color: #4a5568;
            margin-bottom: 8px;
        }

        .table {
            margin-top: 20px;
            background: white;
            border-radius: var(--border-radius);
            overflow: hidden;
        }

        .table thead th {
            background-color: #f8fafc;
            font-weight: 600;
            color: #4a5568;
            border-top: none;
            padding: 16px;
            text-align: center;
        }

        .table tbody td {
            padding: 16px;
            vertical-align: middle;
            border-color: #f0f2f5;
            text-align: center;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            max-width: 200px;
        }
        
        /* 电商店铺表格中的店铺列可以显示更多内容 */
        .ecommerce-table-responsive .table tbody td:nth-child(4) {
            white-space: normal;
            max-width: 500px;
        }

        .preset-actions {
            display: flex;
            justify-content: center;
            gap: 8px;
        }

        .btn-sm {
            padding: 0.4rem 0.8rem;
            font-size: 0.875rem;
        }

        .alert {
            border-radius: var(--border-radius);
            margin-bottom: 20px;
        }

        .preset-item {
            background: white;
            border-radius: var(--border-radius);
            padding: 16px;
            margin-bottom: 12px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            transition: all 0.3s ease;
            border: 1px solid #e2e8f0;
        }

        .preset-item:hover {
            transform: translateY(-2px);
            box-shadow: var(--box-shadow);
        }

        .modal-content {
            border-radius: var(--border-radius);
            border: none;
        }

        .modal-header {
            border-bottom: 1px solid rgba(0, 0, 0, 0.05);
            padding: 20px;
        }

        .modal-body {
            padding: 24px;
        }

        .modal-footer {
            border-top: 1px solid rgba(0, 0, 0, 0.05);
            padding: 20px;
        }

        .section-title {
            font-size: 16px;
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 16px;
            display: flex;
            align-items: center;
        }

        .section-title i {
            margin-right: 8px;
            color: var(--primary-color);
        }

        .grid-layout {
            display: grid;
            grid-template-columns: 1fr;
            gap: 24px;
        }

        @media (max-width: 1200px) {
            .grid-layout {
                grid-template-columns: 1fr 1fr;
            }
        }

        @media (max-width: 768px) {
            .grid-layout {
                grid-template-columns: 1fr;
            }
        }

        .table-responsive {
            overflow-x: auto;
        }

        /* 店铺预设表格 */
        .shops-table-responsive {
            min-width: 700px;
        }
        
        /* 电商店铺预设表格 */
        .ecommerce-table-responsive {
            min-width: 700px;
        }

        /* 主播预设表格 */
        .hosts-table-responsive, .rooms-table-responsive {
            min-width: 520px;
        }

        .checkbox-column {
            width: 40px;
            text-align: center;
        }

        .narrow-column {
            width: 80px;
            text-align: center;
        }

        .abbr-column {
            width: 120px;
            text-align: center;
        }

        .host-name-column {
            min-width: 200px;
        }

        .actions-column {
            width: 150px;
            text-align: center;
        }

        /* Excel导入模态框样式 */
        .step {
            text-align: center;
            cursor: pointer;
            opacity: 0.5;
            transition: all 0.3s;
        }
        
        .step.active {
            opacity: 1;
            font-weight: 500;
        }
        
        .step-number {
            display: inline-block;
            width: 24px;
            height: 24px;
            line-height: 24px;
            text-align: center;
            border-radius: 50%;
            background-color: #e9ecef;
            margin-right: 8px;
        }
        
        .step.active .step-number {
            background-color: var(--primary-color);
            color: white;
        }
        
        /* Excel预览表格样式 */
        .preview-table-container {
            max-height: 300px;
            overflow-y: auto;
            overflow-x: auto;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            position: relative;
        }
        
        .preview-table-container table {
            margin-bottom: 0;
        }
        
        .preview-table-container thead {
            position: sticky;
            top: 0;
            background-color: #f8f9fa;
            z-index: 1;
        }
        
        .preview-table-container th {
            white-space: nowrap;
            min-width: 120px;
            background-color: #f8f9fa;
        }
        
        .preview-table-container td {
            white-space: nowrap;
            max-width: 200px;
            overflow: hidden;
            text-overflow: ellipsis;
        }
        
        /* 滚动条美化 */
        .preview-table-container::-webkit-scrollbar {
            width: 8px;
            height: 8px;
        }
        
        .preview-table-container::-webkit-scrollbar-track {
            background: #f1f1f1;
            border-radius: 4px;
        }
        
        .preview-table-container::-webkit-scrollbar-thumb {
            background: #c1c1c1;
            border-radius: 4px;
        }
        
        .preview-table-container::-webkit-scrollbar-thumb:hover {
            background: #a8a8a8;
        }

        .nav-tabs {
            border-bottom: 1px solid #dee2e6;
            margin-bottom: 20px;
        }

        .nav-tabs .nav-item {
            margin-bottom: -1px;
        }

        .nav-tabs .nav-link {
            border: 1px solid transparent;
            border-top-left-radius: 0.25rem;
            border-top-right-radius: 0.25rem;
            padding: 0.75rem 1.5rem;
            color: #495057;
            cursor: pointer;
            font-size: 16px;
            transition: all 0.2s ease;
        }

        .nav-tabs .nav-link:hover {
            border-color: #e9ecef #e9ecef #dee2e6;
            color: #1a73e8;
        }

        .nav-tabs .nav-link.active {
            color: #1a73e8;
            background-color: #fff;
            border-color: #dee2e6 #dee2e6 #fff;
            font-weight: 600;
        }

        .tab-content > .tab-pane {
            display: none;
        }

        .tab-content > .active {
            display: block;
        }

        .tab-content .card {
            border-top-left-radius: 0;
        }

        /* Loading 动画样式 */
        #loading-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: #f5f7fa;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            z-index: 9999;
            transition: opacity 0.5s ease-out;
        }

        #loading-overlay.fade-out {
            opacity: 0;
            pointer-events: none;
        }

        .loading-spinner {
            width: 60px;
            height: 60px;
            border: 4px solid rgba(255, 255, 255, 0.3);
            border-top: 4px solid #1a73e8;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-bottom: 20px;
        }

        .loading-text {
            color: #606266;
            font-size: 18px;
            font-weight: 500;
            margin-bottom: 10px;
            text-align: center;
        }

        .loading-progress {
            color: #909399;
            font-size: 14px;
            text-align: center;
        }

        .loading-dots {
            display: inline-block;
            width: 20px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        @keyframes dots {
            0%, 20% { content: ''; }
            40% { content: '.'; }
            60% { content: '..'; }
            80%, 100% { content: '...'; }
        }

        .loading-dots::after {
            content: '';
            animation: dots 1.5s infinite;
        }
    </style>
    <script src="/static/js/fetch.js"></script>
    <!-- 在这里添加jQuery和Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/jquery@3.6.0/dist/jquery.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- XLSX库，用于Excel导入 -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.18.5/xlsx.full.min.js"></script>
</head>
<body>
    <!-- Loading 组件 -->
    <div id="loading-overlay">
        <div class="loading-spinner"></div>
        <div class="loading-text">正在加载表单预设管理</div>
        <div class="loading-progress">
            <span id="loading-status">正在加载静态资源</span>
            <span class="loading-dots"></span>
        </div>
    </div>

    <!-- 页面容器 -->
    <div class="page-container">
        <!-- 导航栏 -->
        <nav class="navbar navbar-expand-lg navbar-light">
            <div class="container-fluid">
                <div class="d-flex align-items-center">
                    <a class="navbar-brand" href="/">
                        <i class="bi bi-box me-2"></i>
                        表单预设管理
                    </a>
                </div>
                <div>
                    <a href="/home" class="btn btn-sm btn-outline-primary d-flex align-items-center">
                        <i class="bi bi-arrow-left me-1"></i>
                        返回首页
                    </a>
                </div>
            </div>
        </nav>

        <!-- 主体内容 -->
        <div class="grid-layout">
            <!-- 标签页导航 -->
            <ul class="nav nav-tabs" id="presetTabs" role="tablist">
                <li class="nav-item" role="presentation">
                    <button class="nav-link active" id="shops-tab" data-bs-toggle="tab" data-bs-target="#shops-tab-pane" type="button" role="tab" aria-controls="shops-tab-pane" aria-selected="true">
                        <i class="bi bi-shop me-2"></i>新媒体店铺预设管理
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="ecommerce-tab" data-bs-toggle="tab" data-bs-target="#ecommerce-tab-pane" type="button" role="tab" aria-controls="ecommerce-tab-pane" aria-selected="false">
                        <i class="bi bi-cart3 me-2"></i>电商店铺预设管理
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="hosts-tab" data-bs-toggle="tab" data-bs-target="#hosts-tab-pane" type="button" role="tab" aria-controls="hosts-tab-pane" aria-selected="false">
                        <i class="bi bi-person-video3 me-2"></i>主播预设管理
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="rooms-tab" data-bs-toggle="tab" data-bs-target="#rooms-tab-pane" type="button" role="tab" aria-controls="rooms-tab-pane" aria-selected="false">
                        <i class="bi bi-camera-video me-2"></i>直播间预设管理
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="sales-tab" data-bs-toggle="tab" data-bs-target="#sales-tab-pane" type="button" role="tab" aria-controls="sales-tab-pane" aria-selected="false">
                        <i class="bi bi-people-fill me-2"></i>销售预设管理
                    </button>
                </li>
            </ul>
            
            <!-- 标签页内容 -->
            <div class="tab-content" id="presetTabsContent">
                <!-- 新媒体店铺预设管理 -->
                <div class="tab-pane fade show active" id="shops-tab-pane" role="tabpanel" aria-labelledby="shops-tab" tabindex="0">
                    <div class="card">
                        <div class="card-body">
                            <div id="shopAlert" class="alert d-none" role="alert"></div>
                            
                            <!-- 添加店铺表单 -->
                            <form id="addShopForm">
                                <div class="form-row">
                                    <div class="form-group">
                                        <label class="form-label" for="shopName">店铺名称</label>
                                        <input type="text" class="form-control" id="shopName" required>
                                    </div>
                                    <div class="form-group">
                                        <label class="form-label" for="shopManager">负责人</label>
                                        <input type="text" class="form-control" id="shopManager">
                                    </div>
                                    <div class="form-group">
                                        <label class="form-label" for="douyinAbbr">抖音缩写</label>
                                        <input type="text" class="form-control" id="douyinAbbr">
                                    </div>
                                    <div class="form-group">
                                        <label class="form-label" for="videoAbbr">视频号缩写</label>
                                        <input type="text" class="form-control" id="videoAbbr">
                                    </div>
                                    <div class="form-group">
                                        <label class="form-label" for="xiaohongshuAbbr">小红书缩写</label>
                                        <input type="text" class="form-control" id="xiaohongshuAbbr">
                                    </div>
                                    <div class="form-group">
                                        <label class="form-label" for="kuaishouAbbr">快手缩写</label>
                                        <input type="text" class="form-control" id="kuaishouAbbr">
                                    </div>
                                </div>
                                <div class="d-flex justify-content-between align-items-center">
                                    <div class="d-flex gap-2">
                                        <button type="submit" class="btn btn-primary">
                                            <i class="bi bi-plus-circle me-1"></i>添加店铺
                                        </button>
                                        <button type="button" class="btn btn-success" data-bs-toggle="modal" data-bs-target="#excelImportModal">
                                            <i class="bi bi-file-earmark-excel me-1"></i>Excel导入
                                        </button>
                                    </div>
                                    <button id="batchDeleteShopsBtn" class="btn btn-danger" disabled>
                                        <i class="bi bi-trash me-1"></i>批量删除
                                    </button>
                                </div>
                            </form>
                            
                            <!-- 店铺列表 -->
                            <div class="mt-4">
                                <div class="table-responsive shops-table-responsive">
                                    <table class="table table-hover">
                                        <thead>
                                            <tr>
                                                <th class="checkbox-column">
                                                    <input type="checkbox" id="selectAllShops" class="form-check-input">
                                                </th>
                                                <th class="narrow-column">序号</th>
                                                <th class="narrow-column">负责人</th>
                                                <th>店铺名称</th>
                                                <th class="abbr-column">抖音缩写</th>
                                                <th class="abbr-column">视频号缩写</th>
                                                <th class="abbr-column">小红书缩写</th>
                                                <th class="abbr-column">快手缩写</th>
                                                <th class="actions-column">操作</th>
                                            </tr>
                                        </thead>
                                        <tbody id="shopsList">
                                            <!-- 店铺项将通过JavaScript动态添加 -->
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- 电商店铺预设管理 -->
                <div class="tab-pane fade" id="ecommerce-tab-pane" role="tabpanel" aria-labelledby="ecommerce-tab" tabindex="0">
                    <div class="card">
                        <div class="card-body">
                            <div id="ecommerceAlert" class="alert d-none" role="alert"></div>
                            
                            <!-- 添加电商店铺表单 -->
                            <form id="addEcommerceForm">
                                <div class="form-row">
                                    <div class="form-group">
                                        <label class="form-label" for="ecommercePlatform">平台</label>
                                        <input type="text" class="form-control" id="ecommercePlatform" required>
                                    </div>
                                    <div class="form-group">
                                        <label class="form-label" for="ecommerceStores">店铺</label>
                                        <input type="text" class="form-control" id="ecommerceStores" placeholder="多个店铺用逗号分隔" required>
                                        <div class="form-text text-muted">多个店铺名称使用逗号分隔</div>
                                    </div>
                                </div>
                                <div class="form-actions form-actions-space-between">
                                    <div>
                                        <button type="submit" class="btn btn-primary">
                                            <i class="bi bi-plus-circle me-1"></i>添加电商店铺
                                        </button>
                                        <button type="button" id="excelImportEcommerceBtn" class="btn btn-success ms-2">
                                            <i class="bi bi-file-earmark-excel me-1"></i>Excel导入
                                        </button>
                                    </div>
                                    <div>
                                        <button type="button" id="batchDeleteEcommerceBtn" class="btn btn-danger" disabled>
                                            <i class="bi bi-trash me-1"></i>批量删除
                                        </button>
                                    </div>
                                </div>
                            </form>
                            
                            <div class="mt-4">
                                <div class="table-responsive ecommerce-table-responsive">
                                    <table class="table table-hover">
                                        <thead>
                                            <tr>
                                                <th class="checkbox-column">
                                                    <input type="checkbox" id="selectAllEcommerce" class="form-check-input">
                                                </th>
                                                <th class="narrow-column">序号</th>
                                                <th class="narrow-column">平台</th>
                                                <th>店铺</th>
                                                <th class="narrow-column">数量</th>
                                                <th class="actions-column">操作</th>
                                            </tr>
                                        </thead>
                                        <tbody id="ecommerceList">
                                            <!-- 电商店铺项将通过JavaScript动态添加 -->
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- 主播预设管理 -->
                <div class="tab-pane fade" id="hosts-tab-pane" role="tabpanel" aria-labelledby="hosts-tab" tabindex="0">
                    <div class="card">
                        <div class="card-body">
                            <div id="hostAlert" class="alert d-none" role="alert"></div>
                            
                            <!-- 添加主播表单 -->
                            <form id="addHostForm" class="mb-4">
                                <div class="row mb-3">
                                    <div class="col-md-3">
                                        <input type="text" class="form-control" id="hostLeader" placeholder="负责人" required>
                                    </div>
                                    <div class="col-md-3">
                                        <input type="text" class="form-control" id="hostGroup" placeholder="分组" required>
                                    </div>
                                    <div class="col-md-6">
                                        <input type="text" class="form-control" id="hostNames" placeholder="主播名称（用英文逗号分隔）" required>
                                    </div>
                                </div>
                                <div class="d-flex justify-content-between align-items-center">
                                    <div class="d-flex gap-2">
                                        <button type="submit" class="btn btn-primary">
                                            <i class="bi bi-plus-circle me-1"></i>添加主播组
                                        </button>
                                        <button type="button" class="btn btn-success" data-bs-toggle="modal" data-bs-target="#hostExcelImportModal">
                                            <i class="bi bi-file-earmark-excel me-1"></i>Excel导入
                                        </button>
                                    </div>
                                    <button id="batchDeleteHostsBtn" type="button" class="btn btn-danger" disabled onclick="batchDeleteHosts()">
                                        <i class="bi bi-trash me-1"></i>批量删除
                                    </button>
                                </div>
                            </form>
                            
                            <!-- 主播列表 -->
                            <div class="mt-4">
                                <div class="table-responsive hosts-table-responsive">
                                    <table class="table table-hover">
                                        <thead>
                                            <tr>
                                                <th class="checkbox-column">
                                                    <input type="checkbox" id="selectAllHosts" class="form-check-input">
                                                </th>
                                                <th class="narrow-column">序号</th>
                                                <th class="narrow-column">负责人</th>
                                                <th class="narrow-column">分组</th>
                                                <th>主播名称</th>
                                                <th class="narrow-column">人数</th>
                                                <th class="actions-column">操作</th>
                                            </tr>
                                        </thead>
                                        <tbody id="hostsList">
                                            <!-- 主播项将通过JavaScript动态添加 -->
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- 直播间预设管理 -->
                <div class="tab-pane fade" id="rooms-tab-pane" role="tabpanel" aria-labelledby="rooms-tab" tabindex="0">
                    <div class="card">
                        <div class="card-body">
                            <div id="roomAlert" class="alert d-none" role="alert"></div>
                            
                            <!-- 添加直播间表单 -->
                            <form id="addRoomForm">
                                <div class="form-group mb-3">
                                    <label class="form-label" for="roomName">直播间名称</label>
                                    <input type="text" class="form-control" id="roomName" required>
                                </div>
                                <div class="d-flex justify-content-between align-items-center">
                                    <div class="d-flex gap-2">
                                        <button type="submit" class="btn btn-primary">
                                            <i class="bi bi-plus-circle me-1"></i>添加直播间
                                        </button>
                                        <button type="button" class="btn btn-success" data-bs-toggle="modal" data-bs-target="#roomExcelImportModal">
                                            <i class="bi bi-file-earmark-excel me-1"></i>Excel导入
                                        </button>
                                    </div>
                                    <button id="batchDeleteRoomsBtn" class="btn btn-danger" disabled>
                                        <i class="bi bi-trash me-1"></i>批量删除
                                    </button>
                                </div>
                            </form>

                            <!-- 直播间列表 -->
                            <div class="mt-4">
                                <div class="table-responsive rooms-table-responsive">
                                    <table class="table table-hover">
                                        <thead>
                                            <tr>
                                                <th class="checkbox-column">
                                                    <input type="checkbox" id="selectAllRooms" class="form-check-input">
                                                </th>
                                                <th class="narrow-column">序号</th>
                                                <th>直播间名称</th>
                                                <th class="actions-column">操作</th>
                                            </tr>
                                        </thead>
                                        <tbody id="roomsList">
                                            <!-- 直播间项将通过JavaScript动态添加 -->
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 销售预设管理 -->
                <div class="tab-pane fade" id="sales-tab-pane" role="tabpanel" aria-labelledby="sales-tab" tabindex="0">
                    <div class="card">
                        <div class="card-body">
                            <div id="salesAlert" class="alert d-none" role="alert"></div>
                            
                            <!-- 添加销售表单 -->
                            <form id="addSalesForm">
                                <div class="row mb-3">
                                    <div class="col-md-4">
                                        <label class="form-label" for="salesGroup">分组</label>
                                        <input type="text" class="form-control" id="salesGroup" required>
                                    </div>
                                    <div class="col-md-4">
                                        <label class="form-label" for="salesLeader">负责人</label>
                                        <input type="text" class="form-control" id="salesLeader" required>
                                    </div>
                                    <div class="col-md-4">
                                        <label class="form-label" for="salesMembers">成员</label>
                                        <input type="text" class="form-control" id="salesMembers" placeholder="多个成员用逗号分隔" required>
                                    </div>
                                </div>
                                <div class="d-flex justify-content-between align-items-center">
                                    <div class="d-flex gap-2">
                                        <button type="submit" class="btn btn-primary">
                                            <i class="bi bi-plus-circle me-1"></i>添加销售组
                                        </button>
                                        <button type="button" class="btn btn-success" data-bs-toggle="modal" data-bs-target="#salesExcelImportModal">
                                            <i class="bi bi-file-earmark-excel me-1"></i>Excel导入
                                        </button>
                                    </div>
                                    <button id="batchDeleteSalesBtn" class="btn btn-danger" disabled>
                                        <i class="bi bi-trash me-1"></i>批量删除
                                    </button>
                                </div>
                            </form>
                            
                            <!-- 销售列表 -->
                            <div class="mt-4">
                                <div class="table-responsive sales-table-responsive">
                                    <table class="table table-hover">
                                        <thead>
                                            <tr>
                                                <th class="checkbox-column">
                                                    <input type="checkbox" id="selectAllSales" class="form-check-input">
                                                </th>
                                                <th class="narrow-column">序号</th>
                                                <th>分组</th>
                                                <th>负责人</th>
                                                <th>成员</th>
                                                <th class="narrow-column">人数</th>
                                                <th class="actions-column">操作</th>
                                            </tr>
                                        </thead>
                                        <tbody id="salesList">
                                            <!-- 销售项将通过JavaScript动态添加 -->
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 编辑模态框 -->
    <div class="modal fade" id="editModal" tabindex="-1" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="editModalTitle">编辑</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <form id="editForm">
                        <input type="hidden" id="editType">
                        <input type="hidden" id="editOldName">
                        
                        <div id="shopEditFields">
                            <div class="form-row">
                                <div class="form-group">
                                    <label class="form-label" for="editNewName">店铺名称</label>
                                    <input type="text" class="form-control" id="editNewName" required>
                                </div>
                                <div class="form-group">
                                    <label class="form-label" for="editManager">负责人</label>
                                    <input type="text" class="form-control" id="editManager">
                                </div>
                            </div>
                            <div class="form-row">
                                <div class="form-group">
                                    <label class="form-label" for="editDouyinAbbr">抖音缩写</label>
                                    <input type="text" class="form-control" id="editDouyinAbbr">
                                </div>
                                <div class="form-group">
                                    <label class="form-label" for="editVideoAbbr">视频号缩写</label>
                                    <input type="text" class="form-control" id="editVideoAbbr">
                                </div>
                                <div class="form-group">
                                    <label class="form-label" for="editXiaohongshuAbbr">小红书缩写</label>
                                    <input type="text" class="form-control" id="editXiaohongshuAbbr">
                                </div>
                                <div class="form-group">
                                    <label class="form-label" for="editKuaishouAbbr">快手缩写</label>
                                    <input type="text" class="form-control" id="editKuaishouAbbr">
                                </div>
                            </div>
                        </div>
                        
                        <div id="hostEditFields" style="display: none;">
                            <div class="form-group">
                                <label class="form-label" for="editHostName">主播名称</label>
                                <input type="text" class="form-control" id="editHostName" required>
                            </div>
                        </div>
                        
                        <div id="roomEditFields" style="display: none;">
                            <div class="form-group">
                                <label class="form-label" for="editRoomName">直播间名称</label>
                                <input type="text" class="form-control" id="editRoomName" required>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                        <i class="bi bi-x-circle me-1"></i>取消
                    </button>
                    <button type="button" class="btn btn-primary" id="saveEdit">
                        <i class="bi bi-check-circle me-1"></i>保存
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Excel导入模态框 -->
    <div class="modal fade" id="excelImportModal" tabindex="-1" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">店铺Excel导入</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <!-- 步骤进度条 -->
                    <div class="mb-4">
                        <div class="progress" style="height: 4px;">
                            <div id="importProgressBar" class="progress-bar" role="progressbar" style="width: 33%;" aria-valuenow="33" aria-valuemin="0" aria-valuemax="100"></div>
                        </div>
                        <div class="d-flex justify-content-between mt-2">
                            <div class="step active" onclick="goToStep(1)">
                                <span class="step-number">1</span>
                                <span class="step-text">上传文件</span>
                            </div>
                            <div class="step" onclick="goToStep(2)">
                                <span class="step-number">2</span>
                                <span class="step-text">数据预览</span>
                            </div>
                            <div class="step" onclick="goToStep(3)">
                                <span class="step-number">3</span>
                                <span class="step-text">导入设置</span>
                            </div>
                        </div>
                    </div>

                    <!-- 步骤1：上传文件 -->
                    <div id="step1" class="import-step">
                        <div class="text-center p-4 border rounded mb-3" style="border-style: dashed !important;">
                            <i class="bi bi-file-earmark-excel" style="font-size: 3rem; color: #28a745;"></i>
                            <h5 class="mt-3">选择Excel文件</h5>
                            <p class="text-muted">支持.xlsx和.xls格式</p>
                            <input type="file" id="excelFile" class="d-none" accept=".xlsx,.xls">
                            <button type="button" class="btn btn-outline-primary mt-2" onclick="document.getElementById('excelFile').click()">
                                <i class="bi bi-upload me-1"></i>选择文件
                            </button>
                            <div id="selectedFileName" class="mt-2 text-muted"></div>
                        </div>
                    </div>

                    <!-- 步骤2：数据预览 -->
                    <div id="step2" class="import-step" style="display: none;">
                        <h6 class="mb-3">数据预览（全部数据）</h6>
                        <div class="d-flex justify-content-between align-items-center mb-2">
                            <div class="text-muted">
                                <small>总行数: <span id="totalRows">0</span></small>
                            </div>
                            <div class="pagination-controls">
                                <button class="btn btn-sm btn-outline-secondary" id="prevPageBtn" disabled>
                                    <i class="bi bi-chevron-left"></i> 上一页
                                </button>
                                <span class="mx-2">
                                    第 <span id="currentPage">1</span> / <span id="totalPages">1</span> 页
                                </span>
                                <button class="btn btn-sm btn-outline-secondary" id="nextPageBtn">
                                    <i class="bi bi-chevron-right"></i> 下一页
                                </button>
                                <select id="pageSizeSelector" class="form-select form-select-sm ms-2" style="width: auto;">
                                    <option value="10">10行/页</option>
                                    <option value="20">20行/页</option>
                                    <option value="50">50行/页</option>
                                    <option value="100">100行/页</option>
                                </select>
                            </div>
                        </div>
                        <div class="table-responsive preview-table-container">
                            <table class="table table-sm table-bordered" id="previewTable">
                                <thead>
                                    <tr id="previewTableHeader"></tr>
                                </thead>
                                <tbody id="previewTableBody"></tbody>
                            </table>
                        </div>
                    </div>

                    <!-- 步骤3：导入设置 -->
                    <div id="step3" class="import-step" style="display: none;">
                        <h6 class="mb-3">字段映射</h6>
                        <p class="text-muted small mb-3">请将Excel表格中的列映射到对应的店铺字段</p>
                        <div id="fieldMappings" class="mb-4"></div>

                        <h6 class="mb-3">导入范围</h6>
                        <div class="row g-3 mb-3">
                            <div class="col-md-6">
                                <label for="startRow" class="form-label">起始行</label>
                                <input type="number" class="form-control" id="startRow" min="1" value="1">
                                <div class="form-text">从第几行开始导入（含表头）</div>
                            </div>
                            <div class="col-md-6">
                                <label for="endRow" class="form-label">结束行</label>
                                <input type="number" class="form-control" id="endRow">
                                <div class="form-text">留空表示导入到最后一行</div>
                            </div>
                        </div>

                        <div class="alert alert-info">
                            <i class="bi bi-info-circle me-2"></i>
                            <span>店铺名称为必填字段，重复的店铺名称将被跳过</span>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" id="prevStepBtn" class="btn btn-outline-primary" style="display: none;" onclick="prevStep()">上一步</button>
                    <button type="button" id="nextStepBtn" class="btn btn-primary" onclick="nextStep()">下一步</button>
                    <button type="button" id="startImportBtn" class="btn btn-success" style="display: none;" onclick="startImport()">开始导入</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 主播Excel导入模态框 -->
    <div class="modal fade" id="hostExcelImportModal" tabindex="-1" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">主播Excel导入</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <!-- 进度条 -->
                    <div class="progress mb-4">
                        <div id="hostImportProgress" class="progress-bar" role="progressbar" style="width: 33%;" aria-valuenow="33" aria-valuemin="0" aria-valuemax="100"></div>
                    </div>
                    
                    <!-- 步骤指示器 -->
                    <div class="d-flex justify-content-between mb-4">
                        <div class="text-center">
                            <div id="hostStep1Indicator" class="btn btn-primary btn-sm rounded-circle mb-2">1</div>
                            <div>上传文件</div>
                        </div>
                        <div class="text-center">
                            <div id="hostStep2Indicator" class="btn btn-outline-secondary btn-sm rounded-circle mb-2">2</div>
                            <div>数据预览</div>
                        </div>
                        <div class="text-center">
                            <div id="hostStep3Indicator" class="btn btn-outline-secondary btn-sm rounded-circle mb-2">3</div>
                            <div>导入设置</div>
                        </div>
                    </div>
                    
                    <!-- 步骤1：上传文件 -->
                    <div id="hostStep1" class="import-step">
                        <div class="mb-3">
                            <label for="hostExcelFile" class="form-label">选择Excel文件</label>
                            <input class="form-control" type="file" id="hostExcelFile" accept=".xlsx,.xls">
                            <div class="form-text">支持.xlsx和.xls格式</div>
                        </div>
                        <div id="hostSelectedFile" class="mb-3">
                            <span class="fw-bold">已选择文件：</span>
                            <span id="hostSelectedFileName">未选择文件</span>
                        </div>
                    </div>
                    
                    <!-- 步骤2：数据预览 -->
                    <div id="hostStep2" class="import-step" style="display: none;">
                        <h6 class="mb-3">数据预览（全部数据）</h6>
                        <div class="mb-3">
                            <div class="d-flex justify-content-between align-items-center mb-2">
                                <div>
                                    <span class="badge bg-info">总行数: <span id="hostTotalRows">0</span></span>
                                    <span class="badge bg-secondary ms-2">当前页: <span id="hostCurrentPage">1</span>/<span id="hostTotalPages">1</span></span>
                                </div>
                                <div class="d-flex align-items-center">
                                    <button id="hostPrevPageBtn" class="btn btn-sm btn-outline-secondary me-2" disabled>
                                        <i class="bi bi-chevron-left"></i> 上一页
                                    </button>
                                    <button id="hostNextPageBtn" class="btn btn-sm btn-outline-secondary me-2">
                                        <i class="bi bi-chevron-right"></i> 下一页
                                    </button>
                                    <select id="hostPageSizeSelector" class="form-select form-select-sm ms-2" style="width: auto;">
                                        <option value="10">10行/页</option>
                                        <option value="20">20行/页</option>
                                        <option value="50">50行/页</option>
                                        <option value="100">100行/页</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div class="table-responsive preview-table-container">
                            <table class="table table-sm table-bordered" id="hostPreviewTable">
                                <thead>
                                    <tr id="hostPreviewTableHeader"></tr>
                                </thead>
                                <tbody id="hostPreviewTableBody"></tbody>
                            </table>
                        </div>
                    </div>
                    
                    <!-- 步骤3：导入设置 -->
                    <div id="hostStep3" class="import-step" style="display: none;">
                        <h6 class="mb-3">字段映射</h6>
                        <p class="text-muted small mb-3">请将Excel表格中的列映射到主播名称字段</p>
                        <div id="hostFieldMappings" class="mb-4"></div>
                        
                        <h6 class="mb-3">导入范围</h6>
                        <div class="row g-3 mb-3">
                            <div class="col-md-6">
                                <label for="hostStartRow" class="form-label">起始行</label>
                                <input type="number" class="form-control" id="hostStartRow" min="1" value="1">
                                <div class="form-text">从第几行开始导入（含表头）</div>
                            </div>
                            <div class="col-md-6">
                                <label for="hostEndRow" class="form-label">结束行</label>
                                <input type="number" class="form-control" id="hostEndRow">
                                <div class="form-text">留空表示导入到最后一行</div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-primary" id="hostUploadExcelBtn" disabled>
                        <i class="bi bi-upload me-1"></i>上传并预览
                    </button>
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                        <i class="bi bi-x-circle me-1"></i>取消
                    </button>
                    <button type="button" class="btn btn-secondary" id="hostPrevStepBtn" style="display: none;">
                        <i class="bi bi-arrow-left me-1"></i>上一步
                    </button>
                    <button type="button" class="btn btn-primary" id="hostNextStepBtn" style="display: none;">
                        <i class="bi bi-arrow-right me-1"></i>下一步
                    </button>
                    <button type="button" class="btn btn-success" id="hostStartImportBtn" style="display: none;">
                        <i class="bi bi-check-circle me-1"></i>开始导入
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- 直播间Excel导入模态框 -->
    <div class="modal fade" id="roomExcelImportModal" tabindex="-1" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">直播间Excel导入</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <!-- 进度条 -->
                    <div class="progress mb-4">
                        <div id="roomImportProgress" class="progress-bar" role="progressbar" style="width: 33%;" aria-valuenow="33" aria-valuemin="0" aria-valuemax="100"></div>
                    </div>
                    
                    <!-- 步骤指示器 -->
                    <div class="d-flex justify-content-between mb-4">
                        <div class="text-center">
                            <div id="roomStep1Indicator" class="btn btn-primary btn-sm rounded-circle mb-2">1</div>
                            <div>上传文件</div>
                        </div>
                        <div class="text-center">
                            <div id="roomStep2Indicator" class="btn btn-outline-secondary btn-sm rounded-circle mb-2">2</div>
                            <div>数据预览</div>
                        </div>
                        <div class="text-center">
                            <div id="roomStep3Indicator" class="btn btn-outline-secondary btn-sm rounded-circle mb-2">3</div>
                            <div>导入设置</div>
                        </div>
                    </div>
                    
                    <!-- 步骤1：上传文件 -->
                    <div id="roomStep1" class="import-step">
                        <div class="mb-3">
                            <label for="roomExcelFile" class="form-label">选择Excel文件</label>
                            <input class="form-control" type="file" id="roomExcelFile" accept=".xlsx,.xls">
                            <div class="form-text">支持.xlsx和.xls格式</div>
                        </div>
                        <div id="roomSelectedFile" class="mb-3">
                            <span class="fw-bold">已选择文件：</span>
                            <span id="roomSelectedFileName">未选择文件</span>
                        </div>
                    </div>
                    
                    <!-- 步骤2：数据预览 -->
                    <div id="roomStep2" class="import-step" style="display: none;">
                        <h6 class="mb-3">数据预览（全部数据）</h6>
                        <div class="mb-3">
                            <div class="d-flex justify-content-between align-items-center mb-2">
                                <div>
                                    <span class="badge bg-info">总行数: <span id="roomTotalRows">0</span></span>
                                    <span class="badge bg-secondary ms-2">当前页: <span id="roomCurrentPage">1</span>/<span id="roomTotalPages">1</span></span>
                                </div>
                                <div class="d-flex align-items-center">
                                    <button id="roomPrevPageBtn" class="btn btn-sm btn-outline-secondary me-2" disabled>
                                        <i class="bi bi-chevron-left"></i> 上一页
                                    </button>
                                    <button id="roomNextPageBtn" class="btn btn-sm btn-outline-secondary me-2">
                                        <i class="bi bi-chevron-right"></i> 下一页
                                    </button>
                                    <select id="roomPageSizeSelector" class="form-select form-select-sm ms-2" style="width: auto;">
                                        <option value="10">10行/页</option>
                                        <option value="20">20行/页</option>
                                        <option value="50">50行/页</option>
                                        <option value="100">100行/页</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div class="table-responsive preview-table-container">
                            <table class="table table-sm table-bordered" id="roomPreviewTable">
                                <thead>
                                    <tr id="roomPreviewTableHeader"></tr>
                                </thead>
                                <tbody id="roomPreviewTableBody"></tbody>
                            </table>
                        </div>
                    </div>
                    
                    <!-- 步骤3：导入设置 -->
                    <div id="roomStep3" class="import-step" style="display: none;">
                        <h6 class="mb-3">字段映射</h6>
                        <p class="text-muted small mb-3">请将Excel表格中的列映射到直播间名称字段</p>
                        <div id="roomFieldMappings" class="mb-4"></div>
                        
                        <h6 class="mb-3">导入范围</h6>
                        <div class="row g-3 mb-3">
                            <div class="col-md-6">
                                <label for="roomStartRow" class="form-label">起始行</label>
                                <input type="number" class="form-control" id="roomStartRow" min="1" value="1">
                                <div class="form-text">从第几行开始导入（含表头）</div>
                            </div>
                            <div class="col-md-6">
                                <label for="roomEndRow" class="form-label">结束行</label>
                                <input type="number" class="form-control" id="roomEndRow">
                                <div class="form-text">留空表示导入到最后一行</div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-primary" id="roomUploadExcelBtn" disabled>
                        <i class="bi bi-upload me-1"></i>上传并预览
                    </button>
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                        <i class="bi bi-x-circle me-1"></i>取消
                    </button>
                    <button type="button" class="btn btn-secondary" id="roomPrevStepBtn" style="display: none;">
                        <i class="bi bi-arrow-left me-1"></i>上一步
                    </button>
                    <button type="button" class="btn btn-primary" id="roomNextStepBtn" style="display: none;">
                        <i class="bi bi-arrow-right me-1"></i>下一步
                    </button>
                    <button type="button" class="btn btn-success" id="roomStartImportBtn" style="display: none;">
                        <i class="bi bi-check-circle me-1"></i>开始导入
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- 销售Excel导入模态框 -->
    <div class="modal fade" id="salesExcelImportModal" tabindex="-1" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">销售Excel导入</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <!-- 步骤进度条 -->
                    <div class="mb-4">
                        <div class="progress" style="height: 4px;">
                            <div id="salesImportProgressBar" class="progress-bar" role="progressbar" style="width: 33%;" aria-valuenow="33" aria-valuemin="0" aria-valuemax="100"></div>
                        </div>
                        <div class="d-flex justify-content-between mt-2">
                            <div class="step active" onclick="goToStep(1)">
                                <span class="step-number">1</span>
                                <span class="step-text">上传文件</span>
                            </div>
                            <div class="step" onclick="goToStep(2)">
                                <span class="step-number">2</span>
                                <span class="step-text">数据预览</span>
                            </div>
                            <div class="step" onclick="goToStep(3)">
                                <span class="step-number">3</span>
                                <span class="step-text">导入设置</span>
                            </div>
                        </div>
                    </div>

                    <!-- 步骤1：上传文件 -->
                    <div id="step1" class="import-step">
                        <div class="text-center p-4 border rounded mb-3" style="border-style: dashed !important;">
                            <i class="bi bi-file-earmark-excel" style="font-size: 3rem; color: #28a745;"></i>
                            <h5 class="mt-3">选择Excel文件</h5>
                            <p class="text-muted">支持.xlsx和.xls格式</p>
                            <input type="file" id="salesExcelFile" class="d-none" accept=".xlsx,.xls">
                            <button type="button" class="btn btn-outline-primary mt-2" onclick="document.getElementById('salesExcelFile').click()">
                                <i class="bi bi-upload me-1"></i>选择文件
                            </button>
                            <div id="selectedSalesFileName" class="mt-2 text-muted"></div>
                        </div>
                    </div>

                    <!-- 步骤2：数据预览 -->
                    <div id="step2" class="import-step" style="display: none;">
                        <h6 class="mb-3">数据预览（全部数据）</h6>
                        <div class="d-flex justify-content-between align-items-center mb-2">
                            <div class="text-muted">
                                <small>总行数: <span id="salesTotalRows">0</span></small>
                            </div>
                            <div class="pagination-controls">
                                <button class="btn btn-sm btn-outline-secondary" id="prevSalesPageBtn" disabled>
                                    <i class="bi bi-chevron-left"></i> 上一页
                                </button>
                                <span class="mx-2">
                                    第 <span id="currentSalesPage">1</span> / <span id="totalSalesPages">1</span> 页
                                </span>
                                <button class="btn btn-sm btn-outline-secondary" id="nextSalesPageBtn">
                                    <i class="bi bi-chevron-right"></i> 下一页
                                </button>
                                <select id="salesPageSizeSelector" class="form-select form-select-sm ms-2" style="width: auto;">
                                    <option value="10">10行/页</option>
                                    <option value="20">20行/页</option>
                                    <option value="50">50行/页</option>
                                    <option value="100">100行/页</option>
                                </select>
                            </div>
                        </div>
                        <div class="table-responsive preview-table-container">
                            <table class="table table-sm table-bordered" id="salesPreviewTable">
                                <thead>
                                    <tr id="salesPreviewTableHeader"></tr>
                                </thead>
                                <tbody id="salesPreviewTableBody"></tbody>
                            </table>
                        </div>
                    </div>

                    <!-- 步骤3：导入设置 -->
                    <div id="step3" class="import-step" style="display: none;">
                        <h6 class="mb-3">字段映射</h6>
                        <p class="text-muted small mb-3">请将Excel表格中的列映射到对应的销售字段</p>
                        <div id="salesFieldMappings" class="mb-4"></div>

                        <h6 class="mb-3">导入范围</h6>
                        <div class="row g-3 mb-3">
                            <div class="col-md-6">
                                <label for="salesStartRow" class="form-label">起始行</label>
                                <input type="number" class="form-control" id="salesStartRow" min="1" value="1">
                                <div class="form-text">从第几行开始导入（含表头）</div>
                            </div>
                            <div class="col-md-6">
                                <label for="salesEndRow" class="form-label">结束行</label>
                                <input type="number" class="form-control" id="salesEndRow">
                                <div class="form-text">留空表示导入到最后一行</div>
                            </div>
                        </div>

                        <div class="alert alert-info">
                            <i class="bi bi-info-circle me-2"></i>
                            <span>销售组为必填字段，重复的销售组将被跳过</span>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" id="prevSalesStepBtn" class="btn btn-outline-primary" style="display: none;" onclick="prevStep()">上一步</button>
                    <button type="button" id="nextSalesStepBtn" class="btn btn-primary" onclick="nextStep()">下一步</button>
                    <button type="button" id="startSalesImportBtn" class="btn btn-success" style="display: none;" onclick="startImport()">开始导入</button>
                </div>
            </div>
        </div>
    </div>
    
    <!-- 电商店铺Excel导入模态框 -->
    <div class="modal fade" id="ecommerceExcelImportModal" tabindex="-1" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">电商店铺Excel导入</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <!-- 步骤进度条 -->
                    <div class="mb-4">
                        <div class="progress" style="height: 4px;">
                            <div id="ecommerceImportProgressBar" class="progress-bar" role="progressbar" style="width: 33%;" aria-valuenow="33" aria-valuemin="0" aria-valuemax="100"></div>
                        </div>
                        <div class="d-flex justify-content-between mt-2">
                            <div class="step active" onclick="ecommerceGoToStep(1)">
                                <span class="step-number">1</span>
                                <span class="step-text">上传文件</span>
                            </div>
                            <div class="step" onclick="ecommerceGoToStep(2)">
                                <span class="step-number">2</span>
                                <span class="step-text">数据预览</span>
                            </div>
                            <div class="step" onclick="ecommerceGoToStep(3)">
                                <span class="step-number">3</span>
                                <span class="step-text">导入设置</span>
                            </div>
                        </div>
                    </div>

                    <!-- 步骤1：上传文件 -->
                    <div id="ecommerceStep1" class="import-step">
                        <div class="text-center p-4 border rounded mb-3" style="border-style: dashed !important;">
                            <i class="bi bi-file-earmark-excel" style="font-size: 3rem; color: #28a745;"></i>
                            <h5 class="mt-3">选择Excel文件</h5>
                            <p class="text-muted">支持.xlsx和.xls格式</p>
                            <input type="file" id="ecommerceExcelFile" class="d-none" accept=".xlsx,.xls">
                            <button type="button" class="btn btn-outline-primary mt-2" onclick="document.getElementById('ecommerceExcelFile').click()">
                                <i class="bi bi-upload me-1"></i>选择文件
                            </button>
                            <div id="selectedEcommerceFileName" class="mt-2 text-muted"></div>
                        </div>
                    </div>

                    <!-- 步骤2：数据预览 -->
                    <div id="ecommerceStep2" class="import-step" style="display: none;">
                        <h6 class="mb-3">数据预览 <small class="text-muted">(显示前10行)</small></h6>
                        <div class="d-flex justify-content-between mb-2">
                            <div>
                                <span class="badge bg-info">总行数: <span id="ecommerceTotalRows">0</span></span>
                            </div>
                            <div>
                                <button type="button" class="btn btn-sm btn-outline-secondary me-2" id="ecommercePrevPageBtn" disabled>
                                    <i class="bi bi-chevron-left"></i> 上一页
                                </button>
                                <span id="ecommercePageInfo">第 1 页</span>
                                <button type="button" class="btn btn-sm btn-outline-secondary ms-2" id="ecommerceNextPageBtn">
                                    下一页 <i class="bi bi-chevron-right"></i>
                                </button>
                            </div>
                        </div>
                        <div class="table-responsive preview-table-container">
                            <table class="table table-sm table-bordered" id="ecommercePreviewTable">
                                <thead>
                                    <tr id="ecommercePreviewTableHeader"></tr>
                                </thead>
                                <tbody id="ecommercePreviewTableBody"></tbody>
                            </table>
                        </div>
                    </div>

                    <!-- 步骤3：导入设置 -->
                    <div id="ecommerceStep3" class="import-step" style="display: none;">
                        <h6 class="mb-3">字段映射</h6>
                        <p class="text-muted small mb-3">请将Excel表格中的列映射到对应的电商店铺字段</p>
                        <div id="ecommerceFieldMappings" class="mb-4"></div>

                        <h6 class="mb-3">导入范围</h6>
                        <div class="row g-3 mb-3">
                            <div class="col-md-6">
                                <label for="ecommerceStartRow" class="form-label">起始行</label>
                                <input type="number" class="form-control" id="ecommerceStartRow" min="1" value="1">
                                <div class="form-text">从第几行开始导入（含表头）</div>
                            </div>
                            <div class="col-md-6">
                                <label for="ecommerceEndRow" class="form-label">结束行</label>
                                <input type="number" class="form-control" id="ecommerceEndRow">
                                <div class="form-text">留空表示导入到最后一行</div>
                            </div>
                        </div>

                        <div class="alert alert-info">
                            <i class="bi bi-info-circle me-2"></i>
                            <span>平台和店铺为必填字段，重复的记录将被跳过</span>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" id="ecommercePrevStepBtn" class="btn btn-outline-primary" style="display: none;" onclick="ecommercePrevStep()">上一步</button>
                    <button type="button" id="ecommerceNextStepBtn" class="btn btn-primary" onclick="ecommerceNextStep()">下一步</button>
                    <button type="button" id="ecommerceStartImportBtn" class="btn btn-success" style="display: none;" onclick="startEcommerceImport()">开始导入</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 直播间删除二次确认模态框 -->
    <div class="modal fade" id="confirmDeleteModal" data-bs-backdrop="static" data-bs-keyboard="false" tabindex="-1" aria-labelledby="confirmDeleteModalLabel" aria-hidden="true">
        <!-- ... existing code ... -->
    </div>

    <!-- 添加主播组编辑模态框 -->
    <div class="modal fade" id="editHostModal" tabindex="-1" aria-labelledby="editHostModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="editHostModalLabel">编辑主播组</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="关闭"></button>
                </div>
                <div class="modal-body" id="editHostModalBody">
                    <div id="editHostModalAlert" class="alert d-none mb-3" role="alert"></div>
                    <!-- 表单将由JavaScript动态添加 -->
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary" id="saveEditedHostBtn">保存</button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // 全局变量
        let editModal;
        let excelImportModal;
        let hostExcelImportModal;
        let roomExcelImportModal;
        let salesExcelImportModal;
        let ecommerceExcelImportModal;
        let currentStep = 1;
        let excelColumns = [];
        let totalExcelRows = 0;
        let excelFile = null;
        let allExcelData = []; // 存储所有Excel数据
        let currentPage = 1;
        let pageSize = 10;
        
        // 主播Excel导入相关变量
        let hostCurrentStep = 1;
        let hostExcelColumns = [];
        let hostTotalExcelRows = 0;
        let hostExcelFile = null;
        let hostAllExcelData = [];
        let hostCurrentPage = 1;
        let hostPageSize = 10;
        
        // 直播间Excel导入相关变量
        let roomCurrentStep = 1;
        let roomExcelColumns = [];
        let roomTotalExcelRows = 0;
        let roomExcelFile = null;
        let roomAllExcelData = [];
        let roomCurrentPage = 1;
        let roomPageSize = 10;
        
        // 销售Excel导入相关变量
        let salesCurrentStep = 1;
        let salesExcelColumns = [];
        let salesTotalExcelRows = 0;
        let salesExcelFile = null;
        let salesAllExcelData = [];
        let salesCurrentPage = 1;
        let salesPageSize = 10;
        
        // 电商店铺Excel导入相关变量
        let ecommerceCurrentStep = 1;
        let ecommerceExcelColumns = [];
        let ecommerceTotalExcelRows = 0;
        let ecommerceExcelFile = null;
        let ecommerceAllExcelData = [];
        let ecommerceCurrentPage = 1;
        let ecommercePageSize = 10;
        
        // 资源加载检测和Loading控制类
        class LoadingManager {
            constructor(pageTitle = '系统') {
                this.loadingOverlay = document.getElementById('loading-overlay');
                this.loadingStatus = document.getElementById('loading-status');
                this.loadingText = document.querySelector('.loading-text');
                this.pageTitle = pageTitle;
                this.resources = new Map();
                this.loadingMessages = [
                    '正在加载静态资源',
                    '正在初始化Bootstrap框架',
                    '正在加载XLSX库',
                    '正在准备用户界面',
                    '即将完成加载'
                ];
                this.currentMessageIndex = 0;
                this.startTime = performance.now();

                // 设置页面标题
                if (this.loadingText) {
                    this.loadingText.textContent = `正在加载${this.pageTitle}`;
                }
            }

            // 更新加载状态文本
            updateLoadingMessage() {
                if (this.currentMessageIndex < this.loadingMessages.length - 1) {
                    this.currentMessageIndex++;
                    if (this.loadingStatus) {
                        this.loadingStatus.textContent = this.loadingMessages[this.currentMessageIndex];
                    }
                }
            }

            // 等待单个资源加载
            waitForResource(checkFunction, resourceName, timeout = 5000) {
                return new Promise((resolve, reject) => {
                    const startTime = Date.now();

                    const check = () => {
                        if (checkFunction()) {
                            console.log(`✅ ${resourceName} 已加载`);
                            resolve(resourceName);
                        } else if (Date.now() - startTime > timeout) {
                            console.error(`❌ ${resourceName} 加载超时`);
                            reject(new Error(`${resourceName} 加载超时`));
                        } else {
                            setTimeout(check, 50);
                        }
                    };

                    check();
                });
            }

            // 开始加载检测
            async startLoading(customResources = []) {
                try {
                    console.log(`🚀 开始检测${this.pageTitle}资源加载状态...`);

                    // 默认资源检测
                    const defaultChecks = [
                        { check: () => typeof $ !== 'undefined', name: 'jQuery' },
                        { check: () => typeof bootstrap !== 'undefined', name: 'Bootstrap' },
                        { check: () => typeof XLSX !== 'undefined', name: 'XLSX' }
                    ];

                    // 合并自定义资源检测
                    const allChecks = [...defaultChecks, ...customResources];

                    // 逐个检测资源
                    for (const resource of allChecks) {
                        this.updateLoadingMessage();
                        await this.waitForResource(resource.check, resource.name);
                    }

                    // 额外等待确保所有资源完全就绪
                    this.updateLoadingMessage();
                    await new Promise(resolve => setTimeout(resolve, 300));

                    const loadingTime = performance.now() - this.startTime;
                    console.log(`✅ ${this.pageTitle}所有资源加载完成，耗时: ${loadingTime.toFixed(2)}ms`);

                    // 隐藏loading并初始化应用
                    this.hideLoading();
                    this.onLoadingComplete();

                } catch (error) {
                    console.error(`❌ ${this.pageTitle}资源加载失败:`, error);
                    this.showError(error.message);
                }
            }

            // 隐藏loading动画
            hideLoading() {
                if (this.loadingOverlay) {
                    this.loadingOverlay.classList.add('fade-out');
                    setTimeout(() => {
                        this.loadingOverlay.style.display = 'none';
                    }, 500);
                }
            }

            // 显示错误信息
            showError(message) {
                if (this.loadingStatus) {
                    this.loadingStatus.textContent = `加载失败: ${message}`;
                }
                if (this.loadingOverlay) {
                    this.loadingOverlay.style.background = 'linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%)';
                }

                // 5秒后尝试重新加载
                setTimeout(() => {
                    location.reload();
                }, 5000);
            }

            // 加载完成回调（可被重写）
            onLoadingComplete() {
                console.log(`🎉 ${this.pageTitle}初始化完成`);
            }
        }

        // 页面加载完成后执行
        document.addEventListener('DOMContentLoaded', function() {
            // 初始化Loading管理器
            const loadingManager = new LoadingManager('表单预设管理');
            loadingManager.startLoading();
        });

        // 原有的页面初始化代码
        document.addEventListener('DOMContentLoaded', function() {
            // 初始化模态框
            editModal = new bootstrap.Modal(document.getElementById('editModal'));
            excelImportModal = new bootstrap.Modal(document.getElementById('excelImportModal'));
            hostExcelImportModal = new bootstrap.Modal(document.getElementById('hostExcelImportModal'));
            roomExcelImportModal = new bootstrap.Modal(document.getElementById('roomExcelImportModal'));
            salesExcelImportModal = new bootstrap.Modal(document.getElementById('salesExcelImportModal'));
            ecommerceExcelImportModal = new bootstrap.Modal(document.getElementById('ecommerceExcelImportModal'));
            
            // 加载预设值
            loadPresets();
            
            // 添加标签页切换事件
            document.querySelectorAll('button[data-bs-toggle="tab"]').forEach(tab => {
                tab.addEventListener('shown.bs.tab', function(event) {
                    if (event.target.id === 'sales-tab') {
                        // 当切换到销售预设管理标签页时，重新加载销售组数据
                        fetch('/api/presets/sales')
                            .then(response => {
                                if (!response.ok) {
                                    throw new Error('获取销售预设失败');
                                }
                                return response.json();
                            })
                            .then(data => {
                                // 渲染销售列表
                                renderSalesList(data.sales || []);
                            })
                            .catch(error => {
                                console.error('加载销售预设出错:', error);
                                showAlert('salesAlert', '加载销售预设失败', 'danger');
                            });
                    }
                    else if (event.target.id === 'ecommerce-tab') {
                        // 当切换到电商店铺预设管理标签页时，重新加载电商店铺数据
                        loadEcommerceStores();
                    }
                });
            });
            
            // 添加电商店铺表单提交事件
            document.getElementById('addEcommerceForm').addEventListener('submit', function(e) {
                e.preventDefault();
                addEcommerceStore();
            });
            
            // 绑定批量删除电商店铺按钮事件
            document.getElementById('batchDeleteEcommerceBtn').addEventListener('click', batchDeleteEcommerceStores);
            
            // 绑定全选电商店铺复选框事件
            document.getElementById('selectAllEcommerce').addEventListener('change', function() {
                const isChecked = this.checked;
                document.querySelectorAll('.ecommerce-checkbox').forEach(checkbox => {
                    checkbox.checked = isChecked;
                });
                
                // 启用或禁用批量删除按钮
                document.getElementById('batchDeleteEcommerceBtn').disabled = !isChecked && document.querySelectorAll('.ecommerce-checkbox:checked').length === 0;
            });
            
            // 绑定电商店铺Excel导入按钮事件
            document.getElementById('excelImportEcommerceBtn').addEventListener('click', function() {
                ecommerceExcelImportModal.show();
            });
            
            // 监听电商店铺Excel文件选择事件
            document.getElementById('ecommerceExcelFile').addEventListener('change', function(e) {
                if (this.files.length > 0) {
                    ecommerceExcelFile = this.files[0];
                    document.getElementById('selectedEcommerceFileName').textContent = ecommerceExcelFile.name;
                    ecommerceUploadExcel();
                }
            });
            
            // 电商店铺Excel预览翻页按钮
            document.getElementById('ecommercePrevPageBtn').addEventListener('click', function() {
                if (ecommerceCurrentPage > 1) {
                    ecommerceCurrentPage--;
                    ecommerceRenderPreviewTable(ecommerceAllExcelData);
                }
            });
            
            document.getElementById('ecommerceNextPageBtn').addEventListener('click', function() {
                const totalPages = Math.ceil(ecommerceTotalExcelRows / ecommercePageSize);
                if (ecommerceCurrentPage < totalPages) {
                    ecommerceCurrentPage++;
                    ecommerceRenderPreviewTable(ecommerceAllExcelData);
                }
            });
            
            // 添加销售组表单提交事件
            document.getElementById('addSalesForm').addEventListener('submit', function(e) {
                e.preventDefault();
                
                const group = document.getElementById('salesGroup').value.trim();
                const leader = document.getElementById('salesLeader').value.trim();
                const members = document.getElementById('salesMembers').value.trim();
                
                if (!group || !leader || !members) {
                    showAlert('salesAlert', '请填写所有必填字段', 'warning');
                    return;
                }
                
                // 发送添加请求
                fetch('/api/presets/sales', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        group: group,
                        leader: leader,
                        members: members
                    })
                })
                .then(response => {
                    if (!response.ok) {
                        return response.json().then(data => {
                            throw new Error(data.detail || '添加销售组失败');
                        });
                    }
                    return response.json();
                })
                .then(data => {
                    // 清空表单
                    document.getElementById('salesGroup').value = '';
                    document.getElementById('salesLeader').value = '';
                    document.getElementById('salesMembers').value = '';
                    
                    // 显示成功消息
                    showAlert('salesAlert', '销售组添加成功', 'success');
                    
                    // 重新加载预设值
                    loadPresets();
                })
                .catch(error => {
                    showAlert('salesAlert', error.message, 'danger');
                });
            });
            
            // 添加店铺表单提交事件
            document.getElementById('addShopForm').addEventListener('submit', function(e) {
                e.preventDefault();
                addShop();
            });
            
            // 添加主播表单提交事件
            document.getElementById('addHostForm').addEventListener('submit', function(e) {
                e.preventDefault();
                addHost(e);
            });
            
            // 添加直播间表单提交事件
            document.getElementById('addRoomForm').addEventListener('submit', function(e) {
                e.preventDefault();
                addRoom();
            });
            
            // 保存编辑按钮点击事件
            document.getElementById('saveEdit').addEventListener('click', function() {
                saveEdit();
            });
            
            // Excel文件选择事件
            document.getElementById('excelFile').addEventListener('change', function(e) {
                if (e.target.files.length > 0) {
                    excelFile = e.target.files[0];
                    document.getElementById('selectedFileName').textContent = excelFile.name;
                }
            });

            // 主播Excel文件选择事件
            document.getElementById('hostExcelFile').addEventListener('change', function(e) {
                const file = e.target.files[0];
                if (file) {
                    document.getElementById('hostSelectedFileName').textContent = file.name;
                    document.getElementById('hostUploadExcelBtn').disabled = false;
                    hostExcelFile = file;
                } else {
                    document.getElementById('hostSelectedFileName').textContent = '未选择文件';
                    document.getElementById('hostUploadExcelBtn').disabled = true;
                    hostExcelFile = null;
                }
            });
            
            // 直播间Excel文件选择事件
            document.getElementById('roomExcelFile').addEventListener('change', function(e) {
                const file = e.target.files[0];
                if (file) {
                    document.getElementById('roomSelectedFileName').textContent = file.name;
                    document.getElementById('roomUploadExcelBtn').disabled = false;
                    roomExcelFile = file;
                } else {
                    document.getElementById('roomSelectedFileName').textContent = '未选择文件';
                    document.getElementById('roomUploadExcelBtn').disabled = true;
                    roomExcelFile = null;
                }
            });
            
            // 主播Excel上传按钮点击事件
            document.getElementById('hostUploadExcelBtn').addEventListener('click', hostUploadExcel);
            
            // 直播间Excel上传按钮点击事件
            document.getElementById('roomUploadExcelBtn').addEventListener('click', roomUploadExcel);
            
            // 主播导入步骤按钮事件
            document.getElementById('hostPrevStepBtn').addEventListener('click', function() {
                hostGoToStep(hostCurrentStep - 1);
            });
            
            document.getElementById('hostNextStepBtn').addEventListener('click', function() {
                hostGoToStep(hostCurrentStep + 1);
            });
            
            document.getElementById('hostStartImportBtn').addEventListener('click', hostStartImport);
            
            // 直播间导入步骤按钮事件
            document.getElementById('roomPrevStepBtn').addEventListener('click', function() {
                roomGoToStep(roomCurrentStep - 1);
            });
            
            document.getElementById('roomNextStepBtn').addEventListener('click', function() {
                roomGoToStep(roomCurrentStep + 1);
            });
            
            document.getElementById('roomStartImportBtn').addEventListener('click', roomStartImport);
            
            // 主播分页控制
            document.getElementById('hostPrevPageBtn').addEventListener('click', function() {
                if (hostCurrentPage > 1) {
                    hostCurrentPage--;
                    hostRenderCurrentPage();
                }
            });
            
            document.getElementById('hostNextPageBtn').addEventListener('click', function() {
                const totalPages = Math.ceil(hostAllExcelData.length / hostPageSize);
                if (hostCurrentPage < totalPages) {
                    hostCurrentPage++;
                    hostRenderCurrentPage();
                }
            });
            
            document.getElementById('hostPageSizeSelector').addEventListener('change', function() {
                hostPageSize = parseInt(this.value);
                hostCurrentPage = 1;
                hostRenderCurrentPage();
            });
            
            // 直播间分页控制
            document.getElementById('roomPrevPageBtn').addEventListener('click', function() {
                if (roomCurrentPage > 1) {
                    roomCurrentPage--;
                    roomRenderCurrentPage();
                }
            });
            
            document.getElementById('roomNextPageBtn').addEventListener('click', function() {
                const totalPages = Math.ceil(roomAllExcelData.length / roomPageSize);
                if (roomCurrentPage < totalPages) {
                    roomCurrentPage++;
                    roomRenderCurrentPage();
                }
            });
            
            document.getElementById('roomPageSizeSelector').addEventListener('change', function() {
                roomPageSize = parseInt(this.value);
                roomCurrentPage = 1;
                roomRenderCurrentPage();
            });
        });
        
        // Excel导入 - 下一步
        function nextStep() {
            if (currentStep === 1) {
                // 从步骤1到步骤2：上传文件并预览
                if (!excelFile) {
                    showAlert('shopAlert', '请选择Excel文件', 'warning');
                    return;
                }
                uploadExcel();
            } else if (currentStep === 2) {
                // 从步骤2到步骤3：配置导入设置
                goToStep(3);
            }
        }
        
        // Excel导入 - 上一步
        function prevStep() {
            if (currentStep > 1) {
                goToStep(currentStep - 1);
            }
        }
        
        // Excel导入 - 跳转到指定步骤
        function goToStep(step) {
            // 隐藏所有步骤
            document.querySelectorAll('.import-step').forEach(el => {
                el.style.display = 'none';
            });
            
            // 显示当前步骤
            document.getElementById(`step${step}`).style.display = 'block';
            
            // 更新进度条
            const progressBar = document.getElementById('importProgressBar');
            progressBar.style.width = `${step * 33}%`;
            progressBar.setAttribute('aria-valuenow', step * 33);
            
            // 更新步骤指示器
            document.querySelectorAll('.step').forEach((el, index) => {
                if (index + 1 <= step) {
                    el.classList.add('active');
                } else {
                    el.classList.remove('active');
                }
            });
            
            // 更新按钮状态
            if (step === 1) {
                document.getElementById('prevStepBtn').style.display = 'none';
                document.getElementById('nextStepBtn').style.display = 'inline-block';
                document.getElementById('startImportBtn').style.display = 'none';
            } else if (step === 2) {
                document.getElementById('prevStepBtn').style.display = 'inline-block';
                document.getElementById('nextStepBtn').style.display = 'inline-block';
                document.getElementById('startImportBtn').style.display = 'none';
            } else if (step === 3) {
                document.getElementById('prevStepBtn').style.display = 'inline-block';
                document.getElementById('nextStepBtn').style.display = 'none';
                document.getElementById('startImportBtn').style.display = 'inline-block';
            }
            
            currentStep = step;
        }
        
        // 上传Excel文件并预览
        async function uploadExcel() {
            try {
                const formData = new FormData();
                formData.append('file', excelFile);
                
                const response = await fetch('/api/presets/shops/upload-excel', {
                    method: 'POST',
                    body: formData
                });
                
                if (!response.ok) {
                    const errorData = await response.json();
                    throw new Error(errorData.detail || '上传Excel文件失败');
                }
                
                const data = await response.json();
                
                // 保存列信息和总行数
                excelColumns = data.columns;
                totalExcelRows = data.total_rows;
                
                // 渲染预览表格
                renderPreview(data);
                
                // 渲染字段映射选项
                renderFieldMappings(data.columns);
                
                // 设置总行数和结束行
                document.getElementById('totalRows').textContent = totalExcelRows;
                document.getElementById('endRow').value = totalExcelRows;
                
                // 跳转到步骤2
                goToStep(2);
            } catch (error) {
                showAlert('shopAlert', error.message, 'danger');
            }
        }
        
        // 渲染预览表格
        function renderPreview(data) {
            const headerRow = document.getElementById('previewTableHeader');
            const tableBody = document.getElementById('previewTableBody');
            
            // 保存所有数据
            allExcelData = data.data;
            
            // 清空表格
            headerRow.innerHTML = '';
            tableBody.innerHTML = '';
            
            // 添加表头
            data.columns.forEach(column => {
                const th = document.createElement('th');
                th.textContent = column;
                th.setAttribute('title', column); // 添加工具提示
                headerRow.appendChild(th);
            });
            
            // 设置总行数和总页数
            const totalRows = data.total_rows;
            document.getElementById('totalRows').textContent = totalRows;
            
            const totalPages = Math.ceil(allExcelData.length / pageSize);
            document.getElementById('totalPages').textContent = totalPages;
            
            // 渲染当前页数据
            renderCurrentPage();
            
            // 添加触摸滑动支持
            enableTouchScroll();
            
            // 设置分页控件事件
            setupPaginationControls();
        }
        
        // 渲染当前页数据
        function renderCurrentPage() {
            const tableBody = document.getElementById('previewTableBody');
            tableBody.innerHTML = '';
            
            // 计算当前页的数据范围
            const startIndex = (currentPage - 1) * pageSize;
            const endIndex = Math.min(startIndex + pageSize, allExcelData.length);
            
            // 获取当前页数据
            const currentPageData = allExcelData.slice(startIndex, endIndex);
            
            // 添加数据行
            currentPageData.forEach((row, rowIndex) => {
                const tr = document.createElement('tr');
                row.forEach(cell => {
                    const td = document.createElement('td');
                    const cellContent = cell !== null ? cell : '';
                    td.textContent = cellContent;
                    
                    // 为单元格添加工具提示，显示完整内容
                    if (cellContent && cellContent.toString().length > 20) {
                        td.setAttribute('title', cellContent);
                        td.classList.add('text-truncate');
                    }
                    
                    tr.appendChild(td);
                });
                tableBody.appendChild(tr);
            });
            
            // 更新当前页显示
            document.getElementById('currentPage').textContent = currentPage;
            
            // 更新分页按钮状态
            updatePaginationButtons();
        }
        
        // 设置分页控件事件
        function setupPaginationControls() {
            // 上一页按钮
            document.getElementById('prevPageBtn').addEventListener('click', function() {
                if (currentPage > 1) {
                    currentPage--;
                    renderCurrentPage();
                }
            });
            
            // 下一页按钮
            document.getElementById('nextPageBtn').addEventListener('click', function() {
                const totalPages = Math.ceil(allExcelData.length / pageSize);
                if (currentPage < totalPages) {
                    currentPage++;
                    renderCurrentPage();
                }
            });
            
            // 每页行数选择器
            document.getElementById('pageSizeSelector').addEventListener('change', function(e) {
                pageSize = parseInt(e.target.value);
                currentPage = 1; // 重置到第一页
                
                // 更新总页数
                const totalPages = Math.ceil(allExcelData.length / pageSize);
                document.getElementById('totalPages').textContent = totalPages;
                
                renderCurrentPage();
            });
        }
        
        // 更新分页按钮状态
        function updatePaginationButtons() {
            const prevBtn = document.getElementById('prevPageBtn');
            const nextBtn = document.getElementById('nextPageBtn');
            const totalPages = Math.ceil(allExcelData.length / pageSize);
            
            prevBtn.disabled = currentPage <= 1;
            nextBtn.disabled = currentPage >= totalPages;
        }
        
        // 启用触摸滑动支持
        function enableTouchScroll() {
            const container = document.querySelector('.preview-table-container');
            if (!container) return;
            
            let startX, startY, startScrollLeft, startScrollTop;
            
            container.addEventListener('touchstart', function(e) {
                startX = e.touches[0].pageX - container.offsetLeft;
                startY = e.touches[0].pageY - container.offsetTop;
                startScrollLeft = container.scrollLeft;
                startScrollTop = container.scrollTop;
            });
            
            container.addEventListener('touchmove', function(e) {
                e.preventDefault();
                const x = e.touches[0].pageX - container.offsetLeft;
                const y = e.touches[0].pageY - container.offsetTop;
                const walkX = (x - startX) * 1.5; // 滚动速度倍数
                const walkY = (y - startY) * 1.5; // 滚动速度倍数
                container.scrollLeft = startScrollLeft - walkX;
                container.scrollTop = startScrollTop - walkY;
            });
        }
        
        // 渲染字段映射选项
        function renderFieldMappings(columns) {
            const mappingsContainer = document.getElementById('fieldMappings');
            mappingsContainer.innerHTML = '';
            
            // 定义系统字段
            const systemFields = [
                { id: 'name', label: '店铺名称', required: true },
                { id: 'manager', label: '负责人', required: false },
                { id: 'douyin_abbr', label: '抖音缩写', required: false },
                { id: 'video_abbr', label: '视频号缩写', required: false },
                { id: 'xiaohongshu_abbr', label: '小红书缩写', required: false },
                { id: 'kuaishou_abbr', label: '快手缩写', required: false }
            ];
            
            // 为每个系统字段创建映射选择器
            systemFields.forEach(field => {
                const row = document.createElement('div');
                row.className = 'row mb-3 align-items-center';
                
                const labelCol = document.createElement('div');
                labelCol.className = 'col-md-4';
                
                const label = document.createElement('label');
                label.className = 'form-label mb-0';
                label.textContent = field.label;
                
                if (field.required) {
                    const requiredBadge = document.createElement('span');
                    requiredBadge.className = 'badge bg-danger ms-2';
                    requiredBadge.textContent = '必填';
                    label.appendChild(requiredBadge);
                }
                
                labelCol.appendChild(label);
                
                const selectCol = document.createElement('div');
                selectCol.className = 'col-md-8';
                
                const select = document.createElement('select');
                select.className = 'form-select field-mapping';
                select.dataset.field = field.id;
                
                // 添加空选项
                const emptyOption = document.createElement('option');
                emptyOption.value = '';
                emptyOption.textContent = '-- 不导入 --';
                select.appendChild(emptyOption);
                
                // 添加Excel列选项
                columns.forEach(column => {
                    const option = document.createElement('option');
                    option.value = column;
                    option.textContent = column;
                    
                    // 尝试自动匹配列名
                    if (
                        column.toLowerCase().includes(field.label.toLowerCase()) ||
                        (field.id === 'name' && column.toLowerCase().includes('店铺')) ||
                        (field.id === 'manager' && column.toLowerCase().includes('负责')) ||
                        (field.id === 'douyin_abbr' && column.toLowerCase().includes('抖音')) ||
                        (field.id === 'video_abbr' && column.toLowerCase().includes('视频')) ||
                        (field.id === 'xiaohongshu_abbr' && column.toLowerCase().includes('小红书')) ||
                        (field.id === 'kuaishou_abbr' && column.toLowerCase().includes('快手'))
                    ) {
                        option.selected = true;
                    }
                    
                    select.appendChild(option);
                });
                
                selectCol.appendChild(select);
                
                row.appendChild(labelCol);
                row.appendChild(selectCol);
                
                mappingsContainer.appendChild(row);
            });
        }
        
        // 开始导入
        async function startImport() {
            try {
                // 收集字段映射
                const fieldMappings = {};
                document.querySelectorAll('.field-mapping').forEach(select => {
                    if (select.value) {
                        fieldMappings[select.value] = select.dataset.field;
                    }
                });
                
                // 检查是否映射了店铺名称
                if (!Object.values(fieldMappings).includes('name')) {
                    showAlert('shopAlert', '必须映射店铺名称字段', 'warning');
                    return;
                }
                
                // 获取起始行和结束行
                const startRow = parseInt(document.getElementById('startRow').value) || 1;
                let endRow = document.getElementById('endRow').value ? parseInt(document.getElementById('endRow').value) : null;
                
                // 创建导入配置
                const importConfig = {
                    field_mappings: fieldMappings,
                    start_row: startRow,
                    end_row: endRow
                };
                
                // 创建FormData
                const formData = new FormData();
                formData.append('file', excelFile);
                
                // 发送请求
                const response = await fetch(`/api/presets/shops/import-excel?config=${encodeURIComponent(JSON.stringify(importConfig))}`, {
                    method: 'POST',
                    body: formData
                });
                
                if (!response.ok) {
                    const errorData = await response.json();
                    throw new Error(errorData.detail || '导入Excel数据失败');
                }
                
                const data = await response.json();
                
                // 关闭模态框
                excelImportModal.hide();
                
                // 显示成功消息
                showAlert('shopAlert', `Excel导入成功，成功导入${data.imported_count}条记录，跳过${data.skipped_count}条记录`, 'success');
                
                // 重新加载预设值
                loadPresets();
                
                // 重置导入状态
                resetImport();
            } catch (error) {
                showAlert('shopAlert', error.message, 'danger');
            }
        }
        
        // 重置导入状态
        function resetImport() {
            currentStep = 1;
            excelColumns = [];
            totalExcelRows = 0;
            excelFile = null;
            allExcelData = [];
            currentPage = 1;
            document.getElementById('excelFile').value = '';
            document.getElementById('selectedFileName').textContent = '';
            goToStep(1);
        }
        
        // 加载预设值
        async function loadPresets() {
            try {
                // 添加请求日志
                console.log('开始加载预设值...');
                
                // 加载店铺详细信息
                const shopsResponse = await fetch('/api/presets/shops/detail');
                if (!shopsResponse.ok) {
                    console.error('加载店铺预设失败:', shopsResponse.status);
                    throw new Error('加载店铺预设值失败');
                }
                const shopsData = await shopsResponse.json();
                console.log('获取到的店铺数据:', shopsData);
                
                // 加载主播信息（直接从hosts接口获取）
                const hostsResponse = await fetch('/api/presets/hosts');
                if (!hostsResponse.ok) {
                    console.error('加载主播预设失败:', hostsResponse.status);
                    throw new Error('加载主播预设值失败');
                }
                
                const hostsData = await hostsResponse.json();
                console.log('获取到的主播数据:', hostsData);
                
                // 加载直播间信息
                const roomsResponse = await fetch('/api/presets');
                if (!roomsResponse.ok) {
                    console.error('加载直播间预设失败:', roomsResponse.status);
                    throw new Error('加载直播间预设值失败');
                }
                
                const roomsData = await roomsResponse.json();
                console.log('获取到的直播间数据:', roomsData);
                
                // 渲染店铺列表
                renderShops(shopsData);
                
                // 渲染主播列表，确保传入正确的数据
                renderHosts(hostsData.hosts || []);
                
                // 渲染直播间列表
                renderRooms(roomsData.rooms || []);
                
                // 加载电商店铺数据
                loadEcommerceStores();
                
                console.log('预设值加载完成');
            } catch (error) {
                console.error('加载预设值出错:', error);
                showAlert('shopAlert', error.message, 'danger');
                showAlert('hostAlert', error.message, 'danger');
                showAlert('roomAlert', error.message, 'danger');
            }
        }
        
        // 渲染店铺列表
        function renderShops(shops) {
            const shopsList = document.getElementById('shopsList');
            shopsList.innerHTML = '';
            
            // 检查shops是否为数组，如果不是数组或为undefined，则使用空数组
            const shopsArray = Array.isArray(shops) ? shops : (shops && Array.isArray(shops.shops) ? shops.shops : []);
            
            if (shopsArray.length === 0) {
                shopsList.innerHTML = '<tr><td colspan="9" class="text-center text-muted">暂无店铺</td></tr>';
                return;
            }
            
            shopsArray.forEach((shop, index) => {
                const row = document.createElement('tr');
                row.innerHTML = `
                    <td><input type="checkbox" class="form-check-input shop-checkbox" data-shop-name="${shop.name}"></td>
                    <td>${index + 1}</td>
                    <td>${shop.manager || '-'}</td>
                    <td>${shop.name}</td>
                    <td>${shop.douyin_abbr || '-'}</td>
                    <td>${shop.video_abbr || '-'}</td>
                    <td>${shop.xiaohongshu_abbr || '-'}</td>
                    <td>${shop.kuaishou_abbr || '-'}</td>
                    <td>
                        <div class="preset-actions">
                            <button class="btn btn-sm btn-outline-primary" onclick="openEditModal('shop', '${shop.name}', ${JSON.stringify(shop).replace(/"/g, '&quot;')})">编辑</button>
                            <button class="btn btn-sm btn-outline-danger" onclick="deleteShop('${shop.name}')">删除</button>
                        </div>
                    </td>
                `;
                shopsList.appendChild(row);
            });
            
            // 添加复选框事件监听
            addShopCheckboxListeners();
        }
        
        // 渲染主播列表
        function renderHosts(hosts) {
            const hostsList = document.getElementById('hostsList');
            hostsList.innerHTML = '';
            
            // 检查hosts是否为有效数组
            if (!hosts || !Array.isArray(hosts) || hosts.length === 0) {
                hostsList.innerHTML = '<tr><td colspan="7" class="text-center text-muted">暂无主播组</td></tr>';
                return;
            }
            
            // 添加调试信息
            console.log('主播数据:', hosts);
            
            hosts.forEach((host, index) => {
                // 检查host是否为有效对象
                if (!host || typeof host !== 'object') {
                    console.error('无效的主播数据项:', host);
                    return;
                }
                
                const hostId = host.id || `host-${index}`;
                const leader = host.leader || '-';
                const group = host.group || '-';
                const hostNames = host.hosts || '-';
                const count = host.count || 0;
                
                const row = document.createElement('tr');
                row.innerHTML = `
                    <td><input type="checkbox" class="form-check-input host-checkbox" data-host-id="${hostId}"></td>
                    <td>${index + 1}</td>
                    <td>${leader}</td>
                    <td>${group}</td>
                    <td>${hostNames}</td>
                    <td>${count}</td>
                    <td>
                        <div class="preset-actions">
                            <button class="btn btn-sm btn-outline-primary" onclick="openEditHostModal('${hostId}')">编辑</button>
                            <button class="btn btn-sm btn-outline-danger" onclick="deleteHost('${hostId}', '${group}')">删除</button>
                        </div>
                    </td>
                `;
                hostsList.appendChild(row);
            });
            
            // 添加复选框事件监听
            addHostCheckboxListeners();
        }
        
        // 添加店铺
        async function addShop() {
            const shopName = document.getElementById('shopName').value.trim();
            const shopManager = document.getElementById('shopManager').value.trim();
            const douyinAbbr = document.getElementById('douyinAbbr').value.trim();
            const videoAbbr = document.getElementById('videoAbbr').value.trim();
            const xiaohongshuAbbr = document.getElementById('xiaohongshuAbbr').value.trim();
            const kuaishouAbbr = document.getElementById('kuaishouAbbr').value.trim();
            
            if (!shopName) return;
            
            try {
                const response = await fetch('/api/presets/shops', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        name: shopName,
                        manager: shopManager,
                        douyin_abbr: douyinAbbr,
                        video_abbr: videoAbbr,
                        xiaohongshu_abbr: xiaohongshuAbbr,
                        kuaishou_abbr: kuaishouAbbr
                    })
                });
                
                const data = await response.json();
                
                if (!response.ok) {
                    throw new Error(data.detail || '添加店铺失败');
                }
                
                // 清空输入框
                document.getElementById('shopName').value = '';
                document.getElementById('shopManager').value = '';
                document.getElementById('douyinAbbr').value = '';
                document.getElementById('videoAbbr').value = '';
                document.getElementById('xiaohongshuAbbr').value = '';
                document.getElementById('kuaishouAbbr').value = '';
                
                // 显示成功消息
                showAlert('shopAlert', '店铺添加成功', 'success');
                
                // 重新加载预设值
                loadPresets();
            } catch (error) {
                showAlert('shopAlert', error.message, 'danger');
            }
        }
        
        // 添加主播
        async function addHost(event) {
            event.preventDefault();
            
            const leader = document.getElementById('hostLeader').value.trim();
            const group = document.getElementById('hostGroup').value.trim();
            const hosts = document.getElementById('hostNames').value.trim();
            
            if (!leader || !group || !hosts) return;
            
            try {
                console.log('正在添加主播组...');
                const response = await fetch('/api/presets/hosts', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        leader: leader,
                        group: group,
                        hosts: hosts
                    })
                });
                
                const data = await response.json();
                
                if (!response.ok) {
                    throw new Error(data.detail || '添加主播组失败');
                }
                
                // 显示成功消息
                showAlert('hostAlert', '主播组添加成功', 'success');
                
                // 重置主播输入表单
                document.getElementById('hostLeader').value = '';
                document.getElementById('hostGroup').value = '';
                document.getElementById('hostNames').value = '';
                
                // 完全重置表单，避免表单验证状态残留
                document.getElementById('addHostForm').reset();
                
                // 重新加载预设值
                loadPresets();
            } catch (error) {
                showAlert('hostAlert', error.message, 'danger');
            }
        }
        
        // 删除店铺
        async function deleteShop(shopName) {
            if (!confirm(`确定要删除店铺 "${shopName}" 吗？`)) return;
            
            try {
                const response = await fetch(`/api/presets/shops/${encodeURIComponent(shopName)}`, {
                    method: 'DELETE'
                });
                
                const data = await response.json();
                
                if (!response.ok) {
                    throw new Error(data.detail || '删除店铺失败');
                }
                
                // 显示成功消息
                showAlert('shopAlert', '店铺删除成功', 'success');
                
                // 重新加载预设值
                loadPresets();
            } catch (error) {
                showAlert('shopAlert', error.message, 'danger');
            }
        }
        
        // 删除主播
        async function deleteHost(hostName) {
            if (!confirm(`确定要删除主播 "${hostName}" 吗？`)) return;
            
            try {
                const response = await fetch(`/api/presets/hosts/${encodeURIComponent(hostName)}`, {
                    method: 'DELETE'
                });
                
                const data = await response.json();
                
                if (!response.ok) {
                    throw new Error(data.detail || '删除主播失败');
                }
                
                // 显示成功消息
                showAlert('hostAlert', '主播删除成功', 'success');
                
                // 重置主播输入表单
                document.getElementById('hostName').value = '';
                
                // 完全重置表单，避免表单验证状态残留
                document.getElementById('addHostForm').reset();
                
                // 重新加载预设值
                loadPresets();
            } catch (error) {
                showAlert('hostAlert', error.message, 'danger');
            }
        }
        
        // 删除主播
        async function deleteHost(hostId, groupName) {
            if (!confirm(`确定要删除主播组 "${groupName}" 吗？`)) return;
            
            try {
                const response = await fetch(`/api/presets/hosts/${encodeURIComponent(hostId)}`, {
                    method: 'DELETE'
                });
                
                const data = await response.json();
                
                if (!response.ok) {
                    throw new Error(data.detail || '删除主播组失败');
                }
                
                // 显示成功消息
                showAlert('hostAlert', '主播组删除成功', 'success');
                
                // 重置表单
                document.getElementById('addHostForm').reset();
                
                // 重新加载预设值
                loadPresets();
            } catch (error) {
                showAlert('hostAlert', error.message, 'danger');
            }
        }
        
        // 打开编辑模态框
        function openEditModal(type, name, shopData) {
            document.getElementById('editType').value = type;
            document.getElementById('editOldName').value = name;
            
            if (type === 'shop') {
                document.getElementById('shopEditFields').style.display = 'block';
                document.getElementById('hostEditFields').style.display = 'none';
                document.getElementById('roomEditFields').style.display = 'none';
                document.getElementById('editModalTitle').textContent = '编辑店铺';
                
                // 填充店铺数据
                document.getElementById('editNewName').value = name;
                document.getElementById('editManager').value = shopData ? shopData.manager || '' : '';
                document.getElementById('editDouyinAbbr').value = shopData ? shopData.douyin_abbr || '' : '';
                document.getElementById('editVideoAbbr').value = shopData ? shopData.video_abbr || '' : '';
                document.getElementById('editXiaohongshuAbbr').value = shopData ? shopData.xiaohongshu_abbr || '' : '';
                document.getElementById('editKuaishouAbbr').value = shopData ? shopData.kuaishou_abbr || '' : '';
            } else if (type === 'host') {
                document.getElementById('shopEditFields').style.display = 'none';
                document.getElementById('hostEditFields').style.display = 'block';
                document.getElementById('roomEditFields').style.display = 'none';
                document.getElementById('editModalTitle').textContent = '编辑主播';
                document.getElementById('editHostName').value = name;
            } else if (type === 'room') {
                document.getElementById('shopEditFields').style.display = 'none';
                document.getElementById('hostEditFields').style.display = 'none';
                document.getElementById('roomEditFields').style.display = 'block';
                document.getElementById('editModalTitle').textContent = '编辑直播间';
                document.getElementById('editRoomName').value = name;
            }
            
            editModal.show();
        }
        
        // 保存编辑
        async function saveEdit() {
            const type = document.getElementById('editType').value;
            const oldName = document.getElementById('editOldName').value;
            
            if (type === 'shop') {
                const newName = document.getElementById('editNewName').value.trim();
                const manager = document.getElementById('editManager').value.trim();
                const douyinAbbr = document.getElementById('editDouyinAbbr').value.trim();
                const videoAbbr = document.getElementById('editVideoAbbr').value.trim();
                const xiaohongshuAbbr = document.getElementById('editXiaohongshuAbbr').value.trim();
                const kuaishouAbbr = document.getElementById('editKuaishouAbbr').value.trim();
                
                if (!newName) return;
                
                try {
                    const response = await fetch('/api/presets/shops', {
                        method: 'PUT',
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify({
                            old_name: oldName,
                            new_name: newName,
                            manager: manager,
                            douyin_abbr: douyinAbbr,
                            video_abbr: videoAbbr,
                            xiaohongshu_abbr: xiaohongshuAbbr,
                            kuaishou_abbr: kuaishouAbbr
                        })
                    });
                    
                    const data = await response.json();
                    
                    if (!response.ok) {
                        throw new Error(data.detail || '更新店铺失败');
                    }
                    
                    // 关闭模态框
                    editModal.hide();
                    
                    // 显示成功消息
                    showAlert('shopAlert', '店铺更新成功', 'success');
                    
                    // 重新加载预设值
                    loadPresets();
                } catch (error) {
                    showAlert('shopAlert', error.message, 'danger');
                    editModal.hide();
                }
            } else if (type === 'host') {
                const newName = document.getElementById('editHostName').value.trim();
                
                if (!newName) return;
                
                try {
                    const response = await fetch('/api/presets/hosts', {
                        method: 'PUT',
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify({
                            old_name: oldName,
                            new_name: newName
                        })
                    });
                    
                    const data = await response.json();
                    
                    if (!response.ok) {
                        throw new Error(data.detail || '更新主播失败');
                    }
                    
                    // 关闭模态框
                    editModal.hide();
                    
                    // 显示成功消息
                    showAlert('hostAlert', '主播更新成功', 'success');
                    
                    // 重新加载预设值
                    loadPresets();
                } catch (error) {
                    showAlert('hostAlert', error.message, 'danger');
                    editModal.hide();
                }
            } else if (type === 'room') {
                const newName = document.getElementById('editRoomName').value.trim();
                
                if (!newName) return;
                
                try {
                    const response = await fetch('/api/presets/rooms', {
                        method: 'PUT',
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify({
                            old_name: oldName,
                            new_name: newName
                        })
                    });
                    
                    const data = await response.json();
                    
                    if (!response.ok) {
                        throw new Error(data.detail || '更新直播间失败');
                    }
                    
                    // 关闭模态框
                    editModal.hide();
                    
                    // 显示成功消息
                    showAlert('roomAlert', '直播间更新成功', 'success');
                    
                    // 重新加载预设值
                    loadPresets();
                } catch (error) {
                    showAlert('roomAlert', error.message, 'danger');
                    editModal.hide();
                }
            }
        }
        
        // 显示提示信息
        function showAlert(elementId, message, type) {
            const alert = document.getElementById(elementId);
            alert.className = `alert alert-${type}`;
            alert.textContent = message;
            alert.classList.remove('d-none');
            
            // 3秒后自动隐藏
            setTimeout(() => {
                alert.classList.add('d-none');
            }, 3000);
        }

        // 主播上传Excel文件并预览
        async function hostUploadExcel() {
            try {
                if (!hostExcelFile) {
                    showAlert('hostAlert', '请选择Excel文件', 'warning');
                    return;
                }
                
                const formData = new FormData();
                formData.append('file', hostExcelFile);
                
                const response = await fetch('/api/presets/hosts/upload-excel', {
                    method: 'POST',
                    body: formData
                });
                
                if (!response.ok) {
                    const errorData = await response.json();
                    throw new Error(errorData.detail || '上传Excel文件失败');
                }
                
                const data = await response.json();
                
                // 保存列和数据
                hostExcelColumns = data.columns;
                hostTotalExcelRows = data.total_rows;
                hostAllExcelData = data.data;
                
                // 渲染预览
                hostRenderPreview(data);
                
                // 渲染字段映射
                hostRenderFieldMappings(data.columns);
                
                // 设置结束行默认值
                document.getElementById('hostEndRow').value = hostTotalExcelRows;
                
                // 进入下一步
                hostGoToStep(2);
            } catch (error) {
                showAlert('hostAlert', error.message, 'danger');
            }
        }
        
        // 主播渲染预览数据
        function hostRenderPreview(data) {
            // 保存所有数据
            hostAllExcelData = data.data;
            hostTotalExcelRows = data.total_rows;
            
            // 设置总行数
            document.getElementById('hostTotalRows').textContent = hostTotalExcelRows;
            
            // 计算总页数
            const totalPages = Math.ceil(hostAllExcelData.length / hostPageSize);
            document.getElementById('hostTotalPages').textContent = totalPages;
            
            // 渲染表头
            const headerRow = document.getElementById('hostPreviewTableHeader');
            headerRow.innerHTML = '';
            data.columns.forEach(column => {
                const th = document.createElement('th');
                th.textContent = column;
                headerRow.appendChild(th);
            });
            
            // 渲染当前页数据
            hostRenderCurrentPage();
        }
        
        // 主播渲染当前页数据
        function hostRenderCurrentPage() {
            const tableBody = document.getElementById('hostPreviewTableBody');
            tableBody.innerHTML = '';
            
            // 计算当前页的数据范围
            const startIndex = (hostCurrentPage - 1) * hostPageSize;
            const endIndex = Math.min(startIndex + hostPageSize, hostAllExcelData.length);
            
            // 更新当前页显示
            document.getElementById('hostCurrentPage').textContent = hostCurrentPage;
            
            // 更新分页按钮状态
            hostUpdatePaginationButtons();
            
            // 渲染当前页数据
            for (let i = startIndex; i < endIndex; i++) {
                const row = document.createElement('tr');
                hostAllExcelData[i].forEach(cell => {
                    const td = document.createElement('td');
                    td.textContent = cell === null ? '' : cell;
                    row.appendChild(td);
                });
                tableBody.appendChild(row);
            }
        }
        
        // 主播更新分页按钮状态
        function hostUpdatePaginationButtons() {
            const totalPages = Math.ceil(hostAllExcelData.length / hostPageSize);
            document.getElementById('hostPrevPageBtn').disabled = hostCurrentPage <= 1;
            document.getElementById('hostNextPageBtn').disabled = hostCurrentPage >= totalPages;
        }
        
        // 主播渲染字段映射
        function hostRenderFieldMappings(columns) {
            const mappingsContainer = document.getElementById('hostFieldMappings');
            mappingsContainer.innerHTML = '';
            
            // 只需要映射主播名称字段
            const fields = [
                { id: 'name', label: '主播名称' }
            ];
            
            fields.forEach(field => {
                const row = document.createElement('div');
                row.className = 'row mb-3 align-items-center';
                
                const labelCol = document.createElement('div');
                labelCol.className = 'col-md-3';
                labelCol.innerHTML = `<label class="form-label mb-0">${field.label}</label>`;
                
                const selectCol = document.createElement('div');
                selectCol.className = 'col-md-9';
                
                const select = document.createElement('select');
                select.className = 'form-select host-field-mapping';
                select.dataset.field = field.id;
                
                // 添加空选项
                const emptyOption = document.createElement('option');
                emptyOption.value = '';
                emptyOption.textContent = '-- 不导入 --';
                select.appendChild(emptyOption);
                
                // 添加Excel列选项
                columns.forEach(column => {
                    const option = document.createElement('option');
                    option.value = column;
                    option.textContent = column;
                    
                    // 尝试自动匹配列名
                    if (
                        column.toLowerCase().includes(field.label.toLowerCase()) ||
                        column.toLowerCase().includes('主播') ||
                        column.toLowerCase().includes('名称')
                    ) {
                        option.selected = true;
                    }
                    
                    select.appendChild(option);
                });
                
                selectCol.appendChild(select);
                
                row.appendChild(labelCol);
                row.appendChild(selectCol);
                
                mappingsContainer.appendChild(row);
            });
        }
        
        // 主播导入步骤控制
        function hostGoToStep(step) {
            // 隐藏所有步骤
            document.querySelectorAll('#hostExcelImportModal .import-step').forEach(el => {
                el.style.display = 'none';
            });
            
            // 重置步骤指示器
            document.querySelectorAll('#hostExcelImportModal [id^="hostStep"]').forEach(el => {
                if (el.id.endsWith('Indicator')) {
                    el.className = 'btn btn-outline-secondary btn-sm rounded-circle mb-2';
                }
            });
            
            // 显示当前步骤
            document.getElementById(`hostStep${step}`).style.display = 'block';
            document.getElementById(`hostStep${step}Indicator`).className = 'btn btn-primary btn-sm rounded-circle mb-2';
            
            // 更新进度条
            const progress = document.getElementById('hostImportProgress');
            progress.style.width = `${step * 33}%`;
            progress.setAttribute('aria-valuenow', step * 33);
            
            // 更新按钮
            const prevBtn = document.getElementById('hostPrevStepBtn');
            const nextBtn = document.getElementById('hostNextStepBtn');
            const importBtn = document.getElementById('hostStartImportBtn');
            
            prevBtn.style.display = step > 1 ? 'block' : 'none';
            nextBtn.style.display = step < 3 ? 'block' : 'none';
            importBtn.style.display = step === 3 ? 'block' : 'none';
            
            // 保存当前步骤
            hostCurrentStep = step;
        }
        
        // 主播开始导入
        async function hostStartImport() {
            try {
                // 禁用导入按钮，防止重复点击
                const importBtn = document.getElementById('hostStartImportBtn');
                importBtn.disabled = true;
                importBtn.innerHTML = '<i class="bi bi-hourglass-split me-1"></i>导入中...';
                
                // 禁用所有其他按钮，防止用户在导入过程中进行其他操作
                document.getElementById('hostPrevStepBtn').disabled = true;
                document.getElementById('hostNextStepBtn').disabled = true;
                
                // 收集字段映射
                const fieldMappings = {};
                document.querySelectorAll('.host-field-mapping').forEach(select => {
                    if (select.value) {
                        fieldMappings[select.value] = select.dataset.field;
                    }
                });
                
                // 检查是否映射了主播名称
                if (!Object.values(fieldMappings).includes('name')) {
                    showAlert('hostAlert', '必须映射主播名称字段', 'warning');
                    // 恢复按钮状态
                    importBtn.disabled = false;
                    importBtn.innerHTML = '<i class="bi bi-check-circle me-1"></i>开始导入';
                    document.getElementById('hostPrevStepBtn').disabled = false;
                    document.getElementById('hostNextStepBtn').disabled = false;
                    return;
                }
                
                // 获取起始行和结束行
                const startRow = parseInt(document.getElementById('hostStartRow').value) || 1;
                let endRow = document.getElementById('hostEndRow').value ? parseInt(document.getElementById('hostEndRow').value) : null;
                
                // 创建导入配置
                const importConfig = {
                    field_mappings: fieldMappings,
                    start_row: startRow,
                    end_row: endRow
                };
                
                // 创建FormData
                const formData = new FormData();
                formData.append('file', hostExcelFile);
                
                // 发送请求
                const response = await fetch(`/api/presets/hosts/import-excel?config=${encodeURIComponent(JSON.stringify(importConfig))}`, {
                    method: 'POST',
                    body: formData
                });
                
                if (!response.ok) {
                    const errorData = await response.json();
                    throw new Error(errorData.detail || '导入Excel数据失败');
                }
                
                const data = await response.json();
                
                // 关闭模态框
                const modalElement = document.getElementById('hostExcelImportModal');
                const modalInstance = bootstrap.Modal.getInstance(modalElement);
                if (modalInstance) {
                    modalInstance.hide();
                } else {
                    hostExcelImportModal.hide();
                }
                
                // 显示成功消息
                showAlert('hostAlert', `Excel导入成功，成功导入${data.imported_count}条记录，跳过${data.skipped_count}条记录`, 'success');
                
                // 重新加载预设值
                await loadPresets();
                
                // 重置导入状态
                hostResetImport();
            } catch (error) {
                showAlert('hostAlert', error.message, 'danger');
            } finally {
                // 恢复按钮状态
                const importBtn = document.getElementById('hostStartImportBtn');
                importBtn.disabled = false;
                importBtn.innerHTML = '<i class="bi bi-check-circle me-1"></i>开始导入';
                document.getElementById('hostPrevStepBtn').disabled = false;
                document.getElementById('hostNextStepBtn').disabled = false;
            }
        }
        
        // 主播重置导入状态
        function hostResetImport() {
            hostCurrentStep = 1;
            hostExcelColumns = [];
            hostTotalExcelRows = 0;
            hostExcelFile = null;
            hostAllExcelData = [];
            hostCurrentPage = 1;
            document.getElementById('hostExcelFile').value = '';
            document.getElementById('hostSelectedFileName').textContent = '未选择文件';
            document.getElementById('hostUploadExcelBtn').disabled = true;
            hostGoToStep(1);
        }

        // 添加店铺复选框事件监听
        function addShopCheckboxListeners() {
            // 单个复选框事件
            const shopCheckboxes = document.querySelectorAll('.shop-checkbox');
            shopCheckboxes.forEach(checkbox => {
                checkbox.addEventListener('change', updateShopBatchDeleteButton);
            });
            
            // 全选复选框事件
            const selectAllShops = document.getElementById('selectAllShops');
            selectAllShops.addEventListener('change', function() {
                shopCheckboxes.forEach(checkbox => {
                    checkbox.checked = this.checked;
                });
                updateShopBatchDeleteButton();
            });
            
            // 批量删除按钮事件
            document.getElementById('batchDeleteShopsBtn').addEventListener('click', batchDeleteShops);
        }

        // 添加主播复选框事件监听
        function addHostCheckboxListeners() {
            // 单个复选框事件
            const hostCheckboxes = document.querySelectorAll('.host-checkbox');
            hostCheckboxes.forEach(checkbox => {
                checkbox.addEventListener('change', updateHostBatchDeleteButton);
            });
            
            // 全选复选框事件
            const selectAllHosts = document.getElementById('selectAllHosts');
            selectAllHosts.addEventListener('change', function() {
                hostCheckboxes.forEach(checkbox => {
                    checkbox.checked = this.checked;
                });
                updateHostBatchDeleteButton();
            });
            
            // 批量删除按钮事件
            document.getElementById('batchDeleteHostsBtn').addEventListener('click', batchDeleteHosts);
        }

        // 更新店铺批量删除按钮状态
        function updateShopBatchDeleteButton() {
            const checkedShops = document.querySelectorAll('.shop-checkbox:checked');
            const batchDeleteBtn = document.getElementById('batchDeleteShopsBtn');
            batchDeleteBtn.disabled = checkedShops.length === 0;
        }

        // 更新主播批量删除按钮状态
        function updateHostBatchDeleteButton() {
            const checkedHosts = document.querySelectorAll('.host-checkbox:checked');
            const batchDeleteBtn = document.getElementById('batchDeleteHostsBtn');
            batchDeleteBtn.disabled = checkedHosts.length === 0;
        }

        // 批量删除店铺
        async function batchDeleteShops() {
            const checkedShops = document.querySelectorAll('.shop-checkbox:checked');
            if (checkedShops.length === 0) return;
            
            const shopNames = Array.from(checkedShops).map(checkbox => checkbox.dataset.shopName);
            
            if (!confirm(`确定要删除选中的 ${shopNames.length} 个店铺吗？`)) return;
            
            try {
                const response = await fetch('/api/presets/shops/batch-delete', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ items: shopNames })
                });
                
                if (!response.ok) {
                    const errorData = await response.json();
                    throw new Error(errorData.detail || '批量删除店铺失败');
                }
                
                const data = await response.json();
                
                // 显示成功消息
                showAlert('shopAlert', data.message || '批量删除店铺成功', 'success');
                
                // 重新加载预设值
                loadPresets();
            } catch (error) {
                console.error('批量删除店铺出错:', error);
                showAlert('shopAlert', error.message || '批量删除店铺失败', 'danger');
            }
        }

        // 批量删除主播
        async function batchDeleteHosts() {
            const checkedHosts = document.querySelectorAll('.host-checkbox:checked');
            if (checkedHosts.length === 0) return;
            
            const hostNames = Array.from(checkedHosts).map(checkbox => checkbox.dataset.hostName);
            
            if (!confirm(`确定要删除选中的 ${hostNames.length} 个主播吗？`)) return;
            
            try {
                const response = await fetch('/api/presets/hosts/batch-delete', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ items: hostNames })
                });
                
                if (!response.ok) {
                    const errorData = await response.json();
                    throw new Error(errorData.detail || '批量删除主播失败');
                }
                
                const data = await response.json();
                
                // 显示成功消息
                showAlert('hostAlert', data.message || '批量删除主播成功', 'success');
                
                // 重置主播输入表单
                document.getElementById('hostName').value = '';
                
                // 完全重置表单，避免表单验证状态残留
                document.getElementById('addHostForm').reset();
                
                // 重新加载预设值
                loadPresets();
            } catch (error) {
                console.error('批量删除主播出错:', error);
                showAlert('hostAlert', error.message || '批量删除主播失败', 'danger');
            }
        }

        // 批量删除主播组
        async function batchDeleteHosts() {
            const checkedHosts = document.querySelectorAll('.host-checkbox:checked');
            if (checkedHosts.length === 0) return;
            
            const hostIds = Array.from(checkedHosts).map(checkbox => checkbox.dataset.hostId);
            
            if (!confirm(`确定要删除选中的 ${hostIds.length} 个主播组吗？`)) return;
            
            try {
                const response = await fetch('/api/presets/hosts/batch-delete', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ ids: hostIds })
                });
                
                if (!response.ok) {
                    const errorData = await response.json();
                    throw new Error(errorData.detail || '批量删除主播组失败');
                }
                
                const data = await response.json();
                
                // 显示成功消息
                showAlert('hostAlert', data.message || '批量删除主播组成功', 'success');
                
                // 重置表单
                document.getElementById('addHostForm').reset();
                
                // 重新加载预设值
                loadPresets();
            } catch (error) {
                console.error('批量删除主播组出错:', error);
                showAlert('hostAlert', error.message || '批量删除主播组失败', 'danger');
            }
        }

        // 渲染直播间列表
        function renderRooms(rooms) {
            const roomsList = document.getElementById('roomsList');
            roomsList.innerHTML = '';
            
            if (rooms.length === 0) {
                roomsList.innerHTML = '<tr><td colspan="4" class="text-center text-muted">暂无直播间</td></tr>';
                return;
            }
            
            rooms.forEach((room, index) => {
                const row = document.createElement('tr');
                row.innerHTML = `
                    <td><input type="checkbox" class="form-check-input room-checkbox" data-room-name="${room}"></td>
                    <td>${index + 1}</td>
                    <td>${room}</td>
                    <td>
                        <div class="preset-actions">
                            <button class="btn btn-sm btn-outline-primary" onclick="openEditModal('room', '${room}')">编辑</button>
                            <button class="btn btn-sm btn-outline-danger" onclick="deleteRoom('${room}')">删除</button>
                        </div>
                    </td>
                `;
                roomsList.appendChild(row);
            });
            
            // 添加复选框事件监听
            addRoomCheckboxListeners();
        }

        // 添加直播间复选框事件监听
        function addRoomCheckboxListeners() {
            // 单个复选框事件
            const roomCheckboxes = document.querySelectorAll('.room-checkbox');
            roomCheckboxes.forEach(checkbox => {
                checkbox.addEventListener('change', updateRoomBatchDeleteButton);
            });
            
            // 全选复选框事件
            const selectAllRooms = document.getElementById('selectAllRooms');
            selectAllRooms.addEventListener('change', function() {
                roomCheckboxes.forEach(checkbox => {
                    checkbox.checked = this.checked;
                });
                updateRoomBatchDeleteButton();
            });
            
            // 批量删除按钮事件
            document.getElementById('batchDeleteRoomsBtn').addEventListener('click', batchDeleteRooms);
        }

        // 更新直播间批量删除按钮状态
        function updateRoomBatchDeleteButton() {
            const checkedRooms = document.querySelectorAll('.room-checkbox:checked');
            const batchDeleteBtn = document.getElementById('batchDeleteRoomsBtn');
            batchDeleteBtn.disabled = checkedRooms.length === 0;
        }

        // 添加直播间
        async function addRoom() {
            const roomName = document.getElementById('roomName').value.trim();
            if (!roomName) return;
            
            try {
                const response = await fetch('/api/presets/rooms', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ name: roomName })
                });
                
                const data = await response.json();
                
                if (!response.ok) {
                    throw new Error(data.detail || '添加直播间失败');
                }
                
                // 清空输入框
                document.getElementById('roomName').value = '';
                
                // 显示成功消息
                showAlert('roomAlert', '直播间添加成功', 'success');
                
                // 重新加载预设值
                loadPresets();
            } catch (error) {
                showAlert('roomAlert', error.message, 'danger');
            }
        }

        // 删除直播间
        async function deleteRoom(roomName) {
            if (!confirm(`确定要删除直播间 "${roomName}" 吗？`)) return;
            
            try {
                const response = await fetch(`/api/presets/rooms/${encodeURIComponent(roomName)}`, {
                    method: 'DELETE'
                });
                
                const data = await response.json();
                
                if (!response.ok) {
                    throw new Error(data.detail || '删除直播间失败');
                }
                
                // 显示成功消息
                showAlert('roomAlert', '直播间删除成功', 'success');
                
                // 重新加载预设值
                loadPresets();
            } catch (error) {
                showAlert('roomAlert', error.message, 'danger');
            }
        }

        // 批量删除直播间
        async function batchDeleteRooms() {
            const checkedRooms = document.querySelectorAll('.room-checkbox:checked');
            if (checkedRooms.length === 0) return;
            
            const roomNames = Array.from(checkedRooms).map(checkbox => checkbox.dataset.roomName);
            
            if (!confirm(`确定要删除选中的 ${roomNames.length} 个直播间吗？`)) return;
            
            try {
                const response = await fetch('/api/presets/rooms/batch-delete', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ items: roomNames })
                });
                
                if (!response.ok) {
                    const errorData = await response.json();
                    throw new Error(errorData.detail || '批量删除直播间失败');
                }
                
                const data = await response.json();
                
                // 显示成功消息
                showAlert('roomAlert', data.message || '批量删除直播间成功', 'success');
                
                // 重新加载预设值
                loadPresets();
            } catch (error) {
                console.error('批量删除直播间出错:', error);
                showAlert('roomAlert', error.message || '批量删除直播间失败', 'danger');
            }
        }

        // 直播间上传Excel文件并预览
        async function roomUploadExcel() {
            try {
                if (!roomExcelFile) {
                    showAlert('roomAlert', '请选择Excel文件', 'warning');
                    return;
                }
                
                const formData = new FormData();
                formData.append('file', roomExcelFile);
                
                const response = await fetch('/api/presets/rooms/upload-excel', {
                    method: 'POST',
                    body: formData
                });
                
                if (!response.ok) {
                    const errorData = await response.json();
                    throw new Error(errorData.detail || '上传Excel文件失败');
                }
                
                const data = await response.json();
                
                // 保存列和数据
                roomExcelColumns = data.columns;
                roomTotalExcelRows = data.total_rows;
                roomAllExcelData = data.data;
                
                // 渲染预览
                roomRenderPreview(data);
                
                // 渲染字段映射
                roomRenderFieldMappings(data.columns);
                
                // 设置结束行默认值
                document.getElementById('roomEndRow').value = roomTotalExcelRows;
                
                // 进入下一步
                roomGoToStep(2);
            } catch (error) {
                showAlert('roomAlert', error.message, 'danger');
            }
        }

        // 直播间渲染预览数据
        function roomRenderPreview(data) {
            // 保存所有数据
            roomAllExcelData = data.data;
            roomTotalExcelRows = data.total_rows;
            
            // 设置总行数
            document.getElementById('roomTotalRows').textContent = roomTotalExcelRows;
            
            // 计算总页数
            const totalPages = Math.ceil(roomAllExcelData.length / roomPageSize);
            document.getElementById('roomTotalPages').textContent = totalPages;
            
            // 渲染表头
            const headerRow = document.getElementById('roomPreviewTableHeader');
            headerRow.innerHTML = '';
            data.columns.forEach(column => {
                const th = document.createElement('th');
                th.textContent = column;
                headerRow.appendChild(th);
            });
            
            // 渲染当前页数据
            roomRenderCurrentPage();
        }

        // 直播间渲染当前页数据
        function roomRenderCurrentPage() {
            const tableBody = document.getElementById('roomPreviewTableBody');
            tableBody.innerHTML = '';
            
            // 计算当前页的数据范围
            const startIndex = (roomCurrentPage - 1) * roomPageSize;
            const endIndex = Math.min(startIndex + roomPageSize, roomAllExcelData.length);
            
            // 更新当前页显示
            document.getElementById('roomCurrentPage').textContent = roomCurrentPage;
            
            // 更新分页按钮状态
            roomUpdatePaginationButtons();
            
            // 渲染当前页数据
            for (let i = startIndex; i < endIndex; i++) {
                const row = document.createElement('tr');
                roomAllExcelData[i].forEach(cell => {
                    const td = document.createElement('td');
                    td.textContent = cell === null ? '' : cell;
                    row.appendChild(td);
                });
                tableBody.appendChild(row);
            }
        }

        // 直播间更新分页按钮状态
        function roomUpdatePaginationButtons() {
            const totalPages = Math.ceil(roomAllExcelData.length / roomPageSize);
            document.getElementById('roomPrevPageBtn').disabled = roomCurrentPage <= 1;
            document.getElementById('roomNextPageBtn').disabled = roomCurrentPage >= totalPages;
        }

        // 直播间渲染字段映射
        function roomRenderFieldMappings(columns) {
            const mappingsContainer = document.getElementById('roomFieldMappings');
            mappingsContainer.innerHTML = '';
            
            // 只需要映射直播间名称字段
            const fields = [
                { id: 'name', label: '直播间名称' }
            ];
            
            fields.forEach(field => {
                const row = document.createElement('div');
                row.className = 'row mb-3 align-items-center';
                
                const labelCol = document.createElement('div');
                labelCol.className = 'col-md-3';
                labelCol.innerHTML = `<label class="form-label mb-0">${field.label}</label>`;
                
                const selectCol = document.createElement('div');
                selectCol.className = 'col-md-9';
                
                const select = document.createElement('select');
                select.className = 'form-select room-field-mapping';
                select.dataset.field = field.id;
                
                // 添加空选项
                const emptyOption = document.createElement('option');
                emptyOption.value = '';
                emptyOption.textContent = '-- 不导入 --';
                select.appendChild(emptyOption);
                
                // 添加Excel列选项
                columns.forEach(column => {
                    const option = document.createElement('option');
                    option.value = column;
                    option.textContent = column;
                    
                    // 尝试自动匹配列名
                    if (
                        column.toLowerCase().includes(field.label.toLowerCase()) ||
                        column.toLowerCase().includes('直播间') ||
                        column.toLowerCase().includes('名称')
                    ) {
                        option.selected = true;
                    }
                    
                    select.appendChild(option);
                });
                
                selectCol.appendChild(select);
                
                row.appendChild(labelCol);
                row.appendChild(selectCol);
                
                mappingsContainer.appendChild(row);
            });
        }

        // 直播间导入步骤控制
        function roomGoToStep(step) {
            // 隐藏所有步骤
            document.querySelectorAll('#roomExcelImportModal .import-step').forEach(el => {
                el.style.display = 'none';
            });
            
            // 重置步骤指示器
            document.querySelectorAll('#roomExcelImportModal [id^="roomStep"]').forEach(el => {
                if (el.id.endsWith('Indicator')) {
                    el.className = 'btn btn-outline-secondary btn-sm rounded-circle mb-2';
                }
            });
            
            // 显示当前步骤
            document.getElementById(`roomStep${step}`).style.display = 'block';
            document.getElementById(`roomStep${step}Indicator`).className = 'btn btn-primary btn-sm rounded-circle mb-2';
            
            // 更新进度条
            const progress = document.getElementById('roomImportProgress');
            progress.style.width = `${step * 33}%`;
            progress.setAttribute('aria-valuenow', step * 33);
            
            // 更新按钮
            const prevBtn = document.getElementById('roomPrevStepBtn');
            const nextBtn = document.getElementById('roomNextStepBtn');
            const importBtn = document.getElementById('roomStartImportBtn');
            
            prevBtn.style.display = step > 1 ? 'block' : 'none';
            nextBtn.style.display = step < 3 ? 'block' : 'none';
            importBtn.style.display = step === 3 ? 'block' : 'none';
            
            // 保存当前步骤
            roomCurrentStep = step;
        }

        // 直播间开始导入
        async function roomStartImport() {
            try {
                // 禁用导入按钮，防止重复点击
                const importBtn = document.getElementById('roomStartImportBtn');
                importBtn.disabled = true;
                importBtn.innerHTML = '<i class="bi bi-hourglass-split me-1"></i>导入中...';
                
                // 禁用所有其他按钮，防止用户在导入过程中进行其他操作
                document.getElementById('roomPrevStepBtn').disabled = true;
                document.getElementById('roomNextStepBtn').disabled = true;
                
                // 收集字段映射
                const fieldMappings = {};
                document.querySelectorAll('.room-field-mapping').forEach(select => {
                    if (select.value) {
                        fieldMappings[select.value] = select.dataset.field;
                    }
                });
                
                // 检查是否映射了直播间名称
                if (!Object.values(fieldMappings).includes('name')) {
                    showAlert('roomAlert', '必须映射直播间名称字段', 'warning');
                    // 恢复按钮状态
                    importBtn.disabled = false;
                    importBtn.innerHTML = '<i class="bi bi-check-circle me-1"></i>开始导入';
                    document.getElementById('roomPrevStepBtn').disabled = false;
                    document.getElementById('roomNextStepBtn').disabled = false;
                    return;
                }
                
                // 获取起始行和结束行
                const startRow = parseInt(document.getElementById('roomStartRow').value) || 1;
                let endRow = document.getElementById('roomEndRow').value ? parseInt(document.getElementById('roomEndRow').value) : null;
                
                // 创建导入配置
                const importConfig = {
                    field_mappings: fieldMappings,
                    start_row: startRow,
                    end_row: endRow
                };
                
                // 创建FormData
                const formData = new FormData();
                formData.append('file', roomExcelFile);
                
                // 发送请求
                const response = await fetch(`/api/presets/rooms/import-excel?config=${encodeURIComponent(JSON.stringify(importConfig))}`, {
                    method: 'POST',
                    body: formData
                });
                
                if (!response.ok) {
                    const errorData = await response.json();
                    throw new Error(errorData.detail || '导入Excel数据失败');
                }
                
                const data = await response.json();
                
                // 关闭模态框
                const modalElement = document.getElementById('roomExcelImportModal');
                const modalInstance = bootstrap.Modal.getInstance(modalElement);
                if (modalInstance) {
                    modalInstance.hide();
                } else {
                    roomExcelImportModal.hide();
                }
                
                // 显示成功消息
                showAlert('roomAlert', `Excel导入成功，成功导入${data.imported_count}条记录，跳过${data.skipped_count}条记录`, 'success');
                
                // 重新加载预设值
                await loadPresets();
                
                // 重置导入状态
                roomResetImport();
            } catch (error) {
                showAlert('roomAlert', error.message, 'danger');
            } finally {
                // 恢复按钮状态
                const importBtn = document.getElementById('roomStartImportBtn');
                importBtn.disabled = false;
                importBtn.innerHTML = '<i class="bi bi-check-circle me-1"></i>开始导入';
                document.getElementById('roomPrevStepBtn').disabled = false;
                document.getElementById('roomNextStepBtn').disabled = false;
            }
        }

        // 直播间重置导入状态
        function roomResetImport() {
            roomCurrentStep = 1;
            roomExcelColumns = [];
            roomTotalExcelRows = 0;
            roomExcelFile = null;
            roomAllExcelData = [];
            roomCurrentPage = 1;
            document.getElementById('roomExcelFile').value = '';
            document.getElementById('roomSelectedFileName').textContent = '未选择文件';
            document.getElementById('roomUploadExcelBtn').disabled = true;
            roomGoToStep(1);
        }

        // 计算成员人数
        function countMembers(membersStr) {
            if (!membersStr) return 0;
            return membersStr.split(',').filter(member => member.trim() !== '').length;
        }

        // 渲染销售组列表
        function renderSalesList(sales) {
            const salesList = document.getElementById('salesList');
            if (!salesList) return;
            
            salesList.innerHTML = '';
            
            if (!sales || sales.length === 0) {
                salesList.innerHTML = '<tr><td colspan="7" class="text-center py-4">暂无销售组数据</td></tr>';
                return;
            }
            
            sales.forEach((sale, index) => {
                // 计算成员人数
                const memberCount = countMembers(sale.members);
                
                const tr = document.createElement('tr');
                
                // 创建复选框单元格
                const checkboxCell = document.createElement('td');
                checkboxCell.className = 'checkbox-column';
                const checkbox = document.createElement('input');
                checkbox.type = 'checkbox';
                checkbox.className = 'form-check-input sales-checkbox';
                checkbox.dataset.id = sale.id;
                checkboxCell.appendChild(checkbox);
                
                // 创建序号单元格
                const indexCell = document.createElement('td');
                indexCell.className = 'narrow-column';
                indexCell.textContent = index + 1;
                
                // 创建分组单元格
                const groupCell = document.createElement('td');
                groupCell.textContent = sale.group;
                
                // 创建负责人单元格
                const leaderCell = document.createElement('td');
                leaderCell.textContent = sale.leader;
                
                // 创建成员单元格
                const membersCell = document.createElement('td');
                membersCell.textContent = sale.members;
                
                // 创建人数单元格
                const countCell = document.createElement('td');
                countCell.className = 'narrow-column';
                countCell.textContent = memberCount;
                
                // 创建操作单元格
                const actionsCell = document.createElement('td');
                actionsCell.className = 'actions-column';
                
                const actionsDiv = document.createElement('div');
                actionsDiv.className = 'd-flex justify-content-center gap-2';
                
                // 创建编辑按钮
                const editButton = document.createElement('button');
                editButton.type = 'button';
                editButton.className = 'btn btn-sm btn-outline-primary edit-sales-btn';
                editButton.dataset.id = sale.id;
                
                const editIcon = document.createElement('i');
                editIcon.className = 'bi bi-pencil';
                editButton.appendChild(editIcon);
                
                // 创建删除按钮
                const deleteButton = document.createElement('button');
                deleteButton.type = 'button';
                deleteButton.className = 'btn btn-sm btn-outline-danger delete-sales-btn';
                deleteButton.dataset.id = sale.id;
                
                const deleteIcon = document.createElement('i');
                deleteIcon.className = 'bi bi-trash';
                deleteButton.appendChild(deleteIcon);
                
                // 添加按钮到操作区域
                actionsDiv.appendChild(editButton);
                actionsDiv.appendChild(deleteButton);
                actionsCell.appendChild(actionsDiv);
                
                // 添加所有单元格到行
                tr.appendChild(checkboxCell);
                tr.appendChild(indexCell);
                tr.appendChild(groupCell);
                tr.appendChild(leaderCell);
                tr.appendChild(membersCell);
                tr.appendChild(countCell);
                tr.appendChild(actionsCell);
                
                // 添加行到表格
                salesList.appendChild(tr);
            });
            
            // 初始化编辑按钮事件
            const editSalesButtons = document.querySelectorAll('.edit-sales-btn');
            editSalesButtons.forEach(button => {
                button.addEventListener('click', function() {
                    const saleId = this.dataset.id;
                    const sale = sales.find(s => s.id === saleId);
                    
                    if (sale) {
                        openEditSalesModal(sale);
                    }
                });
            });
            
            // 初始化删除按钮事件
            const deleteSalesButtons = document.querySelectorAll('.delete-sales-btn');
            deleteSalesButtons.forEach(button => {
                button.addEventListener('click', async function() {
                    const saleId = this.dataset.id;
                    const sale = sales.find(s => s.id === saleId);
                    
                    if (!sale) return;
                    
                    if (!confirm(`确定要删除销售组 "${sale.group}" 吗？`)) {
                        return;
                    }
                    
                    try {
                        const response = await fetch(`/api/presets/sales/${saleId}`, {
                            method: 'DELETE'
                        });
                        
                        if (!response.ok) {
                            const data = await response.json();
                            throw new Error(data.detail || '删除销售组失败');
                        }
                        
                        // 显示成功消息
                        showAlert('salesAlert', '销售组删除成功', 'success');
                        
                        // 重新加载预设值
                        loadPresets();
                    } catch (error) {
                        showAlert('salesAlert', error.message, 'danger');
                    }
                });
            });
            
            // 初始化复选框事件
            const salesCheckboxes = document.querySelectorAll('.sales-checkbox');
            salesCheckboxes.forEach(checkbox => {
                checkbox.addEventListener('change', function() {
                    updateBatchDeleteButtonState('sales');
                });
            });
            
            // 初始化全选复选框
            const selectAllSales = document.getElementById('selectAllSales');
            if (selectAllSales) {
                selectAllSales.checked = false;
                selectAllSales.addEventListener('change', function() {
                    const isChecked = this.checked;
                    document.querySelectorAll('.sales-checkbox').forEach(checkbox => {
                        checkbox.checked = isChecked;
                    });
                    updateBatchDeleteButtonState('sales');
                });
            }
            
            // 初始化批量删除按钮
            const batchDeleteSalesBtn = document.getElementById('batchDeleteSalesBtn');
            if (batchDeleteSalesBtn) {
                batchDeleteSalesBtn.disabled = true;
                batchDeleteSalesBtn.addEventListener('click', async function() {
                    const selectedIds = Array.from(document.querySelectorAll('.sales-checkbox:checked')).map(checkbox => checkbox.dataset.id);
                    
                    if (selectedIds.length === 0) return;
                    
                    if (!confirm(`确定要删除选中的 ${selectedIds.length} 个销售组吗？`)) {
                        return;
                    }
                    
                    try {
                        const response = await fetch('/api/presets/sales/batch', {
                            method: 'DELETE',
                            headers: {
                                'Content-Type': 'application/json'
                            },
                            body: JSON.stringify({
                                ids: selectedIds
                            })
                        });
                        
                        if (!response.ok) {
                            const data = await response.json();
                            throw new Error(data.detail || '批量删除销售组失败');
                        }
                        
                        // 显示成功消息
                        showAlert('salesAlert', `成功删除 ${selectedIds.length} 个销售组`, 'success');
                        
                        // 重新加载预设值
                        loadPresets();
                    } catch (error) {
                        showAlert('salesAlert', error.message, 'danger');
                    }
                });
            }
        }

        // 打开编辑销售组模态框
        function openEditSalesModal(sale) {
            // 创建销售编辑模态框
            const modalHtml = `
                <div class="modal fade" id="editSalesModal" tabindex="-1" aria-hidden="true">
                    <div class="modal-dialog">
                        <div class="modal-content">
                            <div class="modal-header">
                                <h5 class="modal-title">编辑销售组</h5>
                                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                            </div>
                            <div class="modal-body">
                                <form id="editSalesForm">
                                    <input type="hidden" id="editSalesId" value="${sale.id}">
                                    <div class="mb-3">
                                        <label for="editSalesGroup" class="form-label">分组</label>
                                        <input type="text" class="form-control" id="editSalesGroup" value="${sale.group}" required>
                                    </div>
                                    <div class="mb-3">
                                        <label for="editSalesLeader" class="form-label">负责人</label>
                                        <input type="text" class="form-control" id="editSalesLeader" value="${sale.leader}" required>
                                    </div>
                                    <div class="mb-3">
                                        <label for="editSalesMembers" class="form-label">成员</label>
                                        <input type="text" class="form-control" id="editSalesMembers" value="${sale.members}" placeholder="多个成员用逗号分隔" required>
                                    </div>
                                    <div class="mb-3">
                                        <label for="editSalesMemberCount" class="form-label">人数</label>
                                        <input type="text" class="form-control" id="editSalesMemberCount" value="${countMembers(sale.members)}" readonly>
                                        <div class="form-text">根据成员自动计算</div>
                                    </div>
                                </form>
                            </div>
                            <div class="modal-footer">
                                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                                <button type="button" class="btn btn-primary" id="saveSalesBtn">保存</button>
                            </div>
                        </div>
                    </div>
                </div>
            `;
            
            // 添加模态框到页面
            document.body.insertAdjacentHTML('beforeend', modalHtml);
            
            // 显示模态框
            const editSalesModal = new bootstrap.Modal(document.getElementById('editSalesModal'));
            editSalesModal.show();
            
            // 监听成员输入框变化，自动计算人数
            document.getElementById('editSalesMembers').addEventListener('input', function() {
                const members = this.value;
                const count = countMembers(members);
                document.getElementById('editSalesMemberCount').value = count;
            });
            
            // 初始化保存按钮事件
            document.getElementById('saveSalesBtn').addEventListener('click', async function() {
                const saleId = document.getElementById('editSalesId').value;
                const group = document.getElementById('editSalesGroup').value.trim();
                const leader = document.getElementById('editSalesLeader').value.trim();
                const members = document.getElementById('editSalesMembers').value.trim();
                
                if (!group || !leader || !members) {
                    alert('请填写所有必填字段');
                    return;
                }
                
                try {
                    const response = await fetch(`/api/presets/sales/${saleId}`, {
                        method: 'PUT',
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify({
                            group: group,
                            leader: leader,
                            members: members
                        })
                    });
                    
                    if (!response.ok) {
                        const data = await response.json();
                        throw new Error(data.detail || '更新销售组失败');
                    }
                    
                    // 关闭模态框
                    editSalesModal.hide();
                    
                    // 移除模态框元素
                    document.getElementById('editSalesModal').remove();
                    
                    // 显示成功消息
                    showAlert('salesAlert', '销售组更新成功', 'success');
                    
                    // 重新加载预设值
                    loadPresets();
                } catch (error) {
                    showAlert('salesAlert', error.message, 'danger');
                }
            });
            
            // 模态框关闭时移除元素
            document.getElementById('editSalesModal').addEventListener('hidden.bs.modal', function() {
                this.remove();
            });
        }

        // 加载电商店铺预设值
        async function loadEcommerceStores() {
            try {
                const response = await fetch('/api/presets/ecommerce-stores');
                if (!response.ok) {
                    throw new Error('加载电商店铺预设值失败');
                }
                const data = await response.json();
                
                // 渲染电商店铺列表
                renderEcommerceStores(data);
            } catch (error) {
                showAlert('ecommerceAlert', error.message, 'danger');
            }
        }
        
        // 渲染电商店铺列表
        function renderEcommerceStores(stores) {
            const ecommerceList = document.getElementById('ecommerceList');
            ecommerceList.innerHTML = '';
            
            if (!stores || stores.length === 0) {
                ecommerceList.innerHTML = '<tr><td colspan="6" class="text-center text-muted">暂无电商店铺</td></tr>';
                return;
            }
            
            stores.forEach((store, index) => {
                // 计算店铺数量
                const storesArray = store.stores.split(',').filter(item => item.trim() !== '');
                const storeCount = storesArray.length;
                
                // 为store对象添加quantity属性
                store.quantity = storeCount;
                
                const row = document.createElement('tr');
                row.innerHTML = `
                    <td><input type="checkbox" class="form-check-input ecommerce-checkbox" data-store-id="${store.id}"></td>
                    <td>${index + 1}</td>
                    <td>${store.platform}</td>
                    <td>${store.stores}</td>
                    <td>${store.quantity}</td>
                    <td>
                        <div class="preset-actions">
                            <button class="btn btn-sm btn-outline-primary" onclick="openEditEcommerceModal('${store.id}', '${store.platform}', '${store.stores.replace(/'/g, "&#39;")}')">编辑</button>
                            <button class="btn btn-sm btn-outline-danger" onclick="deleteEcommerceStore('${store.id}')">删除</button>
                        </div>
                    </td>
                `;
                ecommerceList.appendChild(row);
            });
            
            // 添加复选框事件监听
            addEcommerceCheckboxListeners();
        }
        
        // 添加电商店铺复选框事件监听
        function addEcommerceCheckboxListeners() {
            document.querySelectorAll('.ecommerce-checkbox').forEach(checkbox => {
                checkbox.addEventListener('change', function() {
                    // 检查是否有选中的项
                    const hasChecked = document.querySelectorAll('.ecommerce-checkbox:checked').length > 0;
                    
                    // 启用或禁用批量删除按钮
                    document.getElementById('batchDeleteEcommerceBtn').disabled = !hasChecked;
                    
                    // 更新全选复选框状态
                    const allChecked = document.querySelectorAll('.ecommerce-checkbox:checked').length === document.querySelectorAll('.ecommerce-checkbox').length;
                    document.getElementById('selectAllEcommerce').checked = allChecked;
                });
            });
        }
        
        // 添加电商店铺
        async function addEcommerceStore() {
            const platform = document.getElementById('ecommercePlatform').value.trim();
            const stores = document.getElementById('ecommerceStores').value.trim();
            
            if (!platform || !stores) {
                showAlert('ecommerceAlert', '请填写所有必填字段', 'warning');
                return;
            }
            
            try {
                const response = await fetch('/api/presets/ecommerce-stores', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        platform: platform,
                        stores: stores
                    })
                });
                
                const data = await response.json();
                
                if (!response.ok) {
                    throw new Error(data.detail || '添加电商店铺失败');
                }
                
                // 清空输入框
                document.getElementById('ecommercePlatform').value = '';
                document.getElementById('ecommerceStores').value = '';
                
                // 显示成功消息
                showAlert('ecommerceAlert', '电商店铺添加成功', 'success');
                
                // 重新加载电商店铺预设值
                loadEcommerceStores();
            } catch (error) {
                showAlert('ecommerceAlert', error.message, 'danger');
            }
        }
        
        // 打开编辑电商店铺模态框
        function openEditEcommerceModal(id, platform, stores) {
            // 计算店铺数量
            const storesArray = stores.split(',').filter(item => item.trim() !== '');
            const storeCount = storesArray.length;
            
            // 创建电商店铺编辑模态框
            const modalHtml = `
                <div class="modal fade" id="editEcommerceModal" tabindex="-1" aria-hidden="true">
                    <div class="modal-dialog modal-lg">
                        <div class="modal-content">
                            <div class="modal-header">
                                <h5 class="modal-title">编辑电商店铺</h5>
                                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                            </div>
                            <div class="modal-body">
                                <form id="editEcommerceForm">
                                    <input type="hidden" id="editEcommerceId" value="${id}">
                                    <div class="mb-3">
                                        <label for="editEcommercePlatform" class="form-label">平台</label>
                                        <input type="text" class="form-control" id="editEcommercePlatform" value="${platform}" required>
                                    </div>
                                    <div class="mb-3">
                                        <label for="editEcommerceStores" class="form-label">店铺</label>
                                        <input type="text" class="form-control" id="editEcommerceStores" value="${stores}" required>
                                        <div class="form-text text-muted">多个店铺名称使用逗号分隔</div>
                                    </div>
                                    <div class="mb-3">
                                        <label for="editEcommerceCount" class="form-label">数量</label>
                                        <input type="text" class="form-control" id="editEcommerceCount" value="${storeCount}" readonly>
                                        <div class="form-text text-muted">根据店铺数据自动计算</div>
                                    </div>
                                </form>
                            </div>
                            <div class="modal-footer">
                                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                                <button type="button" class="btn btn-primary" id="saveEcommerceBtn">保存</button>
                            </div>
                        </div>
                    </div>
                </div>
            `;
            
            // 添加模态框到页面
            document.body.insertAdjacentHTML('beforeend', modalHtml);
            
            // 显示模态框
            const editEcommerceModal = new bootstrap.Modal(document.getElementById('editEcommerceModal'));
            editEcommerceModal.show();
            
            // 监听店铺输入框变化，实时更新数量
            document.getElementById('editEcommerceStores').addEventListener('input', function() {
                const stores = this.value;
                const storesArray = stores.split(',').filter(item => item.trim() !== '');
                const storeCount = storesArray.length;
                document.getElementById('editEcommerceCount').value = storeCount;
            });
            
            // 初始化保存按钮事件
            document.getElementById('saveEcommerceBtn').addEventListener('click', async function() {
                const id = document.getElementById('editEcommerceId').value;
                const platform = document.getElementById('editEcommercePlatform').value.trim();
                const stores = document.getElementById('editEcommerceStores').value.trim();
                
                if (!platform || !stores) {
                    showAlert('ecommerceAlert', '请填写所有必填字段', 'warning');
                    return;
                }
                
                try {
                    const response = await fetch('/api/presets/ecommerce-stores', {
                        method: 'PUT',
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify({
                            id: id,
                            platform: platform,
                            stores: stores
                        })
                    });
                    
                    const data = await response.json();
                    
                    if (!response.ok) {
                        throw new Error(data.detail || '更新电商店铺失败');
                    }
                    
                    // 关闭模态框
                    editEcommerceModal.hide();
                    
                    // 移除模态框元素
                    document.getElementById('editEcommerceModal').remove();
                    
                    // 显示成功消息
                    showAlert('ecommerceAlert', '电商店铺更新成功', 'success');
                    
                    // 重新加载电商店铺预设值
                    loadEcommerceStores();
                } catch (error) {
                    showAlert('ecommerceAlert', error.message, 'danger');
                }
            });
            
            // 模态框关闭时移除元素
            document.getElementById('editEcommerceModal').addEventListener('hidden.bs.modal', function() {
                this.remove();
            });
        }
        
        // 更新电商店铺（旧方法，保留以便参考）
        async function updateEcommerceStore() {
            const id = document.getElementById('editEcommerceId').value;
            const platform = document.getElementById('editEcommercePlatform').value.trim();
            const stores = document.getElementById('editEcommerceStores').value.trim();
            
            if (!platform || !stores) {
                showAlert('ecommerceAlert', '请填写所有必填字段', 'warning');
                return;
            }
            
            try {
                const response = await fetch('/api/presets/ecommerce-stores', {
                    method: 'PUT',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        id: id,
                        platform: platform,
                        stores: stores
                    })
                });
                
                const data = await response.json();
                
                if (!response.ok) {
                    throw new Error(data.detail || '更新电商店铺失败');
                }
                
                // 关闭模态框
                editModal.hide();
                
                // 显示成功消息
                showAlert('ecommerceAlert', '电商店铺更新成功', 'success');
                
                // 重新加载电商店铺预设值
                loadEcommerceStores();
            } catch (error) {
                showAlert('ecommerceAlert', error.message, 'danger');
                editModal.hide();
            }
        }
        
        // 删除电商店铺
        async function deleteEcommerceStore(id) {
            if (!confirm('确定要删除该电商店铺吗？')) return;
            
            try {
                const response = await fetch(`/api/presets/ecommerce-stores/${id}`, {
                    method: 'DELETE'
                });
                
                const data = await response.json();
                
                if (!response.ok) {
                    throw new Error(data.detail || '删除电商店铺失败');
                }
                
                // 显示成功消息
                showAlert('ecommerceAlert', '电商店铺删除成功', 'success');
                
                // 重新加载电商店铺预设值
                loadEcommerceStores();
            } catch (error) {
                showAlert('ecommerceAlert', error.message, 'danger');
            }
        }
        
        // 批量删除电商店铺
        async function batchDeleteEcommerceStores() {
            const checkedStores = document.querySelectorAll('.ecommerce-checkbox:checked');
            if (checkedStores.length === 0) return;
            
            const storeIds = Array.from(checkedStores).map(checkbox => checkbox.dataset.storeId);
            
            if (!confirm(`确定要删除选中的 ${storeIds.length} 个电商店铺吗？`)) return;
            
            try {
                const response = await fetch('/api/presets/ecommerce-stores/batch-delete', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ items: storeIds })
                });
                
                if (!response.ok) {
                    const errorData = await response.json();
                    throw new Error(errorData.detail || '批量删除电商店铺失败');
                }
                
                const data = await response.json();
                
                // 显示成功消息
                showAlert('ecommerceAlert', data.message || '批量删除电商店铺成功', 'success');
                
                // 重新加载电商店铺预设值
                loadEcommerceStores();
            } catch (error) {
                console.error('批量删除电商店铺出错:', error);
                showAlert('ecommerceAlert', error.message || '批量删除电商店铺失败', 'danger');
            }
        }
        
        // 电商店铺导入步骤控制
        function ecommerceGoToStep(step) {
            if (step < 1 || step > 3) return;
            
            ecommerceCurrentStep = step;
            
            // 隐藏所有步骤
            document.querySelectorAll('#ecommerceExcelImportModal .import-step').forEach(el => {
                el.style.display = 'none';
            });
            
            // 显示当前步骤
            document.getElementById(`ecommerceStep${step}`).style.display = 'block';
            
            // 更新进度条
            const progressBar = document.getElementById('ecommerceImportProgressBar');
            progressBar.style.width = `${step * 33.33}%`;
            progressBar.setAttribute('aria-valuenow', step * 33.33);
            
            // 更新步骤样式
            document.querySelectorAll('#ecommerceExcelImportModal .step').forEach((el, index) => {
                if (index + 1 <= step) {
                    el.classList.add('active');
                } else {
                    el.classList.remove('active');
                }
            });
            
            // 控制按钮显示
            const prevBtn = document.getElementById('ecommercePrevStepBtn');
            const nextBtn = document.getElementById('ecommerceNextStepBtn');
            const importBtn = document.getElementById('ecommerceStartImportBtn');
            
            prevBtn.style.display = step > 1 ? 'inline-block' : 'none';
            nextBtn.style.display = step < 3 ? 'inline-block' : 'none';
            importBtn.style.display = step === 3 ? 'inline-block' : 'none';
        }
        
        // 电商店铺导入上一步
        function ecommercePrevStep() {
            if (ecommerceCurrentStep > 1) {
                ecommerceGoToStep(ecommerceCurrentStep - 1);
            }
        }
        
        // 电商店铺导入下一步
        function ecommerceNextStep() {
            if (ecommerceCurrentStep < 3) {
                ecommerceGoToStep(ecommerceCurrentStep + 1);
            }
        }
        
        // 上传电商店铺Excel文件并解析
        async function ecommerceUploadExcel() {
            if (!ecommerceExcelFile) return;
            
            try {
                // 使用FileReader读取文件
                const reader = new FileReader();
                
                reader.onload = function(e) {
                    try {
                        // 解析Excel数据
                        const data = new Uint8Array(e.target.result);
                        const workbook = XLSX.read(data, { type: 'array' });
                        
                        // 获取第一个工作表
                        const firstSheetName = workbook.SheetNames[0];
                        const worksheet = workbook.Sheets[firstSheetName];
                        
                        // 转换为JSON
                        const jsonData = XLSX.utils.sheet_to_json(worksheet, { header: 1, defval: '' });
                        
                        // 保存数据
                        ecommerceAllExcelData = jsonData;
                        ecommerceTotalExcelRows = jsonData.length;
                        
                        // 设置列名
                        if (jsonData.length > 0) {
                            ecommerceExcelColumns = jsonData[0].map((col, index) => ({
                                index: index,
                                name: col || `列 ${index + 1}`
                            }));
                        }
                        
                        // 渲染预览表格
                        ecommerceRenderPreviewTable(jsonData);
                        
                        // 渲染字段映射选项
                        ecommerceRenderFieldMappings();
                        
                        // 设置总行数
                        document.getElementById('ecommerceTotalRows').textContent = ecommerceTotalExcelRows;
                        document.getElementById('ecommerceEndRow').value = ecommerceTotalExcelRows;
                        
                        // 前往步骤2
                        ecommerceGoToStep(2);
                    } catch (error) {
                        console.error('解析Excel文件失败:', error);
                        showAlert('ecommerceAlert', '解析Excel文件失败', 'danger');
                    }
                };
                
                reader.onerror = function() {
                    showAlert('ecommerceAlert', '读取Excel文件失败', 'danger');
                };
                
                reader.readAsArrayBuffer(ecommerceExcelFile);
            } catch (error) {
                showAlert('ecommerceAlert', error.message, 'danger');
            }
        }
        
        // 渲染电商店铺Excel预览表格
        function ecommerceRenderPreviewTable(data) {
            if (!data || data.length === 0) return;
            
            const headerRow = data[0];
            const tableHeader = document.getElementById('ecommercePreviewTableHeader');
            const tableBody = document.getElementById('ecommercePreviewTableBody');
            
            // 清空表格
            tableHeader.innerHTML = '';
            tableBody.innerHTML = '';
            
            // 添加表头
            headerRow.forEach(col => {
                const th = document.createElement('th');
                th.textContent = col || '';
                tableHeader.appendChild(th);
            });
            
            // 计算分页
            const startIndex = Math.max(1, (ecommerceCurrentPage - 1) * ecommercePageSize);
            const endIndex = Math.min(data.length, startIndex + ecommercePageSize);
            
            // 添加数据行
            for (let i = startIndex; i < endIndex; i++) {
                const row = document.createElement('tr');
                data[i].forEach(cell => {
                    const td = document.createElement('td');
                    td.textContent = cell;
                    row.appendChild(td);
                });
                tableBody.appendChild(row);
            }
            
            // 更新分页信息
            document.getElementById('ecommercePageInfo').textContent = `第 ${ecommerceCurrentPage} 页`;
            
            // 更新翻页按钮状态
            document.getElementById('ecommercePrevPageBtn').disabled = ecommerceCurrentPage <= 1;
            document.getElementById('ecommerceNextPageBtn').disabled = endIndex >= data.length;
        }
        
        // 渲染电商店铺字段映射选项
        function ecommerceRenderFieldMappings() {
            const fieldMappings = document.getElementById('ecommerceFieldMappings');
            fieldMappings.innerHTML = '';
            
            // 电商店铺字段定义
            const fields = [
                { id: 'platform', name: '平台', required: true },
                { id: 'stores', name: '店铺', required: true }
            ];
            
            fields.forEach(field => {
                const row = document.createElement('div');
                row.className = 'mb-3 row align-items-center';
                
                const label = document.createElement('label');
                label.className = 'col-sm-3 col-form-label';
                label.textContent = field.name + (field.required ? ' *' : '');
                
                const selectCol = document.createElement('div');
                selectCol.className = 'col-sm-9';
                
                const select = document.createElement('select');
                select.className = 'form-select field-mapping';
                select.dataset.field = field.id;
                
                // 添加空选项
                const emptyOption = document.createElement('option');
                emptyOption.value = '';
                emptyOption.textContent = '-- 选择列 --';
                select.appendChild(emptyOption);
                
                // 添加列选项
                ecommerceExcelColumns.forEach(column => {
                    const option = document.createElement('option');
                    option.value = column.index;
                    option.textContent = column.name;
                    
                    // 尝试智能匹配
                    if (column.name.toLowerCase().includes(field.id.toLowerCase())) {
                        option.selected = true;
                    }
                    
                    select.appendChild(option);
                });
                
                selectCol.appendChild(select);
                row.appendChild(label);
                row.appendChild(selectCol);
                fieldMappings.appendChild(row);
            });
        }
        
        // 开始导入电商店铺Excel数据
        async function startEcommerceImport() {
            try {
                // 禁用按钮，避免重复点击
                const importBtn = document.getElementById('ecommerceStartImportBtn');
                importBtn.disabled = true;
                importBtn.innerHTML = '<i class="bi bi-hourglass-split me-1"></i>导入中...';
                document.getElementById('ecommercePrevStepBtn').disabled = true;
                
                // 收集字段映射
                const fieldMappings = {};
                document.querySelectorAll('#ecommerceFieldMappings .field-mapping').forEach(select => {
                    if (select.value) {
                        fieldMappings[select.value] = select.dataset.field;
                    }
                });
                
                // 检查是否映射了必填字段
                if (!Object.values(fieldMappings).includes('platform') || !Object.values(fieldMappings).includes('stores')) {
                    showAlert('ecommerceAlert', '必须映射平台和店铺字段', 'warning');
                    
                    // 恢复按钮状态
                    importBtn.disabled = false;
                    importBtn.innerHTML = '<i class="bi bi-check-circle me-1"></i>开始导入';
                    document.getElementById('ecommercePrevStepBtn').disabled = false;
                    
                    return;
                }
                
                // 获取起始行和结束行
                const startRow = parseInt(document.getElementById('ecommerceStartRow').value) || 1;
                let endRow = document.getElementById('ecommerceEndRow').value ? parseInt(document.getElementById('ecommerceEndRow').value) : null;
                
                // 准备导入数据
                const importData = [];
                
                // 从Excel数据中提取需要导入的数据
                for (let i = startRow; i < (endRow ? endRow + 1 : ecommerceAllExcelData.length); i++) {
                    if (i >= ecommerceAllExcelData.length) break;
                    
                    const rowData = ecommerceAllExcelData[i];
                    const importItem = {};
                    
                    // 根据字段映射设置值
                    Object.entries(fieldMappings).forEach(([columnIndex, fieldId]) => {
                        importItem[fieldId] = rowData[parseInt(columnIndex)] || '';
                    });
                    
                    // 检查必填字段
                    if (importItem.platform && importItem.stores) {
                        importData.push(importItem);
                    }
                }
                
                // 批量导入数据
                for (const item of importData) {
                    await fetch('/api/presets/ecommerce-stores', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify(item)
                    });
                }
                
                // 关闭模态框
                ecommerceExcelImportModal.hide();
                
                // 显示成功消息
                showAlert('ecommerceAlert', `Excel导入成功，成功导入${importData.length}条记录`, 'success');
                
                // 重新加载电商店铺预设值
                loadEcommerceStores();
                
                // 重置导入状态
                ecommerceResetImport();
            } catch (error) {
                showAlert('ecommerceAlert', error.message, 'danger');
            } finally {
                // 恢复按钮状态
                const importBtn = document.getElementById('ecommerceStartImportBtn');
                importBtn.disabled = false;
                importBtn.innerHTML = '<i class="bi bi-check-circle me-1"></i>开始导入';
                document.getElementById('ecommercePrevStepBtn').disabled = false;
                document.getElementById('ecommerceNextStepBtn').disabled = false;
            }
        }
        
        // 重置电商店铺Excel导入状态
        function ecommerceResetImport() {
            ecommerceCurrentStep = 1;
            ecommerceExcelColumns = [];
            ecommerceTotalExcelRows = 0;
            ecommerceExcelFile = null;
            ecommerceAllExcelData = [];
            ecommerceCurrentPage = 1;
            document.getElementById('ecommerceExcelFile').value = '';
            document.getElementById('selectedEcommerceFileName').textContent = '';
            ecommerceGoToStep(1);
        }

        // 打开编辑主播组模态框
        function openEditHostModal(hostId) {
            // 获取当前主播组数据
            fetch('/api/presets/hosts')
                .then(response => response.json())
                .then(data => {
                    const hosts = data.hosts || [];
                    const hostItem = hosts.find(h => h.id === hostId);
                    
                    if (!hostItem) {
                        showAlert('hostAlert', '未找到主播组数据', 'danger');
                        return;
                    }
                    
                    // 设置模态框标题
                    document.getElementById('editHostModalLabel').textContent = `编辑主播组: ${hostItem.group}`;
                    
                    // 创建编辑表单
                    const modalBody = document.getElementById('editHostModalBody');
                    modalBody.innerHTML = `
                        <div id="editHostModalAlert" class="alert d-none mb-3" role="alert"></div>
                        <div class="mb-3">
                            <label for="editHostLeader" class="form-label">负责人</label>
                            <input type="text" class="form-control" id="editHostLeader" value="${hostItem.leader}" required>
                        </div>
                        <div class="mb-3">
                            <label for="editHostGroup" class="form-label">分组</label>
                            <input type="text" class="form-control" id="editHostGroup" value="${hostItem.group}" required>
                        </div>
                        <div class="mb-3">
                            <label for="editHostNames" class="form-label">主播名称（用英文逗号分隔）</label>
                            <input type="text" class="form-control" id="editHostNames" value="${hostItem.hosts}" required>
                        </div>
                    `;
                    
                    // 设置保存按钮事件
                    document.getElementById('saveEditedHostBtn').onclick = () => saveEditedHost(hostId);
                    
                    // 显示模态框
                    const editHostModal = new bootstrap.Modal(document.getElementById('editHostModal'));
                    editHostModal.show();
                })
                .catch(error => {
                    console.error('获取主播组数据出错:', error);
                    showAlert('hostAlert', '获取主播组数据失败', 'danger');
                });
        }

        // 保存编辑后的主播组
        async function saveEditedHost(hostId) {
            const leader = document.getElementById('editHostLeader').value.trim();
            const group = document.getElementById('editHostGroup').value.trim();
            const hosts = document.getElementById('editHostNames').value.trim();
            
            if (!leader || !group || !hosts) {
                // 在模态框内显示错误信息
                const alertEl = document.getElementById('editHostModalAlert');
                alertEl.textContent = '所有字段都是必填的';
                alertEl.className = 'alert alert-warning mb-3';
                alertEl.classList.remove('d-none');
                return;
            }
            
            try {
                const response = await fetch('/api/presets/hosts', {
                    method: 'PUT',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        id: hostId,
                        leader: leader,
                        group: group,
                        hosts: hosts
                    })
                });
                
                const data = await response.json();
                
                if (!response.ok) {
                    throw new Error(data.detail || '更新主播组失败');
                }
                
                // 关闭模态框
                const editHostModal = bootstrap.Modal.getInstance(document.getElementById('editHostModal'));
                editHostModal.hide();
                
                // 显示成功消息
                showAlert('hostAlert', '主播组更新成功', 'success');
                
                // 重新加载预设值
                loadPresets();
            } catch (error) {
                // 在模态框内显示错误信息
                const alertEl = document.getElementById('editHostModalAlert');
                alertEl.textContent = error.message;
                alertEl.className = 'alert alert-danger mb-3';
                alertEl.classList.remove('d-none');
            }
        }
    </script>
</body>
</html>