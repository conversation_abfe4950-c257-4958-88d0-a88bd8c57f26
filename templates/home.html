<!DOCTYPE html>
<html lang="zh">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Lead Distribution</title>
    <link href="/static/css/element-plus.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        body {
            margin: 0;
            padding: 0;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
            background: #ffffff;
        }

        .home {
            position: relative;
            min-height: 100vh;
            display: flex;
            flex-direction: column;
            align-items: center;
            padding: 40px 20px;
        }

        .app-title {
            text-align: center;
            margin-bottom: 60px;
            margin-top: 40px;
        }

        .app-title h1 {
            font-size: 42px;
            font-weight: 600;
            color: #303133;
            margin-bottom: 16px;
            letter-spacing: 1px;
        }

        .app-title p {
            font-size: 18px;
            color: #606266;
            max-width: 600px;
        }

        .button-container {
            display: flex;
            flex-wrap: wrap;
            justify-content: center;
            gap: 30px;
            max-width: 1400px;
            margin: 0 auto;
        }

        .feature-card {
            width: 280px;
            height: 260px;
            background: white;
            border-radius: 16px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.08);
            overflow: hidden;
            transition: all 0.4s ease;
            cursor: pointer;
            display: flex;
            flex-direction: column;
            position: relative;
            margin: 0 10px;
        }

        .feature-card:hover {
            transform: translateY(-10px);
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.12);
        }

        .card-icon {
            height: 120px;
            display: flex;
            align-items: center;
            justify-content: center;
            background: linear-gradient(145deg, #409EFF, #3a8ee6);
            color: white;
        }

        .user-review-icon {
            background: linear-gradient(145deg, #409EFF, #3a8ee6);
        }

        .user-management-icon {
            background: linear-gradient(145deg, #67C23A, #5daf34);
        }

        .form-preset-icon {
            background: linear-gradient(145deg, #E6A23C, #d49531);
        }

        .schedule-icon {
            background: linear-gradient(145deg, #F56C6C, #e06363);
        }

        .lead-monitor-icon {
            background: linear-gradient(145deg, #9B59B6, #8E44AD);
        }

        .data-format-icon {
            background: linear-gradient(145deg, #3498DB, #2980B9);
        }

        .lead-distribution-icon {
            background: linear-gradient(145deg, #1ABC9C, #16A085);
        }

        .data-analysis-icon {
            background: linear-gradient(145deg, #34495E, #2C3E50);
        }

        .distribution-plan-icon {
            background: linear-gradient(145deg, #FF9800, #F57C00);
        }

        .card-icon i {
            font-size: 60px;
        }

        .card-content {
            padding: 24px;
            flex: 1;
            display: flex;
            flex-direction: column;
            justify-content: center;
            text-align: center;
        }

        .card-content h2 {
            font-size: 24px;
            font-weight: 600;
            color: #303133;
            margin-bottom: 12px;
        }

        .card-content p {
            font-size: 16px;
            color: #606266;
            line-height: 1.5;
        }

        /* 用户头像样式 */
        .user-avatar {
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 1000;
        }

        .avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: linear-gradient(145deg, #3a8ee6, #409EFF);
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 16px;
            cursor: pointer;
            user-select: none;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
        }

        .avatar:hover {
            transform: scale(1.05);
            box-shadow: 0 6px 12px rgba(0, 0, 0, 0.15);
        }

        .dropdown {
            position: relative;
            display: inline-block;
        }

        .dropdown-content {
            display: none;
            position: absolute;
            right: 0;
            background-color: #fff;
            min-width: 160px;
            box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
            border-radius: 4px;
            z-index: 1;
            margin-top: 5px;
        }

        .dropdown:hover .dropdown-content {
            display: block;
        }

        .user-info-item {
            padding: 10px 16px;
            line-height: 1.5;
            white-space: nowrap;
            color: #606266;
            border-bottom: 1px solid #ebeef5;
        }

        .logout-button {
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 10px 16px;
            color: #f56c6c;
            cursor: pointer;
            transition: background-color 0.3s;
        }

        .logout-button:hover {
            background-color: #f8f8f8;
        }

        .logout-icon {
            margin-right: 5px;
        }
    </style>
</head>

<body>
    <div id="app" class="home">
        {% if user_info %}
        <div class="user-avatar">
            <div class="dropdown">
                <div class="avatar">
                    {{ user_info.name[-1] }}
                </div>
                <div class="dropdown-content">
                    <div class="user-info-item">ID：{{ user_info.id }}</div>
                    <div class="user-info-item">姓名：{{ user_info.name }}</div>
                    <div class="user-info-item">部门：{{ user_info.department }}</div>
                    <div class="user-info-item">身份：{{ user_info.role }}</div>
                    <div class="logout-button" onclick="handleLogout()">
                        <i class="fas fa-sign-out-alt logout-icon"></i>
                        退出登录
                    </div>
                </div>
            </div>
        </div>
        {% endif %}

        <div class="app-title">
            <h1>线索管理后端系统</h1>
            <p>高效管理和分发您的业务线索</p>
        </div>

        <div class="button-container">
            <div class="feature-card" onclick="window.location.href='/user-review'">
                <div class="card-icon user-review-icon">
                    <i class="fas fa-user-check"></i>
                </div>
                <div class="card-content">
                    <h2>用户审核</h2>
                    <p>审核新用户注册申请</p>
                </div>
            </div>

            <div class="feature-card" onclick="window.location.href='/user-management'">
                <div class="card-icon user-management-icon">
                    <i class="fas fa-users-cog"></i>
                </div>
                <div class="card-content">
                    <h2>用户管理</h2>
                    <p>管理系统用户权限和信息</p>
                </div>
            </div>

            <div class="feature-card" onclick="window.location.href='/form-preset'">
                <div class="card-icon form-preset-icon">
                    <i class="fas fa-file-alt"></i>
                </div>
                <div class="card-content">
                    <h2>表单预设</h2>
                    <p>配置线索表单字段和选项</p>
                </div>
            </div>

            <div class="feature-card" onclick="window.location.href='/schedule-management'">
                <div class="card-icon schedule-icon">
                    <i class="fas fa-calendar-alt"></i>
                </div>
                <div class="card-content">
                    <h2>主播排班</h2>
                    <p>创建管理主播排班计划</p>
                </div>
            </div>

            <div class="feature-card" onclick="window.location.href='/distribution_rules'">
                <div class="card-icon lead-distribution-icon">
                    <i class="fas fa-random"></i>
                </div>
                <div class="card-content">
                    <h2>销售排班</h2>
                    <p>创建管理销售排班计划</p>
                </div>
            </div>

            <div class="feature-card" onclick="window.location.href='/distribution-plan'">
                <div class="card-icon distribution-plan-icon">
                    <i class="fas fa-tasks"></i>
                </div>
                <div class="card-content">
                    <h2>分发计划</h2>
                    <p>规划和安排线索分发策略</p>
                </div>
            </div>

            <div class="feature-card" onclick="window.location.href='/data-analysis'">
                <div class="card-icon data-analysis-icon">
                    <i class="fas fa-chart-pie"></i>
                </div>
                <div class="card-content">
                    <h2>数据分析</h2>
                    <p>分析线索数据和转化情况</p>
                </div>
            </div>
        </div>
    </div>
    <script src="/static/js/fetch.js"></script>
    <script src="/static/js/vue.global.js"></script>
    <script src="/static/js/element-plus.js"></script>
    <script>
        function handleLogout() {
            // 清除cookie
            document.cookie = "token=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;";
            // 重定向到登录页
            window.location.href = '/';
        }
        // 实现弹窗提示无权限访问
        document.addEventListener('DOMContentLoaded', function() {
            // 检查是否有权限被拒绝的标志(不能删)
            const permissionDenied = {{ 'true' if permission_denied else 'false' }};

            const permissionMessage = "{{ permission_message }}";
            
            if (permissionDenied) {
                ElementPlus.ElMessage.warning({
                    message: permissionMessage,
                    duration: 2000,
                    showClose: true
                });
            }
        });
    </script>
    <script type="module" src="/static/main.js"></script>

</html>