<!DOCTYPE html>
<html lang="zh">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>登录 - Lead Distribution</title>
    <link href="/static/css/element-plus.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        body {
            margin: 0;
            height: 100vh;
            display: flex;
            justify-content: center;
            align-items: center;
            background: #ffffff;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
        }

        .login-container {
            width: 480px;
            padding: 30px;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
        }

        .login-header {
            text-align: center;
            margin-bottom: 30px;
            padding: 20px;
            border-bottom: 1px solid #ebeef5;
        }

        .login-header h2 {
            margin: 0;
            color: #303133;
            font-size: 24px;
        }

        .form-item {
            margin-bottom: 25px;
            position: relative;
        }

        .form-item input {
            width: 100%;
            height: 42px;
            line-height: 42px;
            padding: 0 15px 0 35px;
            border: 1px solid #dcdfe6;
            border-radius: 4px;
            box-sizing: border-box;
            font-size: 14px;
        }

        .form-item input:focus {
            outline: none;
            border-color: #409eff;
        }

        .form-item i {
            position: absolute;
            left: 10px;
            top: 14px;
            color: #909399;
        }

        .login-button {
            width: 100%;
            height: 42px;
            background-color: #409eff;
            border: none;
            border-radius: 4px;
            color: white;
            font-size: 16px;
            cursor: pointer;
            transition: background-color 0.3s;
        }

        .login-button:hover {
            background-color: #66b1ff;
        }

        .register-link {
            text-align: center;
            margin-top: 15px;
        }

        .register-link a {
            color: #409eff;
            text-decoration: none;
            font-size: 14px;
        }

        .register-link a:hover {
            color: #66b1ff;
        }

        .captcha-container {
            display: flex;
            gap: 15px;
            margin-bottom: 20px;
        }

        .captcha-input {
            flex: 1;
            position: relative;
        }

        .captcha-input input {
            width: 100%;
            height: 42px;
            padding: 0 15px;
            border: 1px solid #dcdfe6;
            border-radius: 4px;
            box-sizing: border-box;
            font-size: 14px;
            transition: border-color 0.3s;
        }

        .captcha-input input:focus {
            outline: none;
            border-color: #409eff;
        }

        .captcha-image {
            width: 120px;
            height: 42px;
            display: flex;
            justify-content: center;
            align-items: center;
            cursor: pointer;
            user-select: none;
            border-radius: 4px;
            overflow: hidden;
            background: #f5f7fa;
            transition: opacity 0.3s;
        }

        .captcha-image:hover {
            opacity: 0.8;
        }
    </style>
</head>

<body>
    <!-- 引入Loading组件 -->
    {% include 'components/loading.html' %}

    <div id="login-error" style="color: red; margin-top: 10px; display: none;"></div>
    <div class="login-container">
        <div class="login-header">
            <h2>登录</h2>
        </div>
        <form id="loginForm">
            <div class="form-item">
                <i class="fas fa-user"></i>
                <input type="text" id="username" placeholder="请输入账号" required>
            </div>
            <div class="form-item">
                <i class="fas fa-lock"></i>
                <input type="password" id="password" placeholder="请输入密码" required>
            </div>
            <div class="form-item captcha-container">
                <div class="captcha-input">
                    <input type="text" id="captcha" placeholder="请输入验证码" required>
                </div>
                <div class="captcha-image" id="captchaImage"></div>
            </div>
            <button type="submit" class="login-button">登录</button>
        </form>

    </div>
    <script src="/static/js/getKey.js"></script>
    <script src="/static/js/fetch.js"></script>
    <script src="/static/js/vue.global.js"></script>
    <script src="/static/js/element-plus.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/svg-captcha-browser/dist/captcha.min.js"></script>
    <script>
        async function login() {
            const username = document.getElementById('username').value;
            const password = document.getElementById('password').value;
            const errorDiv = document.getElementById('login-error');

            try {
                const response = await fetch('/api/login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ username, password })
                });

                const data = await response.json();

                if (!response.ok) {
                    errorDiv.textContent = data.detail || '登录失败';
                    errorDiv.style.display = 'block';
                    return;
                }

                // 登录成功
                localStorage.setItem('token', data.token);
                // 设置cookie
                document.cookie = `token=${data.token}; path=/`;

                // 根据用户角色跳转
                if (data.user.is_admin === 2) {
                    // 普通用户跳转到前端系统
                    window.location.href = 'http://localhost:5173/';
                } else {
                    // 管理员跳转到后台首页
                    window.location.href = '/home';
                }
            } catch (error) {
                console.error('登录错误:', error);
                errorDiv.textContent = '服务器错误，请稍后重试';
                errorDiv.style.display = 'block';
            }
        }
        let captchaText = '';

        function generateCaptcha() {
            const width = 120;
            const height = 40;
            const chars = '0123456789ABCDEFGHJKLMNPQRSTUVWXYZabcdefghijkmnpqrstuvwxyz';
            let result = '';
            for (let i = 0; i < 4; i++) {
                result += chars[Math.floor(Math.random() * chars.length)];
            }
            captchaText = result;

            let svg = `<svg width="${width}" height="${height}" xmlns="http://www.w3.org/2000/svg">
                <defs>
                    <linearGradient id="grad" x1="0%" y1="0%" x2="100%" y2="100%">
                        <stop offset="0%" style="stop-color:rgb(240,240,240);stop-opacity:1" />
                        <stop offset="100%" style="stop-color:rgb(220,220,220);stop-opacity:1" />
                    </linearGradient>
                </defs>
                <rect width="100%" height="100%" fill="url(#grad)"/>`;

            // 添加随机干扰点
            for (let i = 0; i < 50; i++) {
                const x = Math.random() * width;
                const y = Math.random() * height;
                const color = `rgb(${Math.random() * 200},${Math.random() * 200},${Math.random() * 200})`;
                svg += `<circle cx="${x}" cy="${y}" r="${Math.random()}" fill="${color}"/>`;
            }

            // 添加随机干扰线
            for (let i = 0; i < 3; i++) {
                const x1 = Math.random() * width;
                const y1 = Math.random() * height;
                const x2 = Math.random() * width;
                const y2 = Math.random() * height;
                const color = `rgb(${Math.random() * 200},${Math.random() * 200},${Math.random() * 200})`;
                svg += `<path d="M${x1} ${y1} Q${(x1 + x2) / 2 + Math.random() * 20 - 10} ${(y1 + y2) / 2 + Math.random() * 20 - 10} ${x2} ${y2}" stroke="${color}" fill="none" stroke-width="0.5"/>`;
            }

            // 渲染文字
            for (let i = 0; i < result.length; i++) {
                const x = 25 + i * 23;
                const y = 25;
                const rotate = Math.random() * 40 - 20;
                const fontSize = Math.floor(Math.random() * 8) + 20;
                const color = `rgb(${Math.random() * 128},${Math.random() * 128},${Math.random() * 128})`;
                const fontFamily = ['Arial', 'Verdana', 'Times New Roman', 'Georgia'][Math.floor(Math.random() * 4)];
                const blur = Math.random() < 0.3 ? `filter="url(#blur${i})"` : '';

                // 添加文字阴影和模糊效果
                svg += `<defs>
                    <filter id="blur${i}">
                        <feGaussianBlur in="SourceGraphic" stdDeviation="0.2" />
                    </filter>
                </defs>`;

                svg += `<text x="${x}" y="${y}" fill="${color}" font-size="${fontSize}" font-family="${fontFamily}" 
                    text-anchor="middle" font-weight="bold" ${blur}
                    transform="rotate(${rotate} ${x} ${y})">${result[i]}</text>`;
            }

            svg += '</svg>';

            document.getElementById('captchaImage').innerHTML = svg;
        }

        document.getElementById('loginForm').addEventListener('submit', async function (e) {
            e.preventDefault();
            const username = document.getElementById('username').value;
            const password = document.getElementById('password').value;
            const captcha = document.getElementById('captcha').value;

            if (captcha.toLowerCase() !== captchaText.toLowerCase()) {
                ElementPlus.ElMessage({
                    message: '验证码输入错误，请重新输入！',
                    type: 'error',
                    duration: 2000
                });
                generateCaptcha();
                document.getElementById('captcha').value = '';
                return;
            }

            try {
                const response = await fetch('/api/login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ username, password })
                });

                if (response.ok) {
                    const data = await response.json();
                    document.cookie = `token=${data.token}; path=/`;
                    window.location.href = '/home';
                } else {
                    ElementPlus.ElMessage({
                        message: '用户不存在或密码输入错误，请尝试重新输入！',
                        type: 'error',
                        duration: 2000
                    });
                    document.getElementById('password').value = '';
                    generateCaptcha();
                    document.getElementById('captcha').value = '';
                }
            } catch (error) {
                ElementPlus.ElMessage({
                    message: '登录请求失败，请稍后重试！',
                    type: 'error',
                    duration: 2000
                });
                console.error('登录失败:', error);
            }
        });

        document.getElementById('captchaImage').addEventListener('click', generateCaptcha);
        generateCaptcha();
    </script>

    <!-- 引入Loading初始化脚本 -->
    <script src="/static/js/loading-init.js"></script>
    <script>
        // 初始化登录页面的Loading效果
        initPageLoading('login.html');
    </script>
</body>

</html>