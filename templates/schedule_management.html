<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>排班管理</title>
    <link href="/static/css/element-plus.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="stylesheet" href="/static/css/common.css">
    <link rel="stylesheet" href="/static/css/schedule.css">
    <script src="/static/js/fetch.js"></script>
    <!-- 引入XLSX库用于Excel导出 -->
    <script src="/static/js/xlsx.full.min.js"></script>
    <style>
        /* Loading 动画样式 */
        #loading-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            z-index: 9999;
            transition: opacity 0.5s ease-out;
        }

        #loading-overlay.fade-out {
            opacity: 0;
            pointer-events: none;
        }

        .loading-spinner {
            width: 60px;
            height: 60px;
            border: 4px solid rgba(255, 255, 255, 0.3);
            border-top: 4px solid #ffffff;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-bottom: 20px;
        }

        .loading-text {
            color: #ffffff;
            font-size: 18px;
            font-weight: 500;
            margin-bottom: 10px;
            text-align: center;
        }

        .loading-progress {
            color: rgba(255, 255, 255, 0.8);
            font-size: 14px;
            text-align: center;
        }

        .loading-dots {
            display: inline-block;
            width: 20px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        @keyframes dots {
            0%, 20% { content: ''; }
            40% { content: '.'; }
            60% { content: '..'; }
            80%, 100% { content: '...'; }
        }

        .loading-dots::after {
            content: '';
            animation: dots 1.5s infinite;
        }

        /* 通用样式 */
        body {
            font-family: Arial, Helvetica, sans-serif;
            margin: 0;
            padding: 0;
            background-color: #f5f7fa;
        }
        
        /* 链接样式 */
        .custom-link {
            color: #409eff;
            text-decoration: none;
            cursor: pointer;
        }
        
        .disabled-link {
            color: #bbb;
            text-decoration: line-through;
            cursor: not-allowed;
        }
        
        .highlight-link {
            color: #67c23a;
            font-weight: 700;
            text-decoration: underline;
        }
        
        .container {
            padding: 20px;
        }
        .home-button {
            margin-left: 10px;
        }
        .form-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
            table-layout: fixed;
        }
        .form-table th {
            font-weight: bold;
            text-align: center;
            padding: 10px;
            background-color: #f5f7fa;
            border-bottom: 1px solid #ebeef5;
            position: sticky;
            top: 0;
            z-index: 10;
        }
        .form-table td {
            padding: 10px;
            vertical-align: middle;
            text-align: center;
        }
        .form-table .store-col {
            width: 13%;
        }
        .form-table .channel-col {
            width: 13%;
        }
        .form-table .anchor-col {
            width: 11%;
        }
        .form-table .shift-col {
            width: 10%;
        }
        .form-table .time-col {
            width: 9%;
        }
        .form-table .duration-col {
            width: 8%;
        }
        .form-table .room-col {
            width: 12%;
        }
        .form-table .notes-col {
            width: 12%;
        }
        .form-table .action-col {
            width: 12%;
        }
        .form-table .count-col {
            width: 7%;
        }
        .single-line-textarea .el-textarea__inner {
            height: 32px !important;
            min-height: 32px !important;
            line-height: 32px !important;
            padding-top: 0 !important;
            padding-bottom: 0 !important;
            resize: none;
            overflow: hidden;
        }
        /* 确保所有输入框高度一致 */
        .el-input__inner, .el-input__wrapper {
            height: 32px !important;
            line-height: 32px !important;
        }
        .date-selector {
            text-align: center;
            margin-bottom: 20px;
        }
        .date-selector .date-label {
            font-weight: bold;
            margin-right: 10px;
            font-size: 16px;
        }
        .store-row {
            background-color: #f0f9eb;
        }
        .sub-row {
            background-color: #ffffff;
        }
        .add-store-btn {
            margin-top: 10px;
            width: 100%;
        }
        .add-sub-row-btn {
            padding: 2px 5px;
            font-size: 12px;
        }
        .delete-row-btn {
            padding: 2px 5px;
            font-size: 12px;
        }
        .empty-cell {
            background-color: #f9f9f9;
        }
        .table-container {
            max-height: 500px;
            overflow-y: auto;
            margin-bottom: 20px;
        }
        .anchor-row {
            background-color: #f4f6f8;
            font-weight: bold;
        }
        .store-row {
            background-color: #f9fafb;
        }
        /* 重置之前的网格样式 */
        .anchor-grid-dropdown .el-scrollbar__view,
        .store-grid-dropdown .el-scrollbar__view,
        .anchor-grid-dropdown .el-select-dropdown__list,
        .store-grid-dropdown .el-select-dropdown__list {
            display: block !important;
        }
        
        /* 使用更直接的方法实现网格布局 */
        .anchor-grid-dropdown .el-select-dropdown__item,
        .store-grid-dropdown .el-select-dropdown__item {
            display: inline-block !important;
            width: calc(33.33% - 4px) !important;
            margin: 2px !important;
            padding: 0 5px !important;
            text-align: center !important;
            box-sizing: border-box !important;
            border-radius: 4px !important;
            height: 32px !important;
            line-height: 32px !important;
            font-size: 14px !important;
        }
        
        .anchor-grid-dropdown .el-select-dropdown__item.selected,
        .store-grid-dropdown .el-select-dropdown__item.selected {
            color: #409eff !important;
            font-weight: bold !important;
        }
        
        .anchor-grid-dropdown .el-select-dropdown__item.hover,
        .store-grid-dropdown .el-select-dropdown__item.hover,
        .anchor-grid-dropdown .el-select-dropdown__item:hover,
        .store-grid-dropdown .el-select-dropdown__item:hover {
            background-color: #f5f7fa !important;
        }
        
        .anchor-grid-dropdown,
        .store-grid-dropdown {
            min-width: 300px !important;
            width: 300px !important;
            max-width: 300px !important;
        }
        
        .el-select-dropdown__wrap {
            max-height: 274px !important;
        }
        .store-input-container {
            display: flex;
            gap: 10px;
            width: 100%;
        }
        
        .store-input-container .el-input {
            flex: 1;
        }
        
        .select-button {
            width: 60px !important;
            min-width: 60px !important;
            padding: 0 !important;
            display: flex !important;
            justify-content: center !important;
            align-items: center !important;
            flex-shrink: 0 !important;
            margin-left: 5px !important;
            height: 32px !important;
        }
        
        .shop-dialog-content {
            display: flex;
            flex-direction: column;
            gap: 10px;
            max-height: 70vh;
        }
        
        .shop-search {
            margin-bottom: 15px;
        }
        
        .shop-search .el-input__wrapper {
            border-radius: 4px !important;
        }
        
        .shop-table-container {
            width: 100%;
            overflow: auto;
        }
        .channel-input-group {
            display: flex;
            width: 100%;
            height: 32px;
            box-sizing: border-box;
        }
        
        .channel-input {
            flex: 1;
            height: 100%;
            padding: 0 10px;
            outline: none;
            font-size: 14px;
            background-color: #fff;
            color: #606266;
            box-sizing: border-box;
            border: 1px solid #dcdfe6;
            border-right: none;
            border-radius: 4px 0 0 4px;
        }
        
        .channel-select-btn {
            width: 60px;
            height: 32px;
            background-color: #409eff;
            color: white;
            border: none;
            cursor: pointer;
            font-size: 14px;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0;
            margin: 0;
            box-sizing: border-box;
            border-radius: 0 4px 4px 0;
        }
        
        .channel-select-btn:hover {
            background-color: #66b1ff;
        }
        
        .channel-input::placeholder {
            color: #c0c4cc;
        }
        
        /* 修复输入框和按钮连接处的圆角 */
        .el-input-group__append {
            border-radius: 0 4px 4px 0 !important;
        }
        
        .el-input__wrapper {
            border-radius: 4px 0 0 4px !important;
        }
        
        /* 修改选择渠道对话框样式 */
        .el-dialog__header {
            padding: 15px 20px;
            margin-right: 0;
            border-bottom: 1px solid #e4e7ed;
        }
        
        .el-dialog__title {
            font-size: 16px;
            font-weight: 600;
            color: #303133;
            line-height: 24px;
        }
        
        .el-dialog__headerbtn {
            top: 15px;
            right: 20px;
        }
        
        .shop-table-container .el-table th {
            background-color: #f5f7fa;
            color: #606266;
            font-weight: 600;
            text-align: center;
            height: 40px;
            padding: 8px 0;
        }
        
        .shop-table-container .el-table td {
            text-align: center;
            height: 40px;
            padding: 8px 0;
        }
        
        .shop-table-container .el-button--link {
            font-weight: normal;
            color: #409eff;
        }
        
        .shop-table-container .el-button--link:hover {
            color: #66b1ff;
        }
        
        /* 调整对话框底部按钮样式 */
        .el-dialog__footer {
            padding: 15px 20px;
            border-top: 1px solid #e4e7ed;
            text-align: right;
        }
        
        .dialog-footer .el-button {
            min-width: 80px;
            margin-left: 10px;
        }
        
        .dialog-footer .el-button:first-child {
            margin-left: 0;
        }
        
        /* 自定义表格样式 */
        .custom-table {
            width: 100%;
            border: 1px solid #ebeef5;
            border-collapse: collapse;
        }
        
        .custom-table-header {
            display: flex;
            background-color: #f5f7fa;
            font-weight: 600;
            color: #606266;
            font-size: 14px;
            border-bottom: 1px solid #ebeef5;
        }
        
        .custom-table-row {
            display: flex;
            border-bottom: 1px solid #ebeef5;
            background-color: #ffffff;
            position: relative;
        }
        
        .custom-table-row.striped {
            background-color: #fafafa;
        }
        
        .custom-table-cell {
            padding: 12px 8px;
            text-align: center;
            display: flex;
            align-items: center;
            justify-content: center;
            border-right: 1px solid #ebeef5;
            min-height: 40px;
            box-sizing: border-box;
        }
        
        .custom-table-cell:last-child {
            border-right: none;
        }
        
        .custom-link {
            color: #409eff;
            text-decoration: none;
            cursor: pointer;
        }
        
        .custom-link:hover {
            color: #66b1ff;
            text-decoration: underline;
        }
        
        /* 固定对话框位置 */
        .store-dialog {
            position: fixed !important;
            top: 50% !important;
            left: 50% !important;
            transform: translate(-50%, -50%) !important;
            margin-top: 0 !important;
            margin-bottom: 0 !important;
        }
        
        /* 班次下拉列表样式 */
        .shift-select-dropdown {
            min-width: 120px !important;
        }
        
        .shift-select-dropdown .el-select-dropdown__item {
            height: 34px !important;
            line-height: 34px !important;
        }
        
        .el-select-dropdown__wrap {
            max-height: 274px !important;
        }
        
        /* 自定义原生select样式 */
        .custom-select {
            width: 100%;
            height: 32px;
            border: 1px solid #dcdfe6;
            border-radius: 4px;
            padding: 0 30px 0 12px;
            color: #606266;
            font-size: 14px;
            background-color: #fff;
            background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%23909399'%3E%3Cpath d='M7 10l5 5 5-5z'/%3E%3C/svg%3E");
            background-repeat: no-repeat;
            background-position: right 8px center;
            background-size: 16px;
            appearance: none;
            -webkit-appearance: none;
            -moz-appearance: none;
            cursor: pointer;
            outline: none;
            transition: border-color 0.2s;
        }
        
        .custom-select:hover {
            border-color: #c0c4cc;
        }
        
        .custom-select:focus {
            border-color: #409eff;
            outline: none;
        }
        
        .custom-select option {
            padding: 10px;
            font-size: 14px;
        }
        
        /* 主播分组排班样式 */
        .anchor-schedule-group {
            margin-bottom: 4px;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 12px rgba(0, 0, 0, 0.05);
            transition: all 0.3s ease;
            cursor: pointer;
            width: 100%;
            box-sizing: border-box;
        }
        
        .anchor-schedule-group:hover {
            box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
            transform: translateY(-2px);
        }
        
        /* 默认的主播名称背景色 */
        .anchor-name {
            background: linear-gradient(135deg,#F4A460, #ff7a5c);
            color: white;
            padding: 4px 8px;
            font-weight: 600;
            border-radius: 8px 8px 0 0;
            font-size: 13px;
            line-height: 1.2;
            letter-spacing: 0.5px;
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        /* 主播分组颜色样式会通过JS动态生成 */
        
        .anchor-delete {
            margin-left: 8px;
            cursor: pointer;
            opacity: 0.6;
            transition: all 0.2s ease;
            font-size: 12px;
        }
        
        .anchor-delete:hover {
            opacity: 1;
            transform: scale(1.1);
        }
        
        .schedule-item {
            padding: 4px 8px;
            border-radius: 0;
            margin-bottom: 1px;
            background-color: #ffffff;
            border-left: none;
            color: #303133;
            font-size: 12px;
            transition: all 0.3s;
            line-height: 1.2;
            position: relative;
            overflow: hidden;
        }
        
        .schedule-item:last-child {
            border-radius: 0 0 8px 8px;
            margin-bottom: 0;
        }
        
        .schedule-item:hover {
            background-color: #f9fafc;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
        }
        
        .schedule-item::before {
            content: '';
            position: absolute;
            left: 0;
            top: 0;
            height: 100%;
            width: 4px;
            background-color: #409eff;
        }
        
        .schedule-item.morning::before {
            background: linear-gradient(to bottom, #67c23a, #95d475);
        }
        
        .schedule-item.evening::before {
            background: linear-gradient(to bottom, #f56c6c, #f89898);
        }
        
        .schedule-item.fullday::before {
            background: linear-gradient(to bottom, #409eff, #79bbff);
        }
        
        .schedule-item.morning {
            background-color: #f8fcf6;
        }
        
        .schedule-item.evening {
            background-color: #fff8f8;
        }
        
        .schedule-item.fullday {
            background-color: #f6faff;
        }
        
        .schedule-row {
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;
            margin-bottom: 2px;
            line-height: 1.3;
            box-sizing: border-box;
        }
        
        .schedule-store {
            font-weight: 600;
            margin-right: 4px;
            font-size: 12px;
            color: #2c3e50;
            text-align: left;
            display: inline-block;
        }
        
        .schedule-channel {
            color: #606266;
            font-size: 11px;
            background-color: #f5f7fa;
            padding: 1px 4px;
            border-radius: 3px;
            text-align: center;
            display: inline-block;
        }
        
        .schedule-time {
            color: #606266;
            font-size: 11px;
            margin-right: 3px;
            text-align: left;
            display: inline-block;
        }
        
        .schedule-duration {
            color: #ffffff;
            font-size: 10px;
            font-weight: 600;
            background-color: #67c23a;
            padding: 1px 4px;
            border-radius: 3px;
            text-align: center;
            display: inline-block;
        }
        
        .schedule-info {
            display: flex;
            flex-direction: column;
            width: 100%;
            box-sizing: border-box;
        }
        
        .schedule-left {
            display: flex;
            align-items: center;
            gap: 2px;
        }
        
        .schedule-right {
            display: flex;
            align-items: center;
        }
        
        /* 早班和晚班的时长标签颜色区分 */
        .morning .schedule-duration {
            background: linear-gradient(135deg, #67c23a, #85ce61);
            box-shadow: 0 1px 2px rgba(103, 194, 58, 0.2);
        }
        
        .evening .schedule-duration {
            background: linear-gradient(135deg, #f56c6c, #f78989);
            box-shadow: 0 1px 2px rgba(245, 108, 108, 0.2);
        }
        
        .fullday .schedule-duration {
            background: linear-gradient(135deg, #409eff, #79bbff);
            box-shadow: 0 1px 2px rgba(64, 158, 255, 0.2);
        }
        
        .schedule-delete {
            color: #909399;
            font-size: 11px;
            margin-left: 4px;
            cursor: pointer;
            opacity: 0.7;
            transition: all 0.2s ease;
        }
        
        .schedule-delete:hover {
            color: #f56c6c;
            opacity: 1;
        }
        
        .schedule-item:hover .schedule-delete {
            opacity: 1;
        }
        
        .day-schedules {
            min-height: 50px;
            height: 100%;
            padding: 10px 5px;
            width: 100%; /* 确保内容区域宽度充满单元格 */
            display: flex;
            flex-direction: column;
            align-items: stretch; /* 改为顶部对齐 */
            justify-content: flex-start; /* 改为顶部对齐 */
            align-content: flex-start; /* 确保内容从顶部开始 */
        }
        
        /* 在小屏幕上调整排班信息的显示 */
        @media (max-width: 768px) {
            .schedule-info {
                flex-direction: column;
                align-items: flex-start;
            }
            
            .schedule-store, .schedule-channel {
                margin-right: 0;
                margin-bottom: 2px;
            }
        }
        
        /* 修改表格样式，确保固定宽度 */
        .schedule-table {
            width: 100%;
            border-collapse: separate;
            border-spacing: 2px;
            table-layout: fixed; /* 固定表格布局 */
            margin-bottom: 20px;
        }
        
        /* 使所有日期单元格宽度严格相等 */
        .schedule-table th, 
        .schedule-table td {
            width: calc(100% / 7); /* 精确计算每个单元格宽度 */
            max-width: calc(100% / 7);
            box-sizing: border-box; /* 确保内边距和边框不会改变宽度计算 */
            margin: 0;
            padding: 0;
            border: none;
            text-align: center;
            vertical-align: top; /* 确保顶部对齐 */
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 2px 12px rgba(0, 0, 0, 0.03);
            transition: all 0.3s ease;
            height: auto; /* 让高度自适应内容 */
        }
        
        .schedule-table th {
            background-color: #f0f5fa;
            color: #606266;
            font-weight: bold;
            padding: 10px 8px;
            text-align: center;
            font-size: 14px;
            border-bottom: 1px solid #ebeef5;
        }
        
        .schedule-table td {
            background-color: #ffffff;
            padding: 0;
        }
        
        .schedule-table td:hover {
            box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
        }
        
        .schedule-table td.today {
            background-color: #f0f9ff;
            box-shadow: 0 4px 16px rgba(64, 158, 255, 0.1);
        }
        
        .schedule-table th.today {
            background: linear-gradient(135deg, #ecf5ff, #d9ecff);
            color: #409eff;
            border-bottom: 2px solid #409eff;
        }
        
        /* 添加排班按钮样式 */
        .add-schedule-btn {
            margin-top: 4px;
            width: 100%;
            background: linear-gradient(135deg, #409eff, #53a8ff);
            color: white;
            border: none;
            border-radius: 6px;
            padding: 8px 12px;
            font-size: 13px;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 6px;
            box-shadow: 0 2px 6px rgba(64, 158, 255, 0.2);
        }
        
        .add-schedule-btn:hover {
            background: linear-gradient(135deg, #66b1ff, #79bbff);
            box-shadow: 0 4px 12px rgba(64, 158, 255, 0.3);
            transform: translateY(-1px);
        }
        
        .add-schedule-btn:active {
            background: linear-gradient(135deg, #3a8ee6, #409eff);
            box-shadow: 0 2px 4px rgba(64, 158, 255, 0.2);
            transform: translateY(0);
        }
        
        .date-header {
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            margin-bottom: 4px;
        }
        
        .date-top, .date-bottom {
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;
            padding: 0 5px;
        }
        
        .weekday {
            font-weight: 600;
            text-align: center;
        }
        
        .copy-icon, .delete-icon {
            cursor: pointer;
            font-size: 12px;
            color: #909399;
            opacity: 0.7;
            transition: all 0.2s ease;
            width: 12px;
            text-align: center;
        }
        
        .copy-icon:hover {
            color: #409eff;
            opacity: 1;
            transform: scale(1.1);
        }
        
        .delete-icon:hover {
            color: #f56c6c;
            opacity: 1;
            transform: scale(1.1);
        }
        
        /* 复制排班对话框样式 */
        .copy-schedule-dialog {
            padding: 10px;
        }
        
        .dialog-section-title {
            font-weight: 600;
            margin-bottom: 10px;
            color: #303133;
            font-size: 14px;
        }
        
        .source-date-info {
            margin-bottom: 20px;
        }
        
        .source-date {
            padding: 8px 12px;
            background-color: #f5f7fa;
            border-radius: 4px;
            color: #606266;
            font-size: 14px;
        }
        
        .target-dates-section {
            margin-bottom: 10px;
        }
        
        .date-actions {
            display: flex;
            flex-direction: column;
            gap: 4px;
        }
        
        .schedule-notes {
            color: #923ac2;
            font-size: 11px;
            background-color: #e2c9f7;
            padding: 1px 4px;
            border-radius: 3px;
            text-align: center;
            display: inline-block;
            margin-left: 3px;
            font-style: italic;
            border: 1px dashed #c2e7b0;
        }
        
        .schedule-count {
            display: inline-block;
            font-size: 12px;
            margin-right: 8px;
            padding: 1px 5px;
            border-radius: 3px;
            background-color: rgba(255, 255, 255, 0.5);
        }
        
        .schedule-count.expected {
            background-color: rgba(230, 162, 60, 0.2);
            color: #b88230;
        }
        
        .schedule-count.real {
            background-color: rgba(103, 194, 58, 0.2);
            color: #529b2e;
        }
        
        .schedule-count.live {
            background-color: rgba(64, 158, 255, 0.2);
            color: #337ecc;
        }
        
        .schedule-time {
            font-weight: 500;
            font-size: 13px;
            margin-right: 4px;
        }
        
        .schedule-room {
            color: #409eff;
            font-size: 11px;
            background-color: #ecf5ff;
            padding: 1px 4px;
            border-radius: 3px;
            text-align: center;
            display: inline-block;
            margin-left: 3px;
        }
        
        .stats-container {
            background-color: #f5f7fa;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
            margin-bottom: 20px;
        }
        .stats-card {
            padding: 15px 12px;
            border-radius: 8px;
            box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
            transition: all 0.3s;
            background-color: #fff;
            height: 100%;
            display: flex;
            flex-direction: column;
            justify-content: center;
            overflow: hidden;
            position: relative;  /* 添加相对定位 */
        }
        
        /* 为标签腾出更多空间 */
        .stats-card.morning::before, 
        .stats-card.evening::before,
        .stats-card.pay::before,
        .stats-card.total::before {
            padding: 1px 6px;
            font-size: 11px;
            border-radius: 0 0 8px 0;
        }
        
        .stats-item i {
            margin-right: 6px;
            font-size: 15px;
            color: #909399;
            width: 16px;
            text-align: center;
            flex-shrink: 0;
        }
        
        .stats-card.morning {
            background: linear-gradient(120deg, #f0f9ff, #ffffff);
            border-left: 4px solid #409eff;
        }
        
        .stats-card.evening {
            background: linear-gradient(120deg, #fdf6ec, #ffffff);
            border-left: 4px solid #e6a23c;
        }

        .stats-card.pay {
            background: linear-gradient(120deg, #f9eefc, #ffffff);
            border-left: 4px solid #cca0f9;
        }
        
        .stats-title {
            font-size: 13px;
            color: #606266;
            margin-bottom: 5px;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }
        
        .stats-value {
            font-size: 16px;
            font-weight: bold;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }
        
        /* 确保内容在一行展示 */
        .stats-card .el-row {
            display: flex;
            flex-wrap: nowrap;
            align-items: center;
        }
        
        .stats-card .el-col {
            padding: 0 5px;
        }
        
        /* 新增stats-item样式，图标和数值在一行 */
        .stats-item {
            display: flex;
            align-items: center;
            white-space: nowrap;
            padding: 4px 0;
        }
        
        /* 添加标签和内容容器的样式 */
        .stats-content {
            display: flex;
            flex-direction: column;
            align-items: flex-start;
            justify-content: center;
        }
        
        /* 添加标签样式 */
        .stats-label {
            font-size: 10px;
            color: #909399;
            opacity: 0.7;
            white-space: nowrap;
            flex-shrink: 0;
            margin-bottom: 2px;
        }
        
        .stats-value {
            flex-grow: 0;
            flex-shrink: 0;
            white-space: nowrap;
            font-weight: bold;
        }
        
        /* 添加每个功能区的标签 */
        .stats-card.morning::before {
            content: "早班";
            position: absolute;
            top: 0;
            left: 0;
            background-color: #409eff;
            color: white;
            padding: 1px 6px;
        }
        
        .stats-card.evening::before {
            content: "晚班";
            position: absolute;
            top: 0;
            left: 0;
            background-color: #e6a23c;
            color: white;
            padding: 1px 6px;
        }

        .stats-card.pay::before {
            content: "付费";
            position: absolute;
            top: 0;
            left: 0;
            background-color: #cca0f9;
            color: white;
            padding: 1px 6px;
        }
        
        /* 总体统计样式 */
        .stats-card.total {
            background: linear-gradient(120deg, #f8f9fa, #ffffff);
            border-left: 4px solid #909399;
        }
        
        .stats-card.total::before {
            content: "总计";
            position: absolute;
            top: 0;
            left: 0;
            background-color: #909399;
            color: white;
            padding: 1px 6px;
        }
        .morning {
            background-color: #f0f9eb;
        }
        .evening {
            background-color: #fff8f8;
        }
        .fullday {
            background-color: #f6faff;
        }
        .occupied-tag {
            display: inline-block !important;
            margin-left: 4px !important;
            padding: 1px 4px !important;
            background-color: #ff4d4f !important;
            color: white !important;
            border-radius: 2px !important;
            font-size: 10px !important;
            font-weight: bold !important;
        }
        .disabled-link {
            color: #ff4d4f !important;
            background-color: #fff0f0 !important;
            text-decoration: line-through !important;
            padding: 2px 8px !important;
            border-radius: 4px !important;
            border: 1px solid #ffccc7 !important;
            pointer-events: none !important;
            position: relative !important;
            display: inline-block !important;
        }
        
        .disabled-link::after {
            content: "已占用";
            position: absolute;
            top: -8px;
            right: -8px;
            background-color: #ff4d4f;
            color: white;
            font-size: 10px;
            padding: 0 4px;
            border-radius: 2px;
            font-weight: bold;
        }
        
        .schedule-calendar {
            width: 100%;
            overflow-x: auto; /* 如果内容溢出，添加水平滚动条 */
        }
        
        /* 添加排班表格样式 */
        .schedule-table-dialog {
            position: fixed !important;
            top: 50% !important;
            left: 50% !important;
            transform: translate(-50%, -50%) !important;
            margin-top: 0 !important;
            margin-bottom: 0 !important;
            display: flex;
            flex-direction: column;
        }
        
        .schedule-table-dialog .el-dialog__header {
            padding: 15px 20px !important;
            margin: 0 !important;
            border-bottom: 1px solid #e4e7ed;
            background-color: #f5f7fa;
        }
        
        .schedule-table-dialog .dialog-header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;
        }
        
        .schedule-table-dialog .el-dialog__title {
            font-size: 16px;
            font-weight: bold;
            color: #303133;
        }
        
        .schedule-table-dialog .view-type-tab {
            cursor: pointer;
            padding: 5px 15px;
            border-radius: 4px;
            transition: all 0.3s ease;
            font-size: 14px;
            color: #606266;
        }
        
        .schedule-table-dialog .view-type-tab.active {
            background-color: #409eff;
            color: #ffffff;
        }
        
        .schedule-table-dialog .view-type-tab:hover:not(.active) {
            background-color: #f0f2f5;
        }
        
        .schedule-table-dialog .date-selector {
            margin: 0;
        }
        
        .schedule-table-dialog .date-selector .el-input__wrapper {
            background-color: #ffffff;
        }
        
        .schedule-table-dialog .el-dialog__body {
            padding: 20px;
            height: calc(90vh - 120px);
            overflow-y: auto;
        }
        
        .schedule-table-content {
            height: 100%;
            overflow: auto;
            position: relative;
        }
        
        /* 表格容器样式 */
        .schedule-table-container {
            max-height: calc(90vh - 180px);
            overflow-y: auto;
            overflow-x: auto;
            position: relative;
            border: 1px solid #ebeef5;
            border-radius: 4px;
        }
        
        /* 自定义滚动条样式 */
        .schedule-table-dialog .el-dialog__body::-webkit-scrollbar {
            width: 8px;
        }
        
        .schedule-table-dialog .el-dialog__body::-webkit-scrollbar-track {
            background: #f4f4f4;
            border-radius: 4px;
        }
        
        .schedule-table-dialog .el-dialog__body::-webkit-scrollbar-thumb {
            background-color: #c0c4cc;
            border-radius: 4px;
            border: 2px solid #f4f4f4;
        }
        
        .schedule-table-dialog .el-dialog__body::-webkit-scrollbar-thumb:hover {
            background-color: #a0a0a0;
        }
        
        .schedule-table-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
            padding: 10px 0;
            border-bottom: 1px solid #ebeef5;
            position: sticky;
            top: 0;
            background-color: white;
            z-index: 10;
        }
        
        .view-type-tabs {
            display: flex;
            align-items: center;
            gap: 15px;
        }
        
        .view-type-tab {
            padding: 8px 15px;
            border-radius: 4px;
            cursor: pointer;
            font-weight: bold;
            transition: all 0.3s;
            border: 1px solid transparent;
        }
        
        .view-type-tab.active {
            color: #409eff;
            border-color: #409eff;
            background-color: rgba(64, 158, 255, 0.1);
        }
        
        .view-type-tab:hover:not(.active) {
            background-color: #f5f7fa;
        }
        
        .schedule-table {
            width: 100%;
            border-collapse: collapse;
            table-layout: fixed !important; /* 确保固定表格布局 */
            border: 1px solid #ebeef5;
        }
        
        /* 修复表格列宽度问题 */
        .schedule-table th,
        .schedule-table td {
            box-sizing: border-box;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: normal;
            word-break: break-word;
        }
        
        /* 特定列宽度样式，使用!important确保应用 */
        .schedule-table th.col-seq { width: 50px !important; min-width: 50px !important; max-width: 50px !important; }
        .schedule-table th.col-leader { width: 80px !important; min-width: 80px !important; max-width: 80px !important; }
        .schedule-table th.col-group { width: 80px !important; min-width: 80px !important; max-width: 80px !important; }
        .schedule-table th.col-anchor { width: 80px !important; min-width: 80px !important; max-width: 100px !important; }
        .schedule-table th.col-store { width: 140px !important; min-width: 140px !important; max-width: 120px !important; }
        .schedule-table th.col-channel { width: 80px !important; min-width: 80px !important; max-width: 80px !important; }
        .schedule-table th.col-shift { width: 60px !important; min-width: 60px !important; max-width: 60px !important; }
        .schedule-table th.col-start-time { width: 80px !important; min-width: 80px !important; max-width: 80px !important; }
        .schedule-table th.col-end-time { width: 80px !important; min-width: 80px !important; max-width: 80px !important; }
        .schedule-table th.col-duration { width: 60px !important; min-width: 60px !important; max-width: 60px !important; }
        .schedule-table th.col-expected { width: 50px !important; min-width: 50px !important; max-width: 50px !important; }
        .schedule-table th.col-real { width: 50px !important; min-width: 50px !important; max-width: 50px !important; }
        .schedule-table th.col-live { width: 50px !important; min-width: 50px !important; max-width: 50px !important; }
        .schedule-table th.col-notes { width: 120px !important; min-width: 120px !important; max-width: 120px !important; }
        .schedule-table th.col-action { width: 120px !important; min-width: 120px !important; max-width: 120px !important; }
        
        /* 对应的td也应用相同的宽度 */
        .schedule-table td.col-seq { width: 50px !important; min-width: 50px !important; max-width: 50px !important; }
        .schedule-table td.col-leader { width: 80px !important; min-width: 80px !important; max-width: 80px !important; }
        .schedule-table td.col-group { width: 80px !important; min-width: 80px !important; max-width: 80px !important; }
        .schedule-table td.col-anchor { width: 100px !important; min-width: 100px !important; max-width: 100px !important; }
        .schedule-table td.col-store { width: 120px !important; min-width: 120px !important; max-width: 120px !important; }
        .schedule-table td.col-channel { width: 80px !important; min-width: 80px !important; max-width: 80px !important; }
        .schedule-table td.col-shift { width: 60px !important; min-width: 60px !important; max-width: 60px !important; }
        .schedule-table td.col-start-time { width: 80px !important; min-width: 80px !important; max-width: 80px !important; }
        .schedule-table td.col-end-time { width: 80px !important; min-width: 80px !important; max-width: 80px !important; }
        .schedule-table td.col-duration { width: 60px !important; min-width: 60px !important; max-width: 60px !important; }
        .schedule-table td.col-expected { width: 50px !important; min-width: 50px !important; max-width: 50px !important; }
        .schedule-table td.col-real { width: 50px !important; min-width: 50px !important; max-width: 50px !important; }
        .schedule-table td.col-live { width: 50px !important; min-width: 50px !important; max-width: 50px !important; }
        .schedule-table td.col-notes { width: 120px !important; min-width: 120px !important; max-width: 120px !important; }
        .schedule-table td.col-action { width: 120px !important; min-width: 120px !important; max-width: 120px !important; }
        
        .schedule-table th {
            background-color: #f0f5fa;
            color: #606266;
            font-weight: bold;
            padding: 10px 8px;
            text-align: center;
            font-size: 14px;
            border-bottom: 1px solid #ebeef5;
            border-right: 1px solid #ebeef5;
            border-radius: 0; /* 取消圆角 */
            vertical-align: middle;
            white-space: nowrap; /* 防止表头文字换行 */
        }
        
        /* 搜索输入框样式 */
        .search-input-left-align .el-input__wrapper {
            border-radius: 20px !important;
            box-shadow: 0 0 0 1px #dcdfe6 inset;
            padding-left: 5px;
            background-color: #f5f7fa;
            width: 100% !important; /* 确保宽度固定 */
            min-width: 180px !important; /* 设置最小宽度 */
            transition: none !important; /* 禁用所有过渡效果 */
        }
        
        .search-input-left-align .el-input__inner {
            color: #606266;
            font-size: 14px;
            padding-left: 0 !important; /* 强制移除左侧内边距 */
            margin-left: -10px !important; /* 进一步减少与图标的间距 */
            text-indent: 0 !important; /* 确保文字不缩进 */
            width: 100% !important; /* 确保宽度固定 */
            transition: none !important; /* 禁用所有过渡效果 */
        }
        
        .search-input-left-align .el-input__suffix {
            right: 10px;
        }
        
        .search-input-left-align .el-input__prefix {
            margin-right: 0 !important; /* 移除图标右侧的默认间距 */
            padding-right: 0 !important; /* 移除右侧内边距 */
        }
        
        /* 确保前缀图标和输入内容紧密相连 */
        .search-input-left-align .el-input__inner:not(:placeholder-shown) {
            padding-left: 0;
        }
        
        /* 操作按钮样式 */
        .table-action-btn {
            padding: 4px 6px;
            font-size: 12px;
            margin: 0 2px;
            display: inline-block;
        }
        
        /* 确保操作按钮在一行显示 */
        .table-action-cell {
            white-space: nowrap;
            width: 100%;
        }
        
        /* 主播分组颜色样式 */
        .group-tag {
            padding: 2px 6px;
            border-radius: 3px;
            font-size: 12px;
            color: white;
            display: inline-block;
            margin-right: 4px;
            background: linear-gradient(135deg, #4bc6ff, #5d92b1);
        }
        
        /* 不同分组的颜色样式 */
        .group-a1组, .group-a1 {
            background: linear-gradient(135deg, #8c3ff8, #cca0f9);
        }
        
        .group-a2组, .group-a2 {
            background: linear-gradient(135deg, #5c6ef4, #97a1ce);
        }
        
        .group-a组, .group-a {
            background: linear-gradient(135deg, #4bc6ff, #5d92b1);
        }
        
        .group-b组, .group-b {
            background: linear-gradient(135deg, #f87b98, #a97381);
        }
        
        .group-c组, .group-c {
            background: linear-gradient(135deg, #28f7dc, #4981b5);
        }
        
        .group-d组, .group-d {
            background: linear-gradient(135deg, #f89c3a, #ac8349);
        }
        
        .schedule-table td {
            padding: 10px 8px;
            text-align: center;
            font-size: 14px;
            color: #606266;
            border-bottom: 1px solid #ebeef5;
            border-right: 1px solid #ebeef5;
            word-break: break-all;
            vertical-align: middle; /* 垂直居中 */
        }
        
        .schedule-table th:last-child,
        .schedule-table td:last-child {
            border-right: none;
        }
        
        .schedule-table tr:nth-child(even) {
            background-color: #fafafa;
        }
        
        .schedule-table tr:hover {
            background-color: #f0f7ff;
        }
        
        /* 排班表格样式 */
        .schedule-table {
            width: 100%;
            border-collapse: collapse;
            border-spacing: 0;
            font-size: 14px;
            color: #606266;
            table-layout: fixed;
        }
        
        .schedule-table thead {
            position: sticky;
            top: 0;
            z-index: 10;
            background-color: #f5f7fa;
        }
        
        .schedule-table thead tr {
            position: sticky;
            top: 0;
            z-index: 10;
            box-shadow: 0 2px 3px rgba(0, 0, 0, 0.1);
        }
        
        .schedule-table th {
            padding: 12px 8px;
            text-align: center;
            font-weight: 600;
            background-color: #f5f7fa;
            border-bottom: 1px solid #ebeef5;
            white-space: nowrap;
            position: sticky;
            top: 0;
            z-index: 10;
        }
        
        .schedule-table td {
            padding: 8px;
            text-align: center;
            border-bottom: 1px solid #ebeef5;
        }
        
        /* 确保鼠标悬停时行颜色变化 */
        .schedule-table tbody tr:hover {
            background-color: #f5f7fa;
        }
        
        /* 表格容器样式 */
        .schedule-table-container {
            max-height: calc(90vh - 200px);
            overflow-y: auto;
            overflow-x: auto;
            position: relative;
            border: 1px solid #ebeef5;
            border-radius: 4px;
            margin-bottom: 15px;
        }
        
        .schedule-table-dialog .search-container {
            position: relative;
        }
        
        .schedule-table-dialog .search-container .el-input__inner {
            border-radius: 20px;
            padding-left: 35px;
        }
        
        .schedule-table-dialog .search-container .el-input__prefix {
            left: 10px;
        }
        
        .search-result-count {
            font-size: 12px;
            color: #909399;
            margin-top: 5px;
            text-align: right;
        }
        
        /* 过滤器样式 */
        .filter-dialog .filter-panel {
            padding: 0 10px;
        }
        
        .filter-dialog .filter-row {
            display: flex;
            margin-bottom: 15px;
            gap: 20px;
        }
        
        .filter-dialog .filter-item {
            flex: 1;
        }
        
        .filter-dialog .filter-label {
            font-size: 14px;
            color: #606266;
            margin-bottom: 8px;
            font-weight: 500;
        }
        
        .filter-info-container .filter-tag {
            background-color: #ecf5ff;
            border: 1px solid #d9ecff;
            color: #409eff;
            padding: 5px 10px;
            border-radius: 4px;
            font-size: 12px;
        }
        
        .active-filters {
            display: flex;
            align-items: center;
        }
        
        /* 确保搜索框中的文本左对齐 */
        .search-input-left-align .el-input__inner {
            text-align: left !important;
        }
        
        .search-input-left-align .el-input__prefix {
            left: 8px !important;
        }
        
        .search-input-left-align.el-input--prefix .el-input__inner {
            padding-left: 30px !important;
        }
        
        /* 确保搜索结果计数显示不影响输入框 */
        .search-result-count {
            position: absolute;
            bottom: -18px;
            left: 0;
            font-size: 12px;
            color: #909399;
            background-color: rgba(255, 255, 255, 0.9);
            padding: 2px 6px;
            border-radius: 4px;
            z-index: 1;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            white-space: nowrap;
        }
        
        .schedule-table .col-duration {
            width: 60px;
            text-align: center;
        }
        
        .schedule-table .col-rest {
            width: 60px;
            text-align: center;
        }
        
        .schedule-table .col-expected {
            width: 60px;
            text-align: center;
        }
        
        .schedule-table .col-rest {
            width: 60px;
            text-align: center;
        }
        
        .schedule-table .col-duration {
            width: 60px;
            text-align: center;
        }
        
        .schedule-table .col-room {
            width: 80px;
            text-align: center;
        }
        
        .schedule-table .col-expected {
            width: 60px;
            text-align: center;
        }
        .schedule-table .col-room {
            width: 100px;
            text-align: center;
        }
        .schedule-table th.col-notes { width: 100px !important; min-width: 100px !important; max-width: 100px !important; }
        .schedule-table td.col-notes { width: 100px !important; min-width: 100px !important; max-width: 100px !important; }
        .schedule-table th.col-live { width: 50px !important; min-width: 50px !important; max-width: 50px !important; }
        .schedule-table th.col-room { width: 100px !important; min-width: 100px !important; max-width: 100px !important; }
        .schedule-table th.col-notes { width: 100px !important; min-width: 100px !important; max-width: 100px !important; }
        .schedule-table th.col-action { width: 120px !important; min-width: 120px !important; max-width: 120px !important; }
        .schedule-table td.col-live { width: 50px !important; min-width: 50px !important; max-width: 50px !important; }
        .schedule-table td.col-room { width: 100px !important; min-width: 100px !important; max-width: 100px !important; }
        .schedule-table td.col-notes { width: 100px !important; min-width: 100px !important; max-width: 100px !important; }
        .schedule-table td.col-action { width: 120px !important; min-width: 120px !important; max-width: 120px !important; }
        .schedule-table .col-room {
            width: 100px !important;
            min-width: 100px !important;
            max-width: 100px !important;
            text-align: center;
        }
        .schedule-table .col-notes {
            width: 100px !important;
            min-width: 100px !important;
            max-width: 100px !important;
        }
        
        /* 隐藏休息时长输入框的步进器 */
        .el-input input[type="number"]::-webkit-outer-spin-button,
        .el-input input[type="number"]::-webkit-inner-spin-button {
            -webkit-appearance: none;
            margin: 0;
        }
        
        .el-input input[type="number"] {
            -moz-appearance: textfield;
        }
    </style>
</head>
<body>
    <!-- Loading 覆盖层 -->
    <div id="loading-overlay">
        <div class="loading-spinner"></div>
        <div class="loading-text">正在加载排班管理系统</div>
        <div class="loading-progress">
            <span id="loading-status">正在加载静态资源</span>
            <span class="loading-dots"></span>
        </div>
    </div>

    {% raw %}
    <div id="app">
        <div class="container">
            <!-- 页面标题 -->
            <div class="page-header">
                <h1 class="page-title">排班管理系统</h1>
                <div>
                    <el-button type="success" @click="openScheduleTable">
                        <i class="fas fa-table"></i> 排班表格
                    </el-button>
                    <el-button type="primary" class="home-button" @click="goToHome()">
                        <i class="fas fa-home"></i> 返回首页
                    </el-button>
                </div>
            </div>
            <!-- 排班统计卡片 -->
            <div class="stats-container">
                <!-- 日期调整 -->
                <el-row :gutter="20" style="margin-bottom: 20px; justify-content: center;">
                    <el-col :span="2">
                        <el-button @click="previousDay">
                            <i class="fas fa-chevron-left"></i> 上一天
                        </el-button>
                    </el-col>
                    <el-col :span="4">
                        <el-date-picker
                            v-model="selectedDate"
                            type="date"
                            placeholder="选择日期"
                            format="YYYY-MM-DD"
                            value-format="YYYY-MM-DD"
                            style="width: 200px;"
                            @change="fetchAnchorStats"
                        />
                    </el-col>
                    <el-col :span="2">
                        <el-button @click="nextDay">
                            下一天 <i class="fas fa-chevron-right"></i>
                        </el-button>
                    </el-col>
                </el-row>
                
                <!-- 总体统计 -->
                <el-row :gutter="20" style="margin-bottom: 20px;">
                    <el-col :span="24">
                        <div class="stats-card total">
                            <el-row :gutter="10" type="flex" justify="center" align="middle" style="flex-wrap: nowrap;">
                                <el-col :span="4">
                                    <div class="stats-item">
                                        <i class="fas fa-clock"></i>
                                        <div class="stats-content">
                                            <div class="stats-label">总时长</div>
                                            <div class="stats-value" style="color: #409eff;">{{ anchorStats.total_hours }}小时</div>
                                        </div>
                                    </div>
                                </el-col>
                                <el-col :span="4">
                                    <div class="stats-item">
                                        <i class="fas fa-user"></i>
                                        <div class="stats-content">
                                            <div class="stats-label">主播数</div>
                                            <div class="stats-value" style="color: #67c23a;">{{ anchorStats.anchor_count }}人</div>
                                        </div>
                                    </div>
                                </el-col>
                                <el-col :span="4">
                                    <div class="stats-item">
                                        <i class="fas fa-store"></i>
                                        <div class="stats-content">
                                            <div class="stats-label">店铺数</div>
                                            <div class="stats-value" style="color: #e6a23c;">{{ anchorStats.store_count }}个</div>
                                        </div>
                                    </div>
                                </el-col>
                                <el-col :span="4">
                                    <div class="stats-item">
                                        <i class="fas fa-calculator"></i>
                                        <div class="stats-content">
                                            <div class="stats-label">总预计数</div>
                                            <div class="stats-value" style="color: #f56c6c;">{{ anchorStats.expected_count }}个</div>
                                        </div>
                                    </div>
                                </el-col>
                                <el-col :span="4">
                                    <div class="stats-item">
                                        <i class="fas fa-check-circle"></i>
                                        <div class="stats-content">
                                            <div class="stats-label">总实际数</div>
                                            <div class="stats-value" style="color: #67c23a;">{{ anchorStats.real_count }}个</div>
                                        </div>
                                    </div>
                                </el-col>
                                <el-col :span="4">
                                    <div class="stats-item">
                                        <i class="fas fa-recycle"></i>
                                        <div class="stats-content">
                                            <div class="stats-label">捡漏</div>
                                            <div class="stats-value" style="color: #f39ffd;">{{ anchorStats.salvage_count }}个</div>
                                        </div>
                                    </div>
                                </el-col>
                            </el-row>
                        </div>
                    </el-col>
                </el-row>
                
                <!-- 早晚全天班统计-->
                <el-row :gutter="20" style="margin-bottom: 20px;">
                    <!-- 使用 el-row 包裹三张卡片 -->
                    <el-col :span="8">
                        <div class="stats-card morning">
                            <el-row :gutter="10" type="flex" justify="space-between" align="middle" style="flex-wrap: nowrap;">
                                <el-col :span="4">
                                    <div class="stats-item">
                                        <i class="fas fa-sun"></i>
                                        <div class="stats-content">
                                            <div class="stats-label">时长</div>
                                            <div class="stats-value" style="color: #409eff;">{{ anchorStats.morning.total_hours }}小时</div>
                                        </div>
                                    </div>
                                </el-col>
                                <el-col :span="4">
                                    <div class="stats-item">
                                        <i class="fas fa-user"></i>
                                        <div class="stats-content">
                                            <div class="stats-label">主播数</div>
                                            <div class="stats-value" style="color: #67c23a;">{{ anchorStats.morning.anchor_count }}人</div>
                                        </div>
                                    </div>
                                </el-col>
                                <el-col :span="4">
                                    <div class="stats-item">
                                        <i class="fas fa-store"></i>
                                        <div class="stats-content">
                                            <div class="stats-label">店铺数</div>
                                            <div class="stats-value" style="color: #e6a23c;">{{ anchorStats.morning.store_count }}个</div>
                                        </div>
                                    </div>
                                </el-col>
                                <el-col :span="4">
                                    <div class="stats-item">
                                        <i class="fas fa-calculator"></i>
                                        <div class="stats-content">
                                            <div class="stats-label">预计数</div>
                                            <div class="stats-value" style="color: #f56c6c;">{{ anchorStats.morning.expected_count }}个</div>
                                        </div>
                                    </div>
                                </el-col>
                                <el-col :span="4">
                                    <div class="stats-item">
                                        <i class="fas fa-check-circle"></i>
                                        <div class="stats-content">
                                            <div class="stats-label">实际数</div>
                                            <div class="stats-value" style="color: #67c23a;">{{ anchorStats.morning.real_count }}个</div>
                                        </div>
                                    </div>
                                </el-col>
                            </el-row>
                        </div>
                    </el-col>
                    <el-col :span="8">
                        <div class="stats-card evening">
                            <el-row :gutter="10" type="flex" justify="space-between" align="middle" style="flex-wrap: nowrap;">
                                <el-col :span="4">
                                    <div class="stats-item">
                                        <i class="fas fa-moon"></i>
                                        <div class="stats-content">
                                            <div class="stats-label">时长</div>
                                            <div class="stats-value" style="color: #409eff;">{{ anchorStats.evening.total_hours }}小时</div>
                                        </div>
                                    </div>
                                </el-col>
                                <el-col :span="4">
                                    <div class="stats-item">
                                        <i class="fas fa-user"></i>
                                        <div class="stats-content">
                                            <div class="stats-label">主播数</div>
                                            <div class="stats-value" style="color: #67c23a;">{{ anchorStats.evening.anchor_count }}人</div>
                                        </div>
                                    </div>
                                </el-col>
                                <el-col :span="4">
                                    <div class="stats-item">
                                        <i class="fas fa-store"></i>
                                        <div class="stats-content">
                                            <div class="stats-label">店铺数</div>
                                            <div class="stats-value" style="color: #e6a23c;">{{ anchorStats.evening.store_count }}个</div>
                                        </div>
                                    </div>
                                </el-col>
                                <el-col :span="4">
                                    <div class="stats-item">
                                        <i class="fas fa-calculator"></i>
                                        <div class="stats-content">
                                            <div class="stats-label">预计数</div>
                                            <div class="stats-value" style="color: #f56c6c;">{{ anchorStats.evening.expected_count }}个</div>
                                        </div>
                                    </div>
                                </el-col>
                                <el-col :span="4">
                                    <div class="stats-item">
                                        <i class="fas fa-check-circle"></i>
                                        <div class="stats-content">
                                            <div class="stats-label">实际数</div>
                                            <div class="stats-value" style="color: #67c23a;">{{ anchorStats.evening.real_count }}个</div>
                                        </div>
                                    </div>
                                </el-col>
                            </el-row>
                        </div>
                    </el-col>
                    <el-col :span="8">
                        <div class="stats-card pay">
                            <el-row :gutter="10" type="flex" justify="space-between" align="middle" style="flex-wrap: nowrap;">
                                <el-col :span="4">
                                    <div class="stats-item">
                                        <i class="fas fa-store"></i>
                                        <div class="stats-content">
                                            <div class="stats-label">店铺数</div>
                                            <div class="stats-value" style="color: #e6a23c;">{{ anchorStats.fullday.store_count }}个</div>
                                        </div>
                                    </div>
                                </el-col>
                                <el-col :span="4">
                                    <div class="stats-item">
                                        <i class="fas fa-calculator"></i>
                                        <div class="stats-content">
                                            <div class="stats-label">预计数</div>
                                            <div class="stats-value" style="color: #f56c6c;">{{ anchorStats.fullday.expected_count }}个</div>
                                        </div>
                                    </div>
                                </el-col>
                                <el-col :span="4">
                                    <div class="stats-item">
                                        <i class="fas fa-check-circle"></i>
                                        <div class="stats-content">
                                            <div class="stats-label">实际数</div>
                                            <div class="stats-value" style="color: #67c23a;">{{ anchorStats.fullday.real_count }}个</div>
                                        </div>
                                    </div>
                                </el-col>
                                <el-col :span="4">
                                    <div class="stats-item">
                                        <i class="fas fa-check-circle"></i>
                                        <div class="stats-content">
                                            <div class="stats-label">非排班实际数</div>
                                            <div class="stats-value" style="color: #409eff;">{{ anchorStats.fullday.not_real_count }}个</div>
                                        </div>
                                    </div>
                                </el-col>
                            </el-row>
                        </div>
                    </el-col>
                </el-row>
            </div>
            
            <!-- 加载中 -->
            <el-loading v-model:full-screen="loading" background="rgba(255, 255, 255, 0.7)" />
            
            <!-- 排班表格视图 -->
            <div class="schedule-container">
                <div class="control-panel">
                    <el-row>
                        <el-col :span="24">
                            <div class="date-navigation">
                                <el-button-group>
                                    <el-button @click="previousWeek">
                                        <i class="fas fa-chevron-left"></i> 上一周
                                    </el-button>
                                    <el-button @click="goToToday">
                                        本周
                                    </el-button>
                                    <el-button @click="nextWeek">
                                        下一周 <i class="fas fa-chevron-right"></i>
                                    </el-button>
                                </el-button-group>
                            </div>
                        </el-col>
                    </el-row>
                </div>
                
                <!-- 排班表格 -->
                <div class="schedule-calendar">
                    <table class="schedule-table">
                        <colgroup>
                            <col v-for="day in weekDays" :key="'col-'+day.formatted">
                        </colgroup>
                        <thead>
                            <tr>
                                <th v-for="day in weekDays" :key="day.formatted" :class="{ 'today': day.isToday }">
                                    <div class="date-header">
                                        <div class="date-top">
                                            <div style="width: 12px;"></div> <!-- 占位元素，保持对称 -->
                                            <span class="weekday">{{ day.date.toLocaleDateString('zh-CN', { weekday: 'short' }) }}</span>
                                            <i class="fas fa-copy copy-icon" @click.stop.prevent="openCopyScheduleDialog(day.formatted)" title="复制排班"></i>
                                        </div>
                                        <div class="date-bottom">
                                            <div style="width: 12px;"></div> <!-- 占位元素，保持对称 -->
                                            <span>{{ day.date.toLocaleDateString('zh-CN', { month: 'numeric', day: 'numeric' }) }}</span>
                                            <i class="fas fa-trash-alt delete-icon" @click.stop.prevent="confirmDeleteDaySchedules(day.formatted)" title="删除当天排班"></i>
                                        </div>
                                    </div>
                                </th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td v-for="day in weekDays" :key="day.formatted" :class="{ 'today': day.isToday }" style="vertical-align: top;">
                                    <div class="day-schedules">
                                        <!-- 按主播分组显示排班信息 -->
                                        <div 
                                            v-for="(schedules, anchorName) in getGroupedSchedules(day.formatted)" 
                                            :key="anchorName"
                                            class="anchor-schedule-group"
                                            @click="openScheduleDialog(day.formatted, schedules[0])"
                                        >
                                            <div :class="['anchor-name', schedules[0]?.groupInfo ? `anchor-name-group-${schedules[0].groupInfo.group.replace(/主播(.*?)组/, '$1')}` : '']">
                                                {{ anchorName }}
                                                <span class="anchor-delete" @click.stop="deleteAnchorSchedules(schedules)">
                                                    <i class="fas fa-times"></i>
                                                </span>
                                            </div>
                                            <div 
                                                v-for="schedule in schedules" 
                                                :key="schedule.id"
                                                :class="['schedule-item', 
                                                        schedule.shift === 'morning' ? 'morning' : 
                                                        (schedule.shift === 'evening' ? 'evening' : 'fullday')]"
                                                @click="openScheduleDialog(day.formatted, schedule)"
                                            >
                                                <div class="schedule-info">
                                                    <div class="schedule-row">
                                                        <div class="schedule-left">
                                                            <span class="schedule-store"><strong>{{ schedule.store_id }}</strong></span>
                                                            <span class="schedule-channel">{{ schedule.channel_id }}</span>
                                                            <span class="schedule-notes" v-if="schedule.notes">({{ schedule.notes }})</span>
                                                        </div>
                                                        <div class="schedule-right">
                                                            <span class="schedule-delete" @click.stop="deleteSchedule(schedule)">
                                                                <i class="fas fa-trash-alt"></i>
                                                            </span>
                                                        </div>
                                                    </div>
                                                    <div class="schedule-row">
                                                        <div class="schedule-left">
                                                            <span class="schedule-time">{{ formatTime(schedule.start_time) }}-{{ formatTime(schedule.end_time) }}</span>
                                                            <span class="schedule-duration">({{ schedule.duration }}h)</span>
                                                            <span class="schedule-room" v-if="schedule.room">{{ schedule.room }}</span>
                                                        </div>
                                                    </div>
                                                    <div class="schedule-row" v-if="schedule.expected_count || schedule.real_count">
                                                        <div class="schedule-left">
                                                            <span class="schedule-count expected" v-if="schedule.expected_count">预计: {{schedule.expected_count}}</span>
                                                            <span class="schedule-count real" v-if="schedule.real_count">实际: {{schedule.real_count}}</span>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        
                                        <el-button 
                                            size="small" 
                                            type="primary" 
                                            class="add-schedule-btn"
                                            @click="openScheduleDialog(day.formatted)"
                                        >
                                            <i class="fas fa-plus"></i> 添加排班
                                        </el-button>
                                    </div>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
            
            <!-- 排班表单对话框 -->
            <el-dialog v-model="dialogVisible.schedule" title="排班信息" width="1800px" :close-on-click-modal="false">
                <template #header>
                    <div style="display: flex; justify-content: space-between; align-items: center; width: 100%;">
                        <span class="el-dialog__title">排班信息</span>
                        <div class="date-selector" style="margin: 0;">
                            <span class="date-label">日期：</span>
                            <el-date-picker
                                v-model="scheduleForm.date"
                                type="date"
                                placeholder="选择日期"
                                format="YYYY-MM-DD"
                                value-format="YYYY-MM-DD"
                                style="width: 200px;"
                                required
                            />
                        </div>
                        <div style="width: 200px;"></div> <!-- 用于平衡布局 -->
                    </div>
                </template>
                <div>
                    <!-- 表格式表单 -->
                    <div class="table-container" style="padding-top: 20px; max-height: 400px; overflow-y: auto;">
                        <table class="form-table">
                            <thead>
                                <tr>
                                    <th class="anchor-col" style="position: sticky; top: 0; z-index: 10;">主播</th>
                                    <th class="store-col" style="position: sticky; top: 0; z-index: 10;">店铺</th>
                                    <th class="channel-col" style="position: sticky; top: 0; z-index: 10;">渠道</th>
                                    <th class="shift-col" style="position: sticky; top: 0; z-index: 10;">班次</th>
                                    <th class="time-col" style="position: sticky; top: 0; z-index: 10;">开始时间</th>
                                    <th class="time-col" style="position: sticky; top: 0; z-index: 10;">结束时间</th>
                                    <th class="time-col" style="position: sticky; top: 0; z-index: 10;">休息(h)</th>
                                    <th class="duration-col" style="position: sticky; top: 0; z-index: 10;">时长(h)</th>
                                    <th class="room-col" style="position: sticky; top: 0; z-index: 10;">直播间</th>
                                    <th class="count-col" style="position: sticky; top: 0; z-index: 10;">预计</th>
                                    <th class="notes-col" style="position: sticky; top: 0; z-index: 10;">备注</th>
                                    <th class="action-col" style="position: sticky; top: 0; z-index: 10;">操作</th>
                                </tr>
                            </thead>
                            <tbody>
                                <!-- 循环主播组 -->
                                <template v-for="(anchorGroup, anchorIndex) in scheduleForm.anchorGroups" :key="anchorIndex">
                                    <!-- 主播行和第一条排班规则在同一行 -->
                                    <tr class="anchor-row">
                                        <td>
                                            <el-select 
                                                v-model="anchorGroup.anchor_id" 
                                                placeholder="请选择主播" 
                                                style="width: 100%;" 
                                                filterable
                                                popper-class="anchor-grid-dropdown"
                                                required
                                            >
                                                <el-option
                                                    v-for="anchor in anchors"
                                                    :key="anchor.id"
                                                    :label="anchor.name"
                                                    :value="anchor.id"
                                                />
                                            </el-select>
                                        </td>
                                        <td v-if="anchorGroup.subRows.length > 0">
                                            <el-input 
                                                v-model="anchorGroup.subRows[0].store_id" 
                                                placeholder="请选择店铺" 
                                                readonly
                                                style="width: 100%;"
                                                required
                                            />
                                        </td>
                                        <td v-if="anchorGroup.subRows.length > 0">
                                            <el-input 
                                                v-model="anchorGroup.subRows[0].channel_id" 
                                                placeholder="请选择渠道" 
                                                readonly
                                                style="width: 100%;"
                                                required
                                            >
                                                <template #append>
                                                    <el-button 
                                                        type="success" 
                                                        @click="openStoreDialog(anchorIndex, 0)"
                                                        style="background-color: #67c23a; border-color: #67c23a; color: white; border-radius: 0 4px 4px 0;"
                                                    >选择</el-button>
                                                </template>
                                            </el-input>
                                        </td>
                                        <td v-if="anchorGroup.subRows.length > 0">
                                            <select 
                                                v-model="anchorGroup.subRows[0].shift" 
                                                class="custom-select"
                                                required
                                                @change="handleShiftChange(anchorIndex, 0)"
                                            >
                                                <option value="">请选择班次</option>
                                                <option value="morning">早班</option>
                                                <option value="evening">晚班</option>
                                                <option value="fullday">全天</option>
                                            </select>
                                        </td>
                                        <td v-if="anchorGroup.subRows.length > 0">
                                            <el-time-picker
                                                v-model="anchorGroup.subRows[0].start_time"
                                                placeholder="选择开始时间"
                                                format="HH:mm"
                                                value-format="HH:mm:ss"
                                                style="width: 100%;"
                                                @change="() => { calculateDuration(anchorIndex, 0); autoFillShift(anchorIndex, 0); }"
                                                required
                                            />
                                        </td>
                                        <td v-if="anchorGroup.subRows.length > 0">
                                            <el-time-picker
                                                v-model="anchorGroup.subRows[0].end_time"
                                                placeholder="选择结束时间"
                                                format="HH:mm"
                                                value-format="HH:mm:ss"
                                                style="width: 100%;"
                                                @change="calculateDuration(anchorIndex, 0)"
                                                required
                                            />
                                        </td>
                                        <td v-if="anchorGroup.subRows.length > 0">
                                            <el-input
                                                v-model.number="anchorGroup.subRows[0].rest_duration"
                                                type="number"
                                                placeholder="休息(h)"
                                                style="width: 100%; max-width: 80px;"
                                                :disabled="anchorGroup.subRows[0].shift === 'fullday'"
                                            >
                                            </el-input>
                                        </td>
                                        <td v-if="anchorGroup.subRows.length > 0">
                                            <el-input v-model="anchorGroup.subRows[0].duration" disabled />
                                        </td>
                                        <td v-if="anchorGroup.subRows.length > 0">
                                            <el-select 
                                                v-model="anchorGroup.subRows[0].room" 
                                                placeholder="请选择直播间" 
                                                style="width: 100%;" 
                                                filterable
                                                required
                                                :disabled="anchorGroup.subRows[0].shift === 'fullday'"
                                            >
                                                <el-option
                                                    v-for="room in rooms"
                                                    :key="room"
                                                    :label="room"
                                                    :value="room"
                                                />
                                            </el-select>
                                        </td>
                                        <td v-if="anchorGroup.subRows.length > 0">
                                            <el-input 
                                                v-model.number="anchorGroup.subRows[0].expected_count" 
                                                placeholder="预计"
                                                style="width: 100%;"
                                                @input="validateNumber($event, anchorIndex, 0, 'expected_count')"
                                            />
                                        </td>
                                        <td v-if="anchorGroup.subRows.length > 0">
                                            <el-input 
                                                v-model="anchorGroup.subRows[0].notes" 
                                                type="textarea" 
                                                placeholder="请输入备注"
                                                :rows="1"
                                                class="single-line-textarea"
                                                style="width: 100%;"
                                            />
                                        </td>
                                        <td>
                                            <el-button 
                                                type="primary" 
                                                size="small" 
                                                class="add-sub-row-btn"
                                                @click="addSubRow(anchorIndex)"
                                            >
                                                <i class="fas fa-plus"></i> 添加排班
                                            </el-button>
                                        </td>
                                    </tr>
                                    
                                    <!-- 额外的排班规则行 -->
                                    <tr 
                                        v-for="(subRow, subIndex) in anchorGroup.subRows.slice(1)" 
                                        :key="`${anchorIndex}-${subIndex+1}`"
                                        class="sub-row"
                                    >
                                        <td class="empty-cell"></td>
                                        <td>
                                            <el-input 
                                                v-model="subRow.store_id" 
                                                placeholder="请选择店铺" 
                                                readonly
                                                style="width: 100%;"
                                                required
                                            />
                                        </td>
                                        <td>
                                            <el-input 
                                                v-model="subRow.channel_id" 
                                                placeholder="请选择渠道" 
                                                readonly
                                                style="width: 100%;"
                                                required
                                            >
                                                <template #append>
                                                    <el-button 
                                                        type="success" 
                                                        @click="openStoreDialog(anchorIndex, subIndex+1)"
                                                        style="background-color: #67c23a; border-color: #67c23a; color: white; border-radius: 0 4px 4px 0;"
                                                    >选择</el-button>
                                                </template>
                                            </el-input>
                                        </td>
                                        <td>
                                            <select 
                                                v-model="subRow.shift" 
                                                class="custom-select"
                                                required
                                                @change="handleShiftChange(anchorIndex, subIndex+1)"
                                            >
                                                <option value="">请选择班次</option>
                                                <option value="morning">早班</option>
                                                <option value="evening">晚班</option>
                                                <option value="fullday">全天</option>
                                            </select>
                                        </td>
                                        <td>
                                            <el-time-picker
                                                v-model="subRow.start_time"
                                                placeholder="选择开始时间"
                                                format="HH:mm"
                                                value-format="HH:mm:ss"
                                                style="width: 100%;"
                                                @change="() => { calculateDuration(anchorIndex, subIndex+1); autoFillShift(anchorIndex, subIndex+1); }"
                                                required
                                            />
                                        </td>
                                        <td>
                                            <el-time-picker
                                                v-model="subRow.end_time"
                                                placeholder="选择结束时间"
                                                format="HH:mm"
                                                value-format="HH:mm:ss"
                                                style="width: 100%;"
                                                @change="calculateDuration(anchorIndex, subIndex+1)"
                                                required
                                            />
                                        </td>
                                        <td>
                                            <el-input
                                                v-model.number="subRow.rest_duration"
                                                type="number"
                                                placeholder="休息(h)"
                                                style="width: 100%; max-width: 80px;"
                                                :disabled="subRow.shift === 'fullday'"
                                            >
                                            </el-input>
                                        </td>
                                        <td>
                                            <el-input v-model="subRow.duration" disabled />
                                        </td>
                                        <td>
                                            <el-select 
                                                v-model="subRow.room" 
                                                placeholder="请选择直播间" 
                                                style="width: 100%;" 
                                                filterable
                                                required
                                                :disabled="subRow.shift === 'fullday'"
                                            >
                                                <el-option
                                                    v-for="room in rooms"
                                                    :key="room"
                                                    :label="room"
                                                    :value="room"
                                                />
                                            </el-select>
                                        </td>
                                        <td>
                                            <el-input 
                                                v-model.number="subRow.expected_count" 
                                                placeholder="预计"
                                                style="width: 100%;"
                                                @input="validateNumber($event, anchorIndex, subIndex+1, 'expected_count')"
                                            />
                                        </td>
                                        <td>
                                            <el-input 
                                                v-model="subRow.notes" 
                                                type="textarea" 
                                                placeholder="请输入备注"
                                                :rows="1"
                                                class="single-line-textarea"
                                                style="width: 100%;"
                                            />
                                        </td>
                                        <td>
                                            <el-button 
                                                type="danger" 
                                                size="small" 
                                                class="delete-row-btn"
                                                @click="deleteSubRow(anchorIndex, subIndex+1)"
                                            >
                                                <i class="fas fa-trash"></i>
                                            </el-button>
                                        </td>
                                    </tr>
                                </template>
                            </tbody>
                        </table>
                    </div>
                    <div style="margin-bottom: 10px; padding: 8px; background-color: #f5f5f5; border-radius: 4px;">
                        <div style="margin-bottom: 4px; color: #f56c6c; font-weight: bold;">已排班店铺不可选择</div>
                        <div v-if="scheduleForm.anchorGroups[currentEditingAnchorIndex]?.subRows[currentEditingSubRowIndex]?.shift === 'morning'">
                            <strong>早班已占用:</strong> 
                            <span v-for="store in usedStoresInCurrentDay.morning" :key="store" 
                                  style="display: inline-block; margin: 2px 4px; padding: 2px 6px; background-color: #fff0f0; border: 1px solid #ffa0a0; border-radius: 2px;">
                                {{ store }}
                            </span>
                        </div>
                        <div v-if="scheduleForm.anchorGroups[currentEditingAnchorIndex]?.subRows[currentEditingSubRowIndex]?.shift === 'evening'">
                            <strong>晚班已占用:</strong> 
                            <span v-for="store in usedStoresInCurrentDay.evening" :key="store" 
                                  style="display: inline-block; margin: 2px 4px; padding: 2px 6px; background-color: #fff0f0; border: 1px solid #ffa0a0; border-radius: 2px;">
                                {{ store }}
                            </span>
                        </div>
                    </div>
                </div>
                <template #footer>
                    <span class="dialog-footer">
                        <el-button @click="cancelScheduleForm">取消</el-button>
                        <el-button type="primary" @click="saveSchedule">保存</el-button>
                    </span>
                </template>
            </el-dialog>
            
            <!-- 店铺选择对话框 -->
            <el-dialog v-model="dialogVisible.store" title="选择渠道" width="80%" class="store-dialog" :modal-append-to-body="false" :append-to-body="true" :close-on-click-modal="false">
                <div class="shop-dialog-content">
                    <div class="shop-search">
                        <el-input 
                            v-model="storeSearchKeyword" 
                            placeholder="搜索渠道名称或缩写"
                            clearable
                        >
                            <template #prefix>
                                <i class="fas fa-search"></i>
                            </template>
                        </el-input>
                    </div>
                    
                    <!-- 全天班次提示信息 -->
                    <div v-if="currentEditingAnchorIndex >= 0 && currentEditingSubRowIndex >= 0 && scheduleForm.anchorGroups[currentEditingAnchorIndex].subRows[currentEditingSubRowIndex].shift === 'fullday'" 
                        class="fullday-warning" 
                        style="margin: 10px 0; padding: 8px 12px; background-color: #fdf6ec; border-radius: 4px; color: #e6a23c; font-size: 14px;"
                    >
                        <i class="fas fa-exclamation-triangle" style="margin-right: 5px;"></i>
                        全天班次只能选择包含"F"的渠道缩写
                    </div>
                    
                    <!-- 非全天班次提示信息 -->
                    <div v-if="currentEditingAnchorIndex >= 0 && currentEditingSubRowIndex >= 0 && scheduleForm.anchorGroups[currentEditingAnchorIndex].subRows[currentEditingSubRowIndex].shift && scheduleForm.anchorGroups[currentEditingAnchorIndex].subRows[currentEditingSubRowIndex].shift !== 'fullday'" 
                        class="normal-shift-warning" 
                        style="margin: 10px 0; padding: 8px 12px; background-color: #f0f9eb; border-radius: 4px; color: #67c23a; font-size: 14px;"
                    >
                        <i class="fas fa-info-circle" style="margin-right: 5px;"></i>
                        早班/晚班只能选择不包含"F"的渠道缩写
                    </div>
                    
                    <div class="shop-table-container" style="overflow-x: auto;">
                        <div style="max-height: 500px; overflow-y: auto;">
                            <div class="custom-table">
                                <!-- 表头 -->
                                <div class="custom-table-header">
                                    <div class="custom-table-cell" style="width: 60px;">序号</div>
                                    <div class="custom-table-cell" style="width: 80px;">负责人</div>
                                    <div class="custom-table-cell" style="flex: 1;">店铺名称</div>
                                    <div class="custom-table-cell" style="flex: 1;">抖音缩写</div>
                                    <div class="custom-table-cell" style="flex: 1;">视频号缩写</div>
                                    <div class="custom-table-cell" style="flex: 1;">小红书缩写</div>
                                    <div class="custom-table-cell" style="flex: 1;">快手缩写</div>
                                </div>
                                
                                <!-- 表格内容 -->
                                <div v-for="(store, index) in filteredStores" 
                                    :key="index" 
                                    class="custom-table-row" 
                                    :class="{
                                        'striped': index % 2 === 1,
                                        'occupied-store': isAnyChannelOccupied(store)
                                    }"
                                >
                                    <div class="custom-table-cell" style="width: 60px;">{{ index + 1 }}</div>
                                    <div class="custom-table-cell" style="width: 80px;">{{ store.manager }}</div>
                                    <div class="custom-table-cell" style="flex: 1;">
                                        <span>{{ store.name }}</span>
                                        <span v-if="isAnyChannelOccupied(store)" class="occupied-tag">部分已占用</span>
                                    </div>
                                    <div class="custom-table-cell" style="flex: 1;">
                                        <a href="javascript:void(0)" 
                                           @click="selectStoreAbbr(store.douyin_abbr, store.name)" 
                                           class="custom-link"
                                           :class="{
                                               'disabled-link': isChannelOccupied(store.douyin_abbr),
                                               'highlight-link': (currentEditingAnchorIndex >= 0 && 
                                                                 currentEditingSubRowIndex >= 0 && 
                                                                 scheduleForm.anchorGroups[currentEditingAnchorIndex].subRows[currentEditingSubRowIndex].shift === 'fullday' && 
                                                                 store.douyin_abbr && 
                                                                 store.douyin_abbr.includes('F')) ||
                                                                (currentEditingAnchorIndex >= 0 && 
                                                                 currentEditingSubRowIndex >= 0 && 
                                                                 (scheduleForm.anchorGroups[currentEditingAnchorIndex].subRows[currentEditingSubRowIndex].shift === 'morning' || 
                                                                  scheduleForm.anchorGroups[currentEditingAnchorIndex].subRows[currentEditingSubRowIndex].shift === 'evening') && 
                                                                 store.douyin_abbr && 
                                                                 !store.douyin_abbr.includes('F'))
                                           }"
                                        >
                                            {{ store.douyin_abbr }}
                                        </a>
                                    </div>
                                    <div class="custom-table-cell" style="flex: 1;">
                                        <a href="javascript:void(0)" 
                                           @click="selectStoreAbbr(store.video_abbr, store.name)" 
                                           class="custom-link"
                                           :class="{
                                               'disabled-link': isChannelOccupied(store.video_abbr),
                                               'highlight-link': (currentEditingAnchorIndex >= 0 && 
                                                                 currentEditingSubRowIndex >= 0 && 
                                                                 scheduleForm.anchorGroups[currentEditingAnchorIndex].subRows[currentEditingSubRowIndex].shift === 'fullday' && 
                                                                 store.video_abbr && 
                                                                 store.video_abbr.includes('F')) ||
                                                                (currentEditingAnchorIndex >= 0 && 
                                                                 currentEditingSubRowIndex >= 0 && 
                                                                 (scheduleForm.anchorGroups[currentEditingAnchorIndex].subRows[currentEditingSubRowIndex].shift === 'morning' || 
                                                                  scheduleForm.anchorGroups[currentEditingAnchorIndex].subRows[currentEditingSubRowIndex].shift === 'evening') && 
                                                                 store.video_abbr && 
                                                                 !store.video_abbr.includes('F'))
                                           }"
                                        >
                                            {{ store.video_abbr }}
                                        </a>
                                    </div>
                                    <div class="custom-table-cell" style="flex: 1;">
                                        <a href="javascript:void(0)" 
                                           @click="selectStoreAbbr(store.xiaohongshu_abbr, store.name)" 
                                           class="custom-link"
                                           :class="{
                                               'disabled-link': isChannelOccupied(store.xiaohongshu_abbr),
                                               'highlight-link': (currentEditingAnchorIndex >= 0 && 
                                                                 currentEditingSubRowIndex >= 0 && 
                                                                 scheduleForm.anchorGroups[currentEditingAnchorIndex].subRows[currentEditingSubRowIndex].shift === 'fullday' && 
                                                                 store.xiaohongshu_abbr && 
                                                                 store.xiaohongshu_abbr.includes('F')) ||
                                                                (currentEditingAnchorIndex >= 0 && 
                                                                 currentEditingSubRowIndex >= 0 && 
                                                                 (scheduleForm.anchorGroups[currentEditingAnchorIndex].subRows[currentEditingSubRowIndex].shift === 'morning' || 
                                                                  scheduleForm.anchorGroups[currentEditingAnchorIndex].subRows[currentEditingSubRowIndex].shift === 'evening') && 
                                                                 store.xiaohongshu_abbr && 
                                                                 !store.xiaohongshu_abbr.includes('F'))
                                           }"
                                        >
                                            {{ store.xiaohongshu_abbr }}
                                        </a>
                                    </div>
                                    <div class="custom-table-cell" style="flex: 1;">
                                        <a href="javascript:void(0)" 
                                           @click="selectStoreAbbr(store.kuaishou_abbr, store.name)" 
                                           class="custom-link"
                                           :class="{
                                               'disabled-link': isChannelOccupied(store.kuaishou_abbr),
                                               'highlight-link': (currentEditingAnchorIndex >= 0 && 
                                                                 currentEditingSubRowIndex >= 0 && 
                                                                 scheduleForm.anchorGroups[currentEditingAnchorIndex].subRows[currentEditingSubRowIndex].shift === 'fullday' && 
                                                                 store.kuaishou_abbr && 
                                                                 store.kuaishou_abbr.includes('F')) ||
                                                                (currentEditingAnchorIndex >= 0 && 
                                                                 currentEditingSubRowIndex >= 0 && 
                                                                 (scheduleForm.anchorGroups[currentEditingAnchorIndex].subRows[currentEditingSubRowIndex].shift === 'morning' || 
                                                                  scheduleForm.anchorGroups[currentEditingAnchorIndex].subRows[currentEditingSubRowIndex].shift === 'evening') && 
                                                                 store.kuaishou_abbr && 
                                                                 !store.kuaishou_abbr.includes('F'))
                                           }"
                                        >
                                            {{ store.kuaishou_abbr }}
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <template #footer>
                    <span class="dialog-footer">
                        <el-button @click="dialogVisible.store = false">取消</el-button>
                        <el-button type="primary" @click="confirmStoreSelection">确定</el-button>
                    </span>
                </template>
            </el-dialog>
            
            <!-- 复制排班对话框 -->
            <el-dialog v-model="dialogVisible.copySchedule" title="复制排班" width="500px" :close-on-click-modal="false">
                <div class="copy-schedule-dialog">
                    <div class="source-date-info">
                        <div class="dialog-section-title">源日期</div>
                        <div class="source-date">{{ sourceDateForCopy }}</div>
                    </div>
                    
                    <div class="target-dates-section">
                        <div class="dialog-section-title">目标日期（可多选）</div>
                        <el-date-picker
                            v-model="targetDatesForCopy"
                            type="dates"
                            placeholder="选择日期"
                            format="YYYY-MM-DD"
                            value-format="YYYY-MM-DD"
                            style="width: 100%;"
                        />
                    </div>
                </div>
                <template #footer>
                    <span class="dialog-footer">
                        <el-button @click="dialogVisible.copySchedule = false">取消</el-button>
                        <el-button type="primary" @click="copySchedulesToDates">确定</el-button>
                    </span>
                </template>
            </el-dialog>
            
            <!-- 排班表格对话框 -->
            <el-dialog
                v-model="scheduleTableVisible"
                width="90%"
                destroy-on-close
                class="schedule-table-dialog"
                :close-on-click-modal="false"
                :modal-append-to-body="true"
                :append-to-body="true"
                :lock-scroll="true"
                :show-close="false"
            >
                <template #header>
                    <div class="dialog-header-content" style="width: 100%; display: flex; justify-content: space-between; align-items: center;">
                        <div style="display: flex; align-items: center;">
                            <span class="el-dialog__title" style="font-size: 16px; font-weight: bold; margin-right: 20px;">排班表格</span>
                            <div class="view-type-tabs" style="display: flex; gap: 10px;">
                                <div 
                                    class="view-type-tab" 
                                    :class="{ active: scheduleTableViewType === 'anchor' }"
                                    @click="changeScheduleTableViewType('anchor')"
                                    style="cursor: pointer; padding: 5px 15px; border-radius: 4px;"
                                >
                                    按主播查看
                                </div>
                                <div 
                                    class="view-type-tab" 
                                    :class="{ active: scheduleTableViewType === 'store' }"
                                    @click="changeScheduleTableViewType('store')"
                                    style="cursor: pointer; padding: 5px 15px; border-radius: 4px;"
                                >
                                    按店铺查看
                                </div>
                            </div>
                        </div>
                        <div class="date-selector" style="margin: 0; display: flex; align-items: center;">
                            <el-button 
                                type="default"
                                circle
                                size="small"
                                @click="changeToYesterday"
                                title="上一天"
                                style="margin-right: 5px;"
                            >
                                <i class="fas fa-chevron-left"></i>
                            </el-button>
                            <el-date-picker
                                v-model="scheduleTableDate"
                                type="date"
                                placeholder="选择日期"
                                format="YYYY-MM-DD"
                                value-format="YYYY-MM-DD"
                                @change="changeScheduleTableDate"
                                :key="scheduleTableDate"
                                style="width: 150px;"
                            ></el-date-picker>
                            <el-button 
                                type="default"
                                circle
                                size="small"
                                @click="changeToTomorrow"
                                title="下一天"
                                style="margin-left: 5px;"
                            >
                                <i class="fas fa-chevron-right"></i>
                            </el-button>
                        </div>
                        <div class="search-container" style="width: 420px; display: flex; align-items: flex-start; position: relative; min-height: 40px;">
                            <el-button
                                type="primary"
                                size="small"
                                @click="exportToExcel"
                                style="margin-right: 8px; border-radius: 4px; height: 32px;"
                                title="导出Excel"
                            >
                                <i class="fas fa-file-excel"></i> 导出
                            </el-button>
                            <el-button
                                type="success"
                                size="small"
                                @click="openScheduleDialog(scheduleTableDate)"
                                style="margin-right: 8px; border-radius: 4px; height: 32px;"
                                title="添加排班"
                            >
                                <i class="fas fa-plus"></i> 添加
                            </el-button>
                            <div style="position: relative; flex: 1; min-width: 180px; margin-bottom: 18px; flex-basis: 180px; height: 32px;">
                                <el-input
                                    v-model="tableSearchKeyword"
                                    placeholder="搜索表格内容..."
                                    clearable
                                    @input="handleTableSearch"
                                    size="small"
                                    style="width: 100%; text-align: left; border-radius: 20px; overflow: hidden; height: 32px;"
                                    class="search-input-left-align"
                                >
                                    <template #prefix>
                                        <i class="fas fa-search" style="color: #909399; margin-right: 0; margin-left: 5px;"></i>
                                    </template>
                                </el-input>
                                <div v-if="tableSearchKeyword" class="search-result-count">
                                    共找到 {{ scheduleTableData.reduce((total, item) => total + (item.schedules ? item.schedules.length : 0), 0) }} 条记录
                                </div>
                            </div>
                            <el-button
                                type="primary"
                                size="small"
                                @click="openFilterDialog"
                                style="margin-left: 8px; border-radius: 4px; height: 32px;"
                                title="过滤器"
                            >
                                <i class="fas fa-filter"></i>
                            </el-button>
                        </div>
                    </div>
                </template>
                
                <!-- 移除原来的header部分 -->
                <div class="schedule-table-content">
                    <!-- 按主播查看表格 -->
                    <div v-if="scheduleTableViewType === 'anchor' && scheduleTableData.length > 0">
                        <div class="schedule-table-container">
                            <table class="schedule-table">
                                <thead>
                                    <tr>
                                        <th class="col-seq">序号</th>
                                        <th class="col-leader">负责人</th>
                                        <th class="col-group">分组</th>
                                        <th class="col-anchor">主播</th>
                                        <th class="col-store">店铺</th>
                                        <th class="col-channel">渠道</th>
                                        <th class="col-shift">班次</th>
                                        <th class="col-start-time">开始时间</th>
                                        <th class="col-end-time">结束时间</th>
                                        <th class="col-rest">休息(h)</th>
                                        <th class="col-duration">时长(h)</th>
                                        <th class="col-room">直播间</th>
                                        <th class="col-expected">预计</th>
                                        <th class="col-live">实际</th>
                                        <th class="col-notes">备注</th>
                                        <th class="col-action">操作</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <!-- 添加连续序号计数 -->
                                    <template v-for="(anchor, anchorIndex) in scheduleTableData" :key="anchor.anchor_id">
                                        <template v-for="(schedule, scheduleIndex) in anchor.schedules" :key="schedule.id">
                                            <!-- 使用计算属性或表达式计算连续序号 -->
                                            <tr>
                                                <!-- 修改序号计算逻辑，使用连续序号 -->
                                                <td class="col-seq">
                                                    {{ 
                                                        scheduleTableData.slice(0, anchorIndex).reduce((sum, a) => sum + a.schedules.length, 0) + scheduleIndex + 1 
                                                    }}
                                                </td>
                                                
                                                <!-- 负责人单元格，只在每个主播的第一行显示，并合并单元格 -->
                                                <td v-if="scheduleIndex === 0" :rowspan="anchor.schedules.length" class="col-leader">{{ anchor.leader }}</td>
                                                
                                                <!-- 分组单元格，只在每个主播的第一行显示，并合并单元格 -->
                                                <td v-if="scheduleIndex === 0" :rowspan="anchor.schedules.length" class="col-group">
                                                    <span 
                                                        v-if="anchor.group" 
                                                        class="group-tag"
                                                        :class="'group-' + anchor.group.replace(/[^A-Za-z0-9]/g, '').toLowerCase()"
                                                    >
                                                        {{ anchor.group }}
                                                    </span>
                                                </td>
                                                
                                                <!-- 其余保持不变 -->
                                                <!-- 主播单元格，只在每个主播的第一行显示，并合并单元格 -->
                                                <td v-if="scheduleIndex === 0" :rowspan="anchor.schedules.length" class="col-anchor">{{ anchor.anchor_name }}</td>
                                                
                                                <!-- 店铺名称修复 -->
                                                <td class="col-store">{{ getStoreName(schedule.store_id) || '未知店铺' }}</td>
                                                <td class="col-channel">{{ schedule.channel_id }}</td>
                                                <td class="col-shift">{{ schedule.shift === 'morning' ? '早班' : (schedule.shift === 'evening' ? '晚班' : '全天') }}</td>
                                                <td class="col-start-time">{{ formatTime(schedule.start_time) }}</td>
                                                <td class="col-end-time">{{ formatTime(schedule.end_time) }}</td>
                                                <td class="col-rest">{{ schedule.rest_duration }}h</td>
                                                <td class="col-duration">{{ schedule.duration }}</td>
                                                <td class="col-room">{{ schedule.room }}</td>
                                                <td class="col-expected">{{ schedule.expected_count }}</td>
                                                <td class="col-live">{{ schedule.real_count }}</td>
                                                <td class="col-notes">{{ schedule.notes }}</td>
                                                <td class="col-action table-action-cell">
                                                    <el-button
                                                        type="primary"
                                                        size="small"
                                                        class="table-action-btn"
                                                        @click="openScheduleDialog(scheduleTableDate, schedule)"
                                                    >
                                                        编辑
                                                    </el-button>
                                                    <el-button
                                                        type="danger"
                                                        size="small"
                                                        class="table-action-btn"
                                                        @click="deleteSchedule(schedule)"
                                                    >
                                                        删除
                                                    </el-button>
                                                </td>
                                            </tr>
                                        </template>
                                    </template>
                                </tbody>
                            </table>
                        </div>
                    </div>
                    
                    <!-- 按店铺查看表格 -->
                    <div v-if="scheduleTableViewType === 'store' && scheduleTableData.length > 0">
                        <div class="schedule-table-container">
                            <table class="schedule-table">
                                <thead>
                                    <tr>
                                        <th class="col-seq">序号</th>
                                        <th class="col-leader">负责人</th>
                                        <th class="col-store">店铺</th>
                                        <th class="col-channel">渠道</th>
                                        <th class="col-anchor">主播</th>
                                        <th class="col-shift">班次</th>
                                        <th class="col-start-time">开始时间</th>
                                        <th class="col-end-time">结束时间</th>
                                        <th class="col-rest">休息(h)</th>
                                        <th class="col-duration">时长(h)</th>
                                        <th class="col-room">直播间</th>
                                        <th class="col-expected">预计</th>
                                        <th class="col-live">实际</th>
                                        <th class="col-notes">备注</th>
                                        <th class="col-action">操作</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <template v-for="(store, storeIndex) in scheduleTableData" :key="store.store_id">
                                        <template v-for="(schedule, scheduleIndex) in store.schedules" :key="schedule.id">
                                            <tr>
                                                <!-- 修改序号计算逻辑，使用连续序号 -->
                                                <td class="col-seq">
                                                    {{ 
                                                        scheduleTableData.slice(0, storeIndex).reduce((sum, s) => sum + s.schedules.length, 0) + scheduleIndex + 1 
                                                    }}
                                                </td>
                                                
                                                <!-- 负责人单元格合并 - 使用managerRowSpan来控制合并 -->
                                                <td v-if="scheduleIndex === 0 && store.isFirstInManagerGroup" 
                                                    :rowspan="store.managerRowSpan" 
                                                    class="col-leader">
                                                    {{ store.manager }}
                                                </td>
                                                
                                                <!-- 店铺单元格合并 -->
                                                <td v-if="scheduleIndex === 0" :rowspan="store.schedules.length" class="col-store">{{ store.store_name }}</td>
                                                
                                                <td class="col-channel">{{ schedule.channel_id }}</td>
                                                <td class="col-anchor">{{ getAnchorName(schedule.anchor_id) }}</td>
                                                <td class="col-shift">{{ schedule.shift === 'morning' ? '早班' : (schedule.shift === 'evening' ? '晚班' : '全天') }}</td>
                                                <td class="col-start-time">{{ formatTime(schedule.start_time) }}</td>
                                                <td class="col-end-time">{{ formatTime(schedule.end_time) }}</td>
                                                <td class="col-rest">{{ schedule.rest_duration }}h</td>
                                                <td class="col-duration">{{ schedule.duration }}</td>
                                                <td class="col-room">{{ schedule.room }}</td>
                                                <td class="col-expected">{{ schedule.expected_count }}</td>
                                                <td class="col-live">{{ schedule.real_count }}</td>
                                                <td class="col-notes">{{ schedule.notes }}</td>
                                                <td class="col-action table-action-cell">
                                                    <el-button
                                                        type="primary"
                                                        size="small"
                                                        class="table-action-btn"
                                                        @click="openScheduleDialog(scheduleTableDate, schedule)"
                                                    >
                                                        编辑
                                                    </el-button>
                                                    <el-button
                                                        type="danger"
                                                        size="small"
                                                        class="table-action-btn"
                                                        @click="deleteSchedule(schedule)"
                                                    >
                                                        删除
                                                    </el-button>
                                                </td>
                                            </tr>
                                        </template>
                                    </template>
                                </tbody>
                            </table>
                        </div>
                    </div>
                    
                    <!-- 无数据提示 -->
                    <el-empty v-if="scheduleTableData.length === 0" description="暂无排班数据"></el-empty>
                </div>
                
                <template #footer>
                    <div style="display: flex; justify-content: space-between; align-items: center; width: 100%;">
                        <div class="filter-info-container" style="display: flex; align-items: center;">
                            <div v-if="hasActiveFilters" class="active-filters">
                                <span class="filter-tag">已应用过滤器</span>
                                <el-button
                                    type="text"
                                    size="small"
                                    @click="clearAllFilters"
                                    style="color: #f56c6c; margin-left: 10px;"
                                >
                                    清除全部
                                </el-button>
                            </div>
                        </div>
                        <span class="dialog-footer">
                            <el-button @click="scheduleTableVisible = false">关闭</el-button>
                        </span>
                    </div>
                </template>
            </el-dialog>

            <!-- 过滤器对话框 -->
            <el-dialog
                v-model="filterDialogVisible"
                :title="`${scheduleTableViewType === 'anchor' ? '按主播查看' : '按店铺查看'}过滤器`"
                width="600px"
                destroy-on-close
                class="filter-dialog"
                :close-on-click-modal="false"
            >
                <!-- 按主播查看的过滤器面板 -->
                <div v-if="scheduleTableViewType === 'anchor'" class="filter-panel">
                    <div class="filter-row">
                        <div class="filter-item">
                            <div class="filter-label">负责人</div>
                            <el-select
                                v-model="filters.anchor.leader"
                                placeholder="选择负责人"
                                clearable
                                filterable
                                multiple
                                style="width: 100%;"
                            >
                                <el-option
                                    v-for="leader in leaderOptions"
                                    :key="leader"
                                    :label="leader"
                                    :value="leader"
                                />
                            </el-select>
                        </div>
                        <div class="filter-item">
                            <div class="filter-label">分组</div>
                            <el-select
                                v-model="filters.anchor.group"
                                placeholder="选择分组"
                                clearable
                                filterable
                                multiple
                                style="width: 100%;"
                            >
                                <el-option
                                    v-for="group in groupOptions"
                                    :key="group"
                                    :label="group"
                                    :value="group"
                                />
                            </el-select>
                        </div>
                    </div>
                    <div class="filter-row">
                        <div class="filter-item">
                            <div class="filter-label">主播</div>
                            <el-select
                                v-model="filters.anchor.anchor_name"
                                placeholder="选择主播"
                                clearable
                                filterable
                                multiple
                                style="width: 100%;"
                            >
                                <el-option
                                    v-for="anchor in anchorOptions"
                                    :key="anchor"
                                    :label="anchor"
                                    :value="anchor"
                                />
                            </el-select>
                        </div>
                        <div class="filter-item">
                            <div class="filter-label">班次</div>
                            <el-select
                                v-model="filters.anchor.shift"
                                placeholder="选择班次"
                                clearable
                                multiple
                                style="width: 100%;"
                            >
                                <el-option key="morning" label="早班" value="morning" />
                                <el-option key="evening" label="晚班" value="evening" />
                                <el-option key="fullday" label="全天" value="fullday" />
                            </el-select>
                        </div>
                    </div>
                    <div class="filter-row">
                        <div class="filter-item">
                            <div class="filter-label">店铺</div>
                            <el-select
                                v-model="filters.anchor.store_name"
                                placeholder="选择店铺"
                                clearable
                                filterable
                                multiple
                                style="width: 100%;"
                            >
                                <el-option
                                    v-for="store in storeOptions"
                                    :key="store"
                                    :label="store"
                                    :value="store"
                                />
                            </el-select>
                        </div>
                        <div class="filter-item">
                            <div class="filter-label">渠道</div>
                            <el-select
                                v-model="filters.anchor.channel_id"
                                placeholder="选择渠道"
                                clearable
                                filterable
                                multiple
                                style="width: 100%;"
                            >
                                <el-option
                                    v-for="channel in channelOptions"
                                    :key="channel"
                                    :label="channel"
                                    :value="channel"
                                />
                            </el-select>
                        </div>
                    </div>
                </div>

                <!-- 按店铺查看的过滤器面板 -->
                <div v-if="scheduleTableViewType === 'store'" class="filter-panel">
                    <div class="filter-row">
                        <div class="filter-item">
                            <div class="filter-label">负责人</div>
                            <el-select
                                v-model="filters.store.manager"
                                placeholder="选择负责人"
                                clearable
                                filterable
                                multiple
                                style="width: 100%;"
                            >
                                <el-option
                                    v-for="manager in managerOptions"
                                    :key="manager"
                                    :label="manager"
                                    :value="manager"
                                />
                            </el-select>
                        </div>
                        <div class="filter-item">
                            <div class="filter-label">店铺</div>
                            <el-select
                                v-model="filters.store.store_name"
                                placeholder="选择店铺"
                                clearable
                                filterable
                                multiple
                                style="width: 100%;"
                            >
                                <el-option
                                    v-for="store in storeOptions"
                                    :key="store"
                                    :label="store"
                                    :value="store"
                                />
                            </el-select>
                        </div>
                    </div>
                    <div class="filter-row">
                        <div class="filter-item">
                            <div class="filter-label">主播</div>
                            <el-select
                                v-model="filters.store.anchor_name"
                                placeholder="选择主播"
                                clearable
                                filterable
                                multiple
                                style="width: 100%;"
                            >
                                <el-option
                                    v-for="anchor in anchorOptions"
                                    :key="anchor"
                                    :label="anchor"
                                    :value="anchor"
                                />
                            </el-select>
                        </div>
                        <div class="filter-item">
                            <div class="filter-label">班次</div>
                            <el-select
                                v-model="filters.store.shift"
                                placeholder="选择班次"
                                clearable
                                multiple
                                style="width: 100%;"
                            >
                                <el-option key="morning" label="早班" value="morning" />
                                <el-option key="evening" label="晚班" value="evening" />
                                <el-option key="fullday" label="全天" value="fullday" />
                            </el-select>
                        </div>
                    </div>
                    <div class="filter-row">
                        <div class="filter-item">
                            <div class="filter-label">渠道</div>
                            <el-select
                                v-model="filters.store.channel_id"
                                placeholder="选择渠道"
                                clearable
                                filterable
                                multiple
                                style="width: 100%;"
                            >
                                <el-option
                                    v-for="channel in channelOptions"
                                    :key="channel"
                                    :label="channel"
                                    :value="channel"
                                />
                            </el-select>
                        </div>
                    </div>
                </div>
                
                <template #footer>
                    <span class="dialog-footer">
                        <el-button @click="cancelFilter">取消</el-button>
                        <el-button type="primary" @click="applyFilter">应用</el-button>
                    </span>
                </template>
            </el-dialog>
        </div>
    </div>
    {% endraw %}
    
    <!-- 引入Vue和Element Plus -->
    <script src="/static/js/vue.global.js"></script>
    <script src="/static/js/element-plus.js"></script>

    <script>
        // 资源加载检测和Loading控制
        class LoadingManager {
            constructor() {
                this.loadingOverlay = document.getElementById('loading-overlay');
                this.loadingStatus = document.getElementById('loading-status');
                this.resources = new Map();
                this.loadingMessages = [
                    '正在加载静态资源',
                    '正在初始化Vue框架',
                    '正在加载Element Plus组件',
                    '正在准备用户界面',
                    '即将完成加载'
                ];
                this.currentMessageIndex = 0;
                this.startTime = performance.now();
            }

            // 添加需要检测的资源
            addResource(name, checkFunction) {
                this.resources.set(name, {
                    check: checkFunction,
                    loaded: false
                });
            }

            // 更新加载状态文本
            updateLoadingMessage() {
                if (this.currentMessageIndex < this.loadingMessages.length - 1) {
                    this.currentMessageIndex++;
                    this.loadingStatus.textContent = this.loadingMessages[this.currentMessageIndex];
                }
            }

            // 等待单个资源加载
            waitForResource(checkFunction, resourceName, timeout = 5000) {
                return new Promise((resolve, reject) => {
                    const startTime = Date.now();

                    const check = () => {
                        if (checkFunction()) {
                            console.log(`✅ ${resourceName} 已加载`);
                            resolve(resourceName);
                        } else if (Date.now() - startTime > timeout) {
                            console.error(`❌ ${resourceName} 加载超时`);
                            reject(new Error(`${resourceName} 加载超时`));
                        } else {
                            setTimeout(check, 50);
                        }
                    };

                    check();
                });
            }

            // 开始加载检测
            async startLoading() {
                try {
                    console.log('🚀 开始检测资源加载状态...');

                    // 检测Vue.js
                    this.updateLoadingMessage();
                    await this.waitForResource(() => typeof Vue !== 'undefined', 'Vue.js');

                    // 检测Element Plus
                    this.updateLoadingMessage();
                    await this.waitForResource(() => typeof ElementPlus !== 'undefined', 'Element Plus');

                    // 检测XLSX（如果需要）
                    this.updateLoadingMessage();
                    await this.waitForResource(() => typeof XLSX !== 'undefined', 'XLSX');

                    // 额外等待一点时间确保所有资源完全就绪
                    this.updateLoadingMessage();
                    await new Promise(resolve => setTimeout(resolve, 300));

                    const loadingTime = performance.now() - this.startTime;
                    console.log(`✅ 所有资源加载完成，耗时: ${loadingTime.toFixed(2)}ms`);

                    // 隐藏loading并初始化应用
                    this.hideLoading();
                    this.initializeApp();

                } catch (error) {
                    console.error('❌ 资源加载失败:', error);
                    this.showError(error.message);
                }
            }

            // 隐藏loading动画
            hideLoading() {
                this.loadingOverlay.classList.add('fade-out');
                setTimeout(() => {
                    this.loadingOverlay.style.display = 'none';
                }, 500);
            }

            // 显示错误信息
            showError(message) {
                this.loadingStatus.textContent = `加载失败: ${message}`;
                this.loadingOverlay.style.background = 'linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%)';

                // 5秒后尝试重新加载
                setTimeout(() => {
                    location.reload();
                }, 5000);
            }

            // 初始化Vue应用
            initializeApp() {
                console.log('🎉 开始初始化Vue应用...');

                // 由于schedule.js中的Vue应用会在脚本加载时自动执行
                // 我们只需要确保DOM已经准备好，然后让Vue应用挂载
                // Vue应用会自动挂载到#app元素上

                // 等待一个微任务周期，确保所有脚本都已执行
                setTimeout(() => {
                    console.log('Vue应用初始化完成');

                    // 检查Vue应用是否成功挂载
                    const appElement = document.getElementById('app');
                    if (appElement && appElement.__vue_app__) {
                        console.log('✅ Vue应用已成功挂载');
                    } else {
                        console.log('ℹ️ Vue应用正在挂载中...');
                    }
                }, 100);
            }
        }

        // 页面加载完成后开始检测
        document.addEventListener('DOMContentLoaded', function() {
            const loadingManager = new LoadingManager();
            loadingManager.startLoading();
        });
    </script>

    <!-- 引入自定义脚本 -->
    <script src="/static/js/schedule.js"></script>
</body>
</html>