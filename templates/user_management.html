<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>用户管理</title>
    <link href="/static/css/element-plus.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        body {
            margin: 0;
            padding: 0;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
            background-color: #f5f7fa;
        }

        .container {
            padding: 20px;
        }

        .header {
            margin-bottom: 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .header h1 {
            margin: 0;
            color: #303133;
            font-size: 24px;
        }

        .tab-buttons {
            display: flex;
            gap: 10px;
        }

        .tab-button {
            padding: 8px 16px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
            font-weight: bold;
            transition: all 0.3s;
            background-color: #f4f4f5;
            color: #606266;
        }

        .tab-button.active {
            background-color: #409eff;
            color: white;
        }

        .tab-button:hover {
            opacity: 0.8;
        }

        .content {
            background: #fff;
            padding: 20px;
            border-radius: 4px;
            box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
        }

        .user-table {
            width: 100%;
            border-collapse: collapse;
        }

        .user-table th,
        .user-table td {
            padding: 12px;
            text-align: center;
            border-bottom: 1px solid #ebeef5;
        }

        .action-buttons {
            display: flex;
            gap: 10px;
            justify-content: center;
            align-items: center;
        }

        .approve-btn,
        .reject-btn {
            padding: 8px 20px;
            border-radius: 6px;
            cursor: pointer;
            border: none;
            font-size: 14px;
            font-weight: 500;
            letter-spacing: 0.5px;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            position: relative;
            overflow: hidden;
        }

        .approve-btn {
            background: linear-gradient(135deg, #34c759 0%, #28a745 100%);
            color: white;
            box-shadow: 0 4px 15px rgba(40, 167, 69, 0.2);
        }

        .approve-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(40, 167, 69, 0.3);
            background: linear-gradient(135deg, #3dd763 0%, #2dbc4e 100%);
        }

        .approve-btn:active {
            transform: translateY(0);
            box-shadow: 0 2px 10px rgba(40, 167, 69, 0.2);
        }

        .approve-btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(120deg, transparent, rgba(255, 255, 255, 0.3), transparent);
            transition: 0.5s;
        }

        .approve-btn:hover::before {
            left: 100%;
        }

        .reject-btn {
            background: linear-gradient(135deg, #ff4757 0%, #dc3545 100%);
            color: white;
            box-shadow: 0 4px 15px rgba(220, 53, 69, 0.2);
        }

        .reject-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(220, 53, 69, 0.3);
            background: linear-gradient(135deg, #ff5c6c 0%, #e84c59 100%);
        }

        .reject-btn:active {
            transform: translateY(0);
            box-shadow: 0 2px 10px rgba(220, 53, 69, 0.2);
        }

        .reject-btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(120deg, transparent, rgba(255, 255, 255, 0.3), transparent);
            transition: 0.5s;
        }

        .reject-btn:hover::before {
            left: 100%;
        }

        .role-select {
            padding: 8px 12px;
            font-size: 14px;
            border: 1px solid #dcdfe6;
            border-radius: 4px;
            background-color: #fff;
            color: #606266;
            width: 120px;
            cursor: pointer;
            transition: all 0.3s;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        .role-select:hover {
            border-color: #409eff;
        }

        .role-select:focus {
            outline: none;
            border-color: #409eff;
            box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.2);
        }

        .role-select option {
            padding: 8px;
            font-size: 14px;
        }

        .back-home-btn,
        .permission-btn {
            padding: 8px 16px;
            background: linear-gradient(45deg, #409eff, #66b1ff);
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            box-shadow: 0 4px 8px rgba(64, 158, 255, 0.3);
            transition: all 0.3s ease;
            letter-spacing: 1px;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .back-home-btn:hover,
        .permission-btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 6px 12px rgba(64, 158, 255, 0.5);
        }

        .back-home-btn:active,
        .permission-btn:active {
            transform: translateY(0);
            box-shadow: 0 4px 8px rgba(64, 158, 255, 0.3);
        }

        .permission-btn {
            background: linear-gradient(45deg, #8e44ad, #9b59b6);
            box-shadow: 0 4px 8px rgba(142, 68, 173, 0.3);
        }

        .permission-btn:hover {
            box-shadow: 0 6px 12px rgba(142, 68, 173, 0.5);
        }

        .header-actions {
            display: flex;
            gap: 10px;
        }

        /* 模态窗口样式 */
        .modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
            z-index: 1000;
            justify-content: center;
            align-items: center;
            overflow: hidden;
        }

        .modal-content {
            background-color: #fff;
            border-radius: 12px;
            width: 1400px;
            max-width: 90%;
            max-height: 80vh;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
            display: flex;
            flex-direction: column;
            overflow: hidden;
            animation: modal-appear 0.3s ease-out;
        }

        @keyframes modal-appear {
            from {
                opacity: 0;
                transform: translateY(-40px);
            }

            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 20px 24px;
            border-bottom: 2px solid #ebeef5;
            flex-shrink: 0;
        }

        .modal-title {
            font-size: 24px;
            font-weight: 600;
            color: #303133;
        }

        .modal-close {
            background: none;
            border: none;
            font-size: 24px;
            cursor: pointer;
            color: #909399;
            transition: color 0.3s;
        }

        .modal-close:hover {
            color: #f56c6c;
        }

        .modal-body {
            flex: 1;
            overflow-y: auto;
            padding: 20px 24px;
            min-height: 200px;
        }

        .modal-footer {
            display: flex;
            justify-content: flex-end;
            gap: 16px;
            padding: 16px 24px;
            border-top: 2px solid #ebeef5;
            background-color: #fff;
            flex-shrink: 0;
        }

        .modal-btn {
            padding: 12px 24px;
            border-radius: 8px;
            cursor: pointer;
            border: none;
            font-size: 16px;
            font-weight: 500;
            transition: all 0.3s;
        }

        .modal-btn-cancel {
            background-color: #f4f4f5;
            color: #606266;
        }

        .modal-btn-cancel:hover {
            background-color: #e9e9eb;
        }

        .modal-btn-confirm {
            background: linear-gradient(135deg, #409eff, #66b1ff);
            color: white;
            box-shadow: 0 4px 12px rgba(64, 158, 255, 0.3);
        }

        .modal-btn-confirm:hover {
            background: linear-gradient(135deg, #66b1ff, #409eff);
            box-shadow: 0 6px 16px rgba(64, 158, 255, 0.4);
        }

        /* 权限管理部分样式 */
        .permission-section {
            margin-bottom: 25px;
            border: 1px solid #ebeef5;
            border-radius: 6px;
            box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
            overflow: hidden;
        }

        .permission-section:last-child {
            margin-bottom: 0;
        }

        .section-title {
            margin: 0;
            padding: 12px 15px;
            font-size: 16px;
            font-weight: 600;
            background-color: #f5f7fa;
            border-bottom: 1px solid #ebeef5;
            color: #303133;
        }

        .section-content {
            padding: 15px;
            background-color: #fff;
        }

        /* 权限项目样式 */
        .permission-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px 0;
            border-bottom: 1px dashed #ebeef5;
        }

        .permission-item:last-child {
            border-bottom: none;
        }

        .permission-label {
            flex: 1;
            font-size: 14px;
            color: #606266;
        }

        .permission-select {
            width: 200px;
            padding: 8px 12px;
            border: 1px solid #dcdfe6;
            border-radius: 4px;
            font-size: 14px;
            color: #606266;
            background-color: #fff;
            transition: border-color 0.2s;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
        }

        .permission-select:hover {
            border-color: #c0c4cc;
        }

        .permission-select:focus {
            outline: none;
            border-color: #409eff;
            box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.2);
        }

        /* 选项卡样式 */
        .permission-tabs {
            display: flex;
            margin-bottom: 20px;
            border-bottom: 2px solid #ebeef5;
        }

        .permission-tab-btn {
            padding: 10px 20px;
            margin-right: 10px;
            font-size: 16px;
            font-weight: 500;
            color: #606266;
            background: none;
            border: none;
            border-bottom: 2px solid transparent;
            cursor: pointer;
            transition: all 0.3s;
            margin-bottom: -2px;
        }

        .permission-tab-btn:hover {
            color: #409eff;
        }

        .permission-tab-btn.active {
            color: #409eff;
            border-bottom: 2px solid #409eff;
        }

        .permission-tab-content {
            margin-top: 20px;
        }

        .permission-tab-pane {
            display: none;
        }

        .permission-tab-pane.active {
            display: block;
        }

        /* 权限表格样式 */
        .permission-table-container {
            overflow-x: auto;
            margin-bottom: 20px;
            border-radius: 8px;
            border: 1px solid #ebeef5;
            box-shadow: 0 2px 12px rgba(0, 0, 0, 0.05);
            position: relative;
            /* 添加相对定位 */
        }

        .permission-table {
            width: 100%;
            border-collapse: collapse;
            border-spacing: 0;
            background-color: #fff;
        }

        .permission-table thead {
            position: sticky;
            top: 0;
            z-index: 10;
            background-color: #f5f7fa;
        }

        .permission-table th {
            font-weight: 600;
            color: #303133;
            background-color: #f5f7fa;
            border-bottom: 2px solid #dcdfe6;
            position: sticky;
            top: 0;
            z-index: 10;
        }

        /* 确保表头阴影效果 */
        .permission-table thead::after {
            content: '';
            position: absolute;
            left: 0;
            right: 0;
            bottom: 0;
            height: 2px;
            background-color: #dcdfe6;
        }

        .permission-table td {
            padding: 16px;
            text-align: center;
            border-bottom: 1px solid #ebeef5;
            white-space: nowrap;
        }

        .permission-table tr:hover {
            background-color: #f5f7fa;
        }

        .permission-table td.allow {
            color: #67c23a;
            font-weight: 500;
        }

        .permission-table td.deny {
            color: #f56c6c;
            font-weight: 500;
        }

        /* 权限按钮组样式改进 */
        .permission-buttons {
            display: flex;
            justify-content: center;
            gap: 8px;
            align-items: center;
            position: relative;
            padding: 4px;
        }

        .permission-btn-allow,
        .permission-btn-deny {
            width: 14px;
            height: 14px;
            border-radius: 50%;
            border: 1px solid #dcdfe6;
            cursor: pointer;
            transition: all 0.3s;
            position: relative;
            box-shadow: 0 1px 2px rgba(0, 0, 0, 0.08);
            padding: 0;
            display: flex;
            align-items: center;
            justify-content: center;
            box-sizing: border-box;
            background-color: #fff;
            flex-shrink: 0;
        }

        /* 三角形按钮样式 */
        .permission-btn-triangle {
            width: 0;
            height: 0;
            border-left: 6px solid transparent;
            border-right: 6px solid transparent;
            border-top: 8px solid #909399;
            cursor: pointer;
            transition: all 0.3s;
            margin-left: 8px;
            display: inline-block;
            vertical-align: middle;
        }

        .permission-btn-triangle:hover {
            border-top-color: #606266;
            transform: scale(1.1);
        }

        /* 权限弹窗样式 */
        .permission-popup {
            display: none;
            position: fixed;
            background: white;
            border-radius: 4px;
            box-shadow: 0 2px 12px rgba(0, 0, 0, 0.15);
            padding: 12px;
            min-width: 250px;
            z-index: 1000;
            margin-top: 8px;
            border: 1px solid #ebeef5;
            transform: none;
            /* 移除transform，便于拖动 */
            left: auto;
            /* 移除left定位 */
        }

        .permission-popup.show {
            display: block !important;
        }

        .permission-popup::before {
            content: '';
            position: absolute;
            top: -5px;
            left: 50%;
            transform: translateX(-50%) rotate(45deg);
            width: 10px;
            height: 10px;
            background: white;
            border-left: 1px solid #ebeef5;
            border-top: 1px solid #ebeef5;
        }

        /* 允许按钮激活状态 */
        .permission-btn-allow.active {
            background-color: #67c23a;
            border-color: #67c23a;
            box-shadow: 0 1px 5px rgba(103, 194, 58, 0.4);
        }

        /* 禁止按钮激活状态 */
        .permission-btn-deny.active {
            background-color: #f56c6c;
            border-color: #f56c6c;
            box-shadow: 0 1px 5px rgba(245, 108, 108, 0.4);
        }

        /* 按钮中心点样式 */
        .permission-btn-allow.active:after,
        .permission-btn-deny.active:after {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            width: 3px;
            height: 3px;
            background-color: white;
            border-radius: 50%;
            transform: translate(-50%, -50%);
        }

        /* 按钮悬停效果 */
        .permission-btn-allow:hover,
        .permission-btn-deny:hover {
            transform: scale(1.15);
        }

        /* 未激活状态的按钮样式 */
        .permission-btn-allow:not(.active),
        .permission-btn-deny:not(.active) {
            background-color: #fff;
            border-color: #dcdfe6;
        }

        .filter-container {
            display: flex;
            gap: 10px;
            margin-left: auto;
            margin-right: 10px;
        }

        .filter-select {
            padding: 8px 12px;
            border: 1px solid #dcdfe6;
            border-radius: 4px;
            background-color: #fff;
            color: #606266;
            min-width: 120px;
            transition: all 0.3s;
        }

        .filter-select:hover {
            border-color: #409eff;
        }

        .filter-select:focus {
            outline: none;
            border-color: #409eff;
            box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.2);
        }

        .virtual-scroll-container {
            max-height: 700px;
            overflow-y: auto;
            position: relative;
        }

        /* 固定表头 */
        .user-table thead tr th {
            position: sticky;
            top: 0;
            z-index: 10;
            background-color: #f5f7fa;
        }

        /* 骨架屏样式 */
        .skeleton-container {
            padding: 20px;
            background: #fff;
            border-radius: 4px;
            box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
        }

        .skeleton-table {
            width: 100%;
            border-collapse: collapse;
        }

        .skeleton-table th,
        .skeleton-table td {
            padding: 12px;
            text-align: center;
            border-bottom: 1px solid #ebeef5;
        }

        .skeleton-header {
            height: 24px;
            background: linear-gradient(90deg, #f2f2f2 25%, #e6e6e6 37%, #f2f2f2 63%);
            animation: skeleton-loading 1.4s ease infinite;
            background-size: 400% 100%;
            border-radius: 4px;
        }

        .skeleton-cell {
            height: 16px;
            background: linear-gradient(90deg, #f2f2f2 25%, #e6e6e6 37%, #f2f2f2 63%);
            animation: skeleton-loading 1.4s ease infinite;
            background-size: 400% 100%;
            border-radius: 4px;
            margin: 0 auto;
        }

        .skeleton-cell.name {
            width: 80px;
        }

        .skeleton-cell.department {
            width: 70px;
        }

        .skeleton-cell.group {
            width: 60px;
        }

        .skeleton-cell.time {
            width: 120px;
        }

        .skeleton-cell.type {
            width: 70px;
        }

        .skeleton-cell.role {
            width: 100px;
        }

        .skeleton-cell.action {
            width: 120px;
        }

        .skeleton-fade-out {
            animation: fade-out 0.3s forwards;
        }

        .content-fade-in {
            opacity: 0;
            animation: fade-in 0.3s forwards;
        }

        @keyframes skeleton-loading {
            0% {
                background-position: 100% 50%;
            }

            100% {
                background-position: 0 50%;
            }
        }

        @keyframes fade-out {
            from {
                opacity: 1;
            }

            to {
                opacity: 0;
                display: none;
            }
        }

        @keyframes fade-in {
            from {
                opacity: 0;
            }

            to {
                opacity: 1;
            }
        }

        /* 权限模态窗口骨架屏 */
        .skeleton-permission-table {
            width: 100%;
            border-collapse: collapse;
            background: #fff;
        }

        .skeleton-permission-cell {
            height: 14px;
            width: 14px;
            border-radius: 50%;
            background: linear-gradient(90deg, #f2f2f2 25%, #e6e6e6 37%, #f2f2f2 63%);
            animation: skeleton-loading 1.4s ease infinite;
            background-size: 400% 100%;
            margin: 0 auto;
        }

        /* 添加新样式 */
        .add-permission-btn {
            padding: 10px 20px;
            background: linear-gradient(135deg, #409eff, #66b1ff);
            color: white;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            display: flex;
            align-items: center;
            gap: 8px;
            margin: 15px auto;
            transition: all 0.3s ease;
            box-shadow: 0 4px 12px rgba(64, 158, 255, 0.2);
        }

        .add-permission-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 16px rgba(64, 158, 255, 0.3);
        }

        .add-permission-btn i {
            font-size: 16px;
        }

        .department-select {
            padding: 8px 12px;
            border: 1px solid #dcdfe6;
            border-radius: 4px;
            font-size: 14px;
            color: #606266;
            background-color: #fff;
            width: 120px;
            transition: all 0.3s;
        }

        .department-select:hover {
            border-color: #409eff;
        }

        .department-select:focus {
            outline: none;
            border-color: #409eff;
            box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.2);
        }

        /* 表头固定样式 */
        .permission-table-container {
            max-height: 600px;
            overflow-y: auto;
            position: relative;
        }

        .permission-table thead {
            position: sticky;
            top: 0;
            z-index: 20;
            background-color: #f5f7fa;
        }

        .permission-table th {
            background-color: #f5f7fa;
            box-shadow: 0 2px 3px rgba(0, 0, 0, 0.1);
        }

        /* 表格行样式优化 */
        .permission-table tbody tr:hover {
            background-color: #f0f9ff;
        }

        /* 部门字段显示样式 */
        .department-display {
            font-weight: 500;
            padding: 6px 10px;
            border-radius: 4px;
            background-color: #f0f7ff;
            color: #409eff;
            min-width: 90px;
            text-align: center;
            display: inline-block;
        }

        /* 新身份行高亮样式 */
        .new-permission-row {
            background-color: #f0fff0;
            transition: background-color 0.5s;
        }

        /* 弹窗内容样式 */
        .popup-title {
            font-size: 14px;
            font-weight: bold;
            color: #303133;
            padding-bottom: 10px;
            margin-bottom: 10px;
            border-bottom: 1px solid #ebeef5;
            cursor: move;
            /* 添加可拖动指示 */
        }

        .popup-item {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 8px 0;
            border-bottom: 1px dashed #ebeef5;
        }

        .popup-item:last-of-type {
            border-bottom: none;
        }

        .popup-item-label {
            font-size: 13px;
            color: #606266;
        }

        .popup-footer {
            margin-top: 12px;
            text-align: right;
            border-top: 1px solid #ebeef5;
            padding-top: 10px;
        }

        .popup-confirm {
            padding: 6px 12px;
            background: linear-gradient(135deg, #409eff, #66b1ff);
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 12px;
            transition: all 0.3s;
        }

        .popup-confirm:hover {
            background: linear-gradient(135deg, #66b1ff, #409eff);
            transform: translateY(-2px);
            box-shadow: 0 2px 8px rgba(64, 158, 255, 0.4);
        }

        /* 新角色栏中的三角形按钮不受影响 */
        tr.new-permission-row .permission-btn-deny.active~.permission-btn-triangle {
            border-top-color: #909399;
            cursor: pointer;
            opacity: 1;
        }

        /* 只针对主权限字段为禁止时禁用三角形按钮 */
        .permission-buttons[data-field="user_review"] .permission-btn-deny.active~.permission-btn-triangle,
        .permission-buttons[data-field="user_management"] .permission-btn-deny.active~.permission-btn-triangle,
        .permission-buttons[data-field="lead_table"] .permission-btn-deny.active~.permission-btn-triangle,
        .permission-buttons[data-field="recycle_bin"] .permission-btn-deny.active~.permission-btn-triangle,
        .permission-buttons[data-field="user_messages"] .permission-btn-deny.active~.permission-btn-triangle {
            border-top-color: #dcdfe6;
            cursor: not-allowed;
            opacity: 0.6;
        }

        /* 新角色栏中的三角形按钮不受影响 */
        tr.new-permission-row .permission-btn-triangle {
            border-top-color: #909399 !important;
            cursor: pointer !important;
            opacity: 1 !important;
        }

        /* 黄色部分允许状态 */
        .permission-btn-allow.partial {
            background-color: #f7c948 !important;
            border-color: #f7c948 !important;
            box-shadow: 0 1px 5px rgba(247, 201, 72, 0.4);
            color: #fff;
        }

        /* 新增：黄色partial状态也有白色空心圆点 */
        .permission-btn-allow.partial:after {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            width: 3px;
            /* 建议比active略大一点，更显空心 */
            height: 3px;
            background-color: #fff;
            border-radius: 50%;
            transform: translate(-50%, -50%);
        }

        /* 部门字段显示样式 */
        .department-display {
            font-weight: 500;
            padding: 6px 10px;
            border-radius: 4px;
            background-color: #f0f7ff;
            color: #409eff;
            min-width: 90px;
            text-align: center;
            display: inline-block;
        }

        /* 身份字段显示样式 */
        .identity-display {
            font-weight: 500;
            padding: 6px 10px;
            border-radius: 4px;
            background-color: #f0f7ff;
            color: #409eff;
            min-width: 90px;
            text-align: center;
            display: inline-block;
            cursor: pointer;
            /* 添加鼠标指针样式提示可点击 */
        }

        /* 身份输入框样式 */
        .identity-input {
            padding: 6px 10px;
            border: 1px solid #dcdfe6;
            border-radius: 4px;
            font-size: 14px;
            color: #606266;
            background-color: #fff;
            width: 120px;
            transition: all 0.3s;
        }

        .identity-input:hover {
            border-color: #409eff;
        }

        .identity-input:focus {
            outline: none;
            border-color: #409eff;
            box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.2);
        }

        .permission-btn:active {
            transform: translateY(0);
            box-shadow: 0 4px 8px rgba(142, 68, 173, 0.3);
        }

        .disabled-btn {
            background-color: #909399 !important;
            cursor: not-allowed !important;
            opacity: 0.6;
            box-shadow: none !important;
        }

        .disabled-btn:hover {
            transform: none !important;
            box-shadow: none !important;
            background-color: #909399 !important;
        }

        .header-actions {
            display: flex;
            gap: 10px;
        }
    </style>
</head>

<body>
    <div class="container">
        <div class="header">
            <h1>用户管理</h1>
            <div style="display: flex; justify-content: center; flex: 1; width: 100%;">
                <div class="tab-buttons" style="margin: 0;">
                    <button class="tab-button" :class="{ active: currentTab === 'normal' }"
                        @click="switchTab('normal')">前端</button>
                    <button class="tab-button" :class="{ active: currentTab === 'admin' }"
                        @click="switchTab('admin')">前端+后端</button>
                </div>
            </div>
            <div class="filter-container" v-if="currentUser && currentUser.is_admin === 0">
                <div class="filter-item">
                    <select class="filter-select" v-model="selectedDepartment" @change="applyFilters">
                        <option value="">全部部门</option>
                        <option value="总经办">总经办</option>
                        <option value="电商部">电商部</option>
                        <option value="新媒体部">新媒体部</option>
                        <option value="销售部">销售部</option>
                        <option value="技术部">技术部</option>
                        <option value="设计部">设计部</option>
                        <option value="施工部">施工部</option>
                        <option value="访客">访客</option>
                    </select>
                </div>
            </div>
            <div class="header-actions">
                <button class="permission-btn" id="permissionModalBtn">
                    <i class="fas fa-key"></i> 权限管理
                </button>
                <button class="back-home-btn" onclick="window.location.href='home'">
                    <i class="fas fa-home"></i> 返回首页
                </button>
            </div>
        </div>
        <div class="content">
            <!-- 骨架屏容器 -->
            <div id="skeletonContent" class="skeleton-container">
                <table class="skeleton-table">
                    <thead>
                        <tr>
                            <th>
                                <div class="skeleton-header"></div>
                            </th>
                            <th>
                                <div class="skeleton-header"></div>
                            </th>
                            <th>
                                <div class="skeleton-header"></div>
                            </th>
                            <th>
                                <div class="skeleton-header"></div>
                            </th>
                            <th>
                                <div class="skeleton-header"></div>
                            </th>
                            <th>
                                <div class="skeleton-header"></div>
                            </th>
                            <th>
                                <div class="skeleton-header"></div>
                            </th>
                            <th>
                                <div class="skeleton-header"></div>
                            </th>
                        </tr>
                    </thead>
                    <tbody>
                        <!-- 生成10行骨架行 -->
                        <tr v-for="i in 10" :key="i">
                            <td>
                                <div class="skeleton-cell" style="width: 30px;"></div>
                            </td>
                            <td>
                                <div class="skeleton-cell name"></div>
                            </td>
                            <td>
                                <div class="skeleton-cell department"></div>
                            </td>
                            <td>
                                <div class="skeleton-cell group"></div>
                            </td>
                            <td>
                                <div class="skeleton-cell time"></div>
                            </td>
                            <td>
                                <div class="skeleton-cell type"></div>
                            </td>
                            <td>
                                <div class="skeleton-cell role"></div>
                            </td>
                            <td>
                                <div class="skeleton-cell action"></div>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>

            <!-- 实际内容，初始隐藏 -->
            <div id="realContent" class="content" style="visibility: hidden;">
                <div class="virtual-scroll-container" id="userTableContainer">
                    <table class="user-table">
                        <thead>
                            <tr>
                                <th>序号</th>
                                <th>姓名</th>
                                <th>部门</th>
                                <th>分组</th>
                                <th>注册时间</th>
                                <th>用户类型</th>
                                <th>变更类型</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody id="userTableBody">
                            <!-- 用户数据将通过JavaScript动态插入 -->
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <!-- 权限管理模态窗口 -->
    <div class="modal" id="permissionModal">
        <div class="modal-content">
            <div class="modal-header">
                <div class="modal-title">权限管理</div>
                <button class="modal-close" onclick="app._instance.proxy.closePermissionModal()">&times;</button>
            </div>
            <div
                style="display: flex; justify-content: space-between; align-items: center; padding: 0 24px; border-bottom: 1px solid #ebeef5;">
                <!-- 左侧选项卡 -->
                <div class="permission-tabs" style="margin-bottom: 0; border-bottom: none;">
                    <button class="permission-tab-btn active"
                        onclick="app._instance.proxy.switchPermissionTab('management')">后端</button>
                    <button class="permission-tab-btn"
                        onclick="app._instance.proxy.switchPermissionTab('distribution')">前端</button>
                </div>

                <!-- 右侧过滤器 -->
                <div class="filter-container" id="permFilterContainer" style="display: none;">
                    <div class="filter-item">
                        <select class="filter-select" id="permDepartmentFilter" onchange="handlePermFilterChange(this)">
                            <option value="">全部部门</option>
                            <option value="总经办">总经办</option>
                            <option value="电商部">电商部</option>
                            <option value="新媒体部">新媒体部</option>
                            <option value="销售部">销售部</option>
                            <option value="技术部">技术部</option>
                            <option value="设计部">设计部</option>
                            <option value="施工部">施工部</option>
                            <option value="访客">访客</option>
                        </select>
                    </div>
                </div>
            </div>
            <div class="modal-body" style="padding-top: 0;">
                <!-- 权限骨架屏 -->
                <div id="permissionSkeleton" class="permission-tab-content" style="margin-top: 0;">
                    <div class="permission-tab-pane active">
                        <div class="permission-section" style="margin-top: 0;">
                            <div class="section-content">
                                <div class="permission-table-container">
                                    <table class="skeleton-permission-table">
                                        <thead>
                                            <tr>
                                                <th>
                                                    <div class="skeleton-header"></div>
                                                </th>
                                                <th>
                                                    <div class="skeleton-header"></div>
                                                </th>
                                                <th>
                                                    <div class="skeleton-header"></div>
                                                </th>
                                                <th>
                                                    <div class="skeleton-header"></div>
                                                </th>
                                                <th>
                                                    <div class="skeleton-header"></div>
                                                </th>
                                                <th>
                                                    <div class="skeleton-header"></div>
                                                </th>
                                                <th>
                                                    <div class="skeleton-header"></div>
                                                </th>
                                                <th>
                                                    <div class="skeleton-header"></div>
                                                </th>
                                                <th>
                                                    <div class="skeleton-header"></div>
                                                </th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <!-- 生成8行骨架行 -->
                                            <tr v-for="i in 8" :key="i">
                                                <td>
                                                    <div class="skeleton-cell" style="width: 30px;"></div>
                                                </td>
                                                <td>
                                                    <div class="skeleton-cell name"></div>
                                                </td>
                                                <td>
                                                    <div class="skeleton-cell department"></div>
                                                </td>
                                                <td>
                                                    <div class="skeleton-cell group"></div>
                                                </td>
                                                <td>
                                                    <div class="skeleton-cell type"></div>
                                                </td>
                                                <td>
                                                    <div class="skeleton-permission-cell"></div>
                                                </td>
                                                <td>
                                                    <div class="skeleton-permission-cell"></div>
                                                </td>
                                                <td>
                                                    <div class="skeleton-permission-cell"></div>
                                                </td>
                                                <td>
                                                    <div class="skeleton-permission-cell"></div>
                                                </td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 实际权限内容 -->
                <div id="permissionContent" class="modal-body" style="display: none; padding-top: 0;">
                    <!-- 选项卡导航 -->
                    <div class="permission-tab-content" style="margin-top: 0;">
                        <!-- 后端选项卡 -->
                        <div class="permission-tab-pane" id="managementTab">
                            <div class="permission-section" style="margin-top: 0;">
                                <div class="section-content">
                                    <div class="permission-table-container">
                                        <table class="permission-table">
                                            <thead>
                                                <tr>
                                                    <th>序号</th>
                                                    <th>部门</th>
                                                    <th>身份</th>
                                                    <th>用户审核</th>
                                                    <th>用户管理</th>
                                                    <th>表单预设</th>
                                                    <th>排班管理</th>
                                                    <th>分发规则</th>
                                                    <th>分发计划</th>
                                                    <th>操作</th>
                                                </tr>
                                            </thead>
                                            <tbody id="backendPermissionsBody">
                                                <template id="backendPermissionRowTemplate">
                                                    <tr>
                                                        <td class="permission-index"></td>
                                                        <td class="department-cell"></td>
                                                        <td class="identity-cell"></td>
                                                        <td>
                                                            <div class="permission-buttons" style="position: relative;">
                                                                <button class="permission-btn-allow"
                                                                    onclick="togglePermission(this, 'user_review')"></button>
                                                                <button class="permission-btn-deny"
                                                                    onclick="togglePermission(this, 'user_review')"></button>
                                                                <div class="permission-btn-triangle"
                                                                    onclick="togglePopup(this)"></div>
                                                                <div class="permission-popup">
                                                                    <!-- 弹窗内容将来添加 -->
                                                                </div>
                                                            </div>
                                                        </td>
                                                        <td>
                                                            <div class="permission-buttons" style="position: relative;">
                                                                <button class="permission-btn-allow"
                                                                    onclick="togglePermission(this, 'user_management')"></button>
                                                                <button class="permission-btn-deny"
                                                                    onclick="togglePermission(this, 'user_management')"></button>
                                                                <div class="permission-btn-triangle"
                                                                    onclick="togglePopup(this)"></div>
                                                                <div class="permission-popup">
                                                                    <!-- 弹窗内容将来添加 -->
                                                                </div>
                                                            </div>
                                                        </td>
                                                        <td>
                                                            <div class="permission-buttons">
                                                                <button class="permission-btn-allow"
                                                                    onclick="togglePermission(this, 'form_preset')"></button>
                                                                <button class="permission-btn-deny"
                                                                    onclick="togglePermission(this, 'form_preset')"></button>
                                                            </div>
                                                        </td>
                                                        <td>
                                                            <div class="permission-buttons">
                                                                <button class="permission-btn-allow"
                                                                    onclick="togglePermission(this, 'schedule_management')"></button>
                                                                <button class="permission-btn-deny"
                                                                    onclick="togglePermission(this, 'schedule_management')"></button>
                                                            </div>
                                                        </td>
                                                        <td>
                                                            <div class="permission-buttons">
                                                                <button class="permission-btn-allow"
                                                                    onclick="togglePermission(this, 'distribution_rules')"></button>
                                                                <button class="permission-btn-deny"
                                                                    onclick="togglePermission(this, 'distribution_rules')"></button>
                                                            </div>
                                                        </td>
                                                        <td>
                                                            <div class="permission-buttons">
                                                                <button class="permission-btn-allow"
                                                                    onclick="togglePermission(this, 'distribution_plan')"></button>
                                                                <button class="permission-btn-deny"
                                                                    onclick="togglePermission(this, 'distribution_plan')"></button>
                                                            </div>
                                                        </td>
                                                        <td>
                                                            <button class="delete-btn"
                                                                onclick="deletePermission(this)">删除</button>
                                                        </td>
                                                    </tr>
                                                </template>
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <!-- 前端选项卡 -->
                        <div class="permission-tab-pane" id="distributionTab">
                            <div class="permission-section">
                                <div class="section-content">
                                    <div class="permission-table-container">
                                        <table class="permission-table">
                                            <thead>
                                                <tr>
                                                    <th class="id-column">序号</th>
                                                    <th>部门</th>
                                                    <th>身份</th>
                                                    <th>查看线索页</th>
                                                    <th>提交线索页</th>
                                                    <th>客户关系管理</th>
                                                    <th>公海客情管理</th>
                                                    <th>回收站页</th>
                                                    <th>用户消息页</th>
                                                    <th>审批管理</th>
                                                    <th>操作</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                <!-- 用户前端权限数据将通过JavaScript动态插入 -->
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button class="modal-btn modal-btn-cancel"
                    onclick="document.getElementById('permissionModal').style.display = 'none';">取消</button>
                <button class="modal-btn modal-btn-confirm" onclick="saveAllPermissions()">保存</button>
            </div>
        </div>
    </div>
    <script src="/static/js/fetch.js"></script>
    <script src="/static/js/vue.global.js"></script>
    <script src="/static/js/element-plus.js"></script>
    <script>
        const app = Vue.createApp({
            data() {
                return {
                    users: [],
                    currentTab: 'normal',
                    userPermissionsBackend: [],
                    userPermissionsFrontend: [],
                    groupList: [],
                    selectedDepartment: '',
                    selectedGroup: '',
                    permSelectedDepartment: '',
                    permSelectedGroup: '',
                    currentUser: null,
                    allUsers: [],
                    allUserPermissionsBackend: [],
                    allUserPermissionsFrontend: [],
                    virtualScroll: {
                        itemHeight: 54,
                        visibleCount: 20,
                        startIndex: 0,
                        endIndex: 0,
                        bufferCount: 5,
                        scrollTimer: null
                    },
                    isLoading: true,
                    isPermissionLoading: true,
                    // 添加用户管理权限数据
                    userManagementPermissions: {
                        manage_all: 1, 
                        manage_group: 1, 
                        permission_manage: 1
                    }
                }
            },
            computed: {
                sortedGroups() {
                    return [...this.groupList].sort();
                },

                filteredUsers() {
                    let filtered = [...this.users];
                    
                    // 先应用用户选择的筛选条件
                    if (this.selectedDepartment) {
                        filtered = filtered.filter(user => user.department === this.selectedDepartment);
                    }
                    
                    if (this.selectedGroup) {
                        filtered = filtered.filter(user => {
                            if (this.selectedGroup === 'empty') {
                                return !user.group_name || user.group_name === '';
                            }
                            return user.group_name === this.selectedGroup;
                        });
                    }
                    
                    // 再根据当前选中的标签进行筛选
                    return filtered.filter(user => {
                        if (this.currentTab === 'normal') {
                            return user.is_admin === 2;
                        } else if (this.currentTab === 'admin') {
                            return user.is_admin === 1;
                        }
                        return false;
                    });
                },

                // 后端权限过滤数据
                filteredBackendPermissions() {
                    return this.applyFiltersToData(this.allUserPermissionsBackend, this.permSelectedDepartment, this.permSelectedGroup);
                },

                // 前端权限过滤数据
                filteredFrontendPermissions() {
                    return this.applyFiltersToData(this.allUserPermissionsFrontend, this.permSelectedDepartment, this.permSelectedGroup);
                }
            },
            methods: {
                async fetchActiveUsers() {
                    try {
                        const response = await fetch('/api/active-users');
                        if (response.ok) {
                            this.allUsers = await response.json();
                            
                            // 根据权限过滤用户数据
                            if (this.currentUser && this.currentUser.is_admin === 0) {
                                // 超级管理员可以查看所有数据
                                this.users = [...this.allUsers];
                                console.log('超级管理员权限，加载所有用户:', this.users.length);
                            } else if (this.userManagementPermissions.manage_all === 0) {
                                // 用户拥有查看所有用户的权限，但默认只显示同部门的
                                if (this.currentUser && this.currentUser.department) {
                                    // 默认按部门筛选，但保留所有数据以便用户可以通过筛选器查看其他部门
                                    this.users = [...this.allUsers];
                                    console.log('manage_all=0权限，默认显示同部门用户，但已加载所有用户数据');
                                } else {
                                    this.users = [...this.allUsers];
                                    console.log('manage_all=0权限，加载所有用户:', this.users.length);
                                }
                            } else if (this.userManagementPermissions.manage_group === 0) {
                                // 用户只能查看同组用户数据
                                if (this.currentUser && this.currentUser.group_name) {
                                    console.log('管理员组名:', this.currentUser.group_name);
                                    this.users = this.allUsers.filter(user => {
                                        // 同时满足同部门和同组的条件
                                        return user.group_name === this.currentUser.group_name && 
                                               user.department === this.currentUser.department;
                                    });
                                    console.log('manage_group=0权限，加载同组同部门用户:', this.users.length, 
                                                '用户组名:', this.currentUser.group_name,
                                                '部门:', this.currentUser.department);
                                } else {
                                    // 如果当前用户没有组，则按部门筛选
                                    if (this.currentUser && this.currentUser.department) {
                                        this.users = this.allUsers.filter(user => 
                                            user.department === this.currentUser.department
                                        );
                                        console.log('当前用户没有组名，仅按部门筛选:', this.currentUser.department);
                                    } else {
                                        this.users = [];
                                        console.log('当前用户没有组名和部门，不显示任何数据');
                                    }
                                }
                            } else {
                                // 没有权限
                                this.users = [];
                                console.log('无管理权限，不显示任何数据');
                            }
                            
                            this.renderUserTable();
                            this.isLoading = false;

                            // 切换显示内容
                            this.showContent();
                        }
                    } catch (error) {
                        console.error('获取用户列表失败:', error);
                        this.isLoading = false;
                        this.showContent();
                    }
                },

                // 获取后端用户权限数据
                async fetchUserPermissionsBackend() {
                    try {
                        const response = await fetch('/api/user-permissions-backend');
                        if (!response.ok) {
                            throw new Error('获取用户后端权限数据失败');
                        }

                        // 直接使用所有数据，不进行过滤
                        const data = await response.json();
                        this.allUserPermissionsBackend = data;
                        this.userPermissionsBackend = [...this.allUserPermissionsBackend];

                        // 为全局函数存储权限数据
                        window.allBackendPermissions = [...data];

                        this.renderPermissionsTableBackend();
                    } catch (error) {
                        console.error('获取用户后端权限数据失败:', error);
                        this.$message.error('获取用户后端权限数据失败');
                    }
                },

                // 获取用户前端权限数据
                async fetchUserPermissionsFrontend() {
                    try {
                        console.log('开始获取前端权限数据...');
                        const response = await fetch('/api/user-permissions-frontend');
                        if (!response.ok) {
                            throw new Error('获取用户前端权限数据失败');
                        }

                        const data = await response.json();
                        console.log('获取到前端权限数据:', data);
                        this.allUserPermissionsFrontend = data;
                        this.userPermissionsFrontend = [...this.allUserPermissionsFrontend];

                        // 为全局函数存储权限数据
                        window.allFrontendPermissions = [...data];

                        this.renderPermissionsTableFrontend();
                    } catch (error) {
                        console.error('获取用户前端权限数据失败:', error);
                        this.$message.error('获取用户前端权限数据失败');
                    }
                },

                // 渲染后端权限表格
                renderPermissionsTableBackend() {
                    const tbody = document.getElementById('backendPermissionsBody');
                    if (!tbody) return;

                    // 清空现有内容
                    tbody.innerHTML = '';

                    // 过滤数据
                    const filteredPermissions = this.applyFiltersToData(
                        this.userPermissionsBackend,
                        this.permSelectedDepartment,
                        this.permSelectedGroup
                    );

                    // 渲染每一行
                    filteredPermissions.forEach((permission, index) => {
                        const row = document.createElement('tr');

                        // 创建基本单元格
                        row.innerHTML = `
                            <td>${index + 1}</td>
                            <td>${permission.department}</td>
                            <td>
                                <span onclick="app._instance.proxy.editIdentity(this, '${permission.id}', 'backend')">
                                    ${permission.identity || '点击编辑'}
                                </span>
                                <input type="text" class="identity-input" style="display: none;" 
                                    value="${permission.identity || ''}" 
                                    onchange="app._instance.proxy.updateIdentityBackend('${permission.id}', this.value)" 
                                    onblur="app._instance.proxy.finishEditIdentity(this, '${permission.id}', 'backend')"
                                    placeholder="请输入身份">
                            </td>
                            <td>${renderBackendPermissionButtons(permission, 'user_review', true)}</td>
                            <td>${renderBackendPermissionButtons(permission, 'user_management', true)}</td>
                            <td>${renderBackendPermissionButtons(permission, 'form_preset')}</td>
                            <td>${renderBackendPermissionButtons(permission, 'schedule_management')}</td>
                            <td>${renderBackendPermissionButtons(permission, 'distribution_rules')}</td>
                            <td>${renderBackendPermissionButtons(permission, 'distribution_plan')}</td>
                            <td>
                                <button class="reject-btn" style="padding: 5px 10px;" 
                                    onclick="app._instance.proxy.deletePermission('${permission.department}', '${permission.identity}')">
                                    删除
                                </button>
                            </td>
                        `;

                        tbody.appendChild(row);
                    });

                    // 添加"添加新身份"按钮行
                    tbody.insertAdjacentHTML('beforeend', `
                        <tr>
                            <td colspan="15" style="text-align: center; padding: 15px;">
                                <button class="add-permission-btn" onclick="app._instance.proxy.addNewPermissionBackend()">
                                    <i class="fas fa-plus"></i> 添加新身份
                                </button>
                            </td>
                        </tr>
                    `);
                },

                // 渲染前端权限表格
                renderPermissionsTableFrontend() {
                    const tbody = document.querySelector('#distributionTab .permission-table tbody');
                    if (!tbody) return;

                    // 清空现有内容
                    tbody.innerHTML = '';

                    // 过滤数据
                    const filteredData = this.applyFiltersToData(
                        this.allUserPermissionsFrontend,
                        this.permSelectedDepartment,
                        this.permSelectedGroup
                    );

                    // 渲染每一行
                    filteredData.forEach((permission, index) => {
                        const row = document.createElement('tr');
                        const isNewRecord = String(permission.id).startsWith('temp_');

                        // 部门字段显示
                        const departmentField = isNewRecord
                            ? `<select class="department-select" onchange="app._instance.proxy.updateDepartmentFrontend('${permission.id}', this.value)">
                                <option value="" ${!permission.department ? 'selected' : ''}>请选择部门</option>
                                <option value="总经办" ${permission.department === '总经办' ? 'selected' : ''}>总经办</option>
                                <option value="电商部" ${permission.department === '电商部' ? 'selected' : ''}>电商部</option>
                                <option value="新媒体部" ${permission.department === '新媒体部' ? 'selected' : ''}>新媒体部</option>
                                <option value="销售部" ${permission.department === '销售部' ? 'selected' : ''}>销售部</option>
                                <option value="技术部" ${permission.department === '技术部' ? 'selected' : ''}>技术部</option>
                                <option value="设计部" ${permission.department === '设计部' ? 'selected' : ''}>设计部</option>
                                <option value="施工部" ${permission.department === '施工部' ? 'selected' : ''}>施工部</option>
                                <option value="访客" ${permission.department === '访客' ? 'selected' : ''}>访客</option>
                              </select>`
                            : permission.department;

                        // 创建基本单元格
                        row.innerHTML = `
                            <td>${index + 1}</td>
                            <td>${departmentField}</td>
                            <td>
                                ${isNewRecord ?
                                `<input type="text" class="identity-input" 
                                        value="${permission.identity || ''}" 
                                        onchange="app._instance.proxy.updateIdentityFrontend('${permission.id}', this.value)" 
                                        placeholder="请输入身份">` :
                                `<span onclick="app._instance.proxy.editIdentity(this, '${permission.id}', 'frontend')">
                                        ${permission.identity || '点击编辑'}
                                    </span>
                                    <input type="text" class="identity-input" style="display: none;" 
                                        value="${permission.identity || ''}" 
                                        onchange="app._instance.proxy.updateIdentityFrontend('${permission.id}', this.value)" 
                                        onblur="app._instance.proxy.finishEditIdentity(this, '${permission.id}', 'frontend')"
                                        placeholder="请输入身份">
                                `
                            }
                        </td>
                        <td>${renderFrontendPermissionButtons(permission, 'lead_table', true)}</td>
                        <td>${renderFrontendPermissionButtons(permission, 'lead_submit', true)}</td>
                        <td>${renderFrontendPermissionButtons(permission, 'lead_crm', true)}</td>
                        <td>${renderFrontendPermissionButtons(permission, 'gonghai_crm', true)}</td>
                        <td>${renderFrontendPermissionButtons(permission, 'recycle_bin', true)}</td>
                        <td>${renderFrontendPermissionButtons(permission, 'user_messages', true)}</td>
                        <td>${renderFrontendPermissionButtons(permission, 'check_shenpi', true)}</td>
                        <td>
                            <button class="reject-btn" style="padding: 5px 10px;" 
                                onclick="app._instance.proxy.deletePermission('${permission.department}', '${permission.identity}')">
                                删除
                            </button>
                        </td>
                    `;

                        tbody.appendChild(row);
                    });

                    // 添加"添加新身份"按钮行
                    tbody.insertAdjacentHTML('beforeend', `
                    <tr>
                        <td colspan="25" style="text-align: center; padding: 15px;">
                            <button class="add-permission-btn" onclick="app._instance.proxy.addNewPermissionFrontend()">
                                <i class="fas fa-plus"></i> 添加新角色
                            </button>
                        </td>
                    </tr>
                `);
                },

                switchTab(tab) {
                    this.currentTab = tab;

                    // 重置虚拟滚动状态
                    this.virtualScroll.startIndex = 0;
                    this.virtualScroll.endIndex = 0;

                    // 重新初始化虚拟滚动并更新表格
                    this.$nextTick(() => {
                        this.initVirtualScroll();
                        this.renderUserTable();
                    });
                },
                renderUserTable() {
                    const tbody = document.getElementById('userTableBody');
                    if (!tbody) return;

                    if (this.filteredUsers.length === 0) {
                        tbody.innerHTML = `
                            <tr>
                                <td colspan="8" style="text-align: center; padding: 20px;">
                                    没有匹配的用户数据
                                </td>
                            </tr>
                        `;
                        return;
                    }
                    // 更新可见范围
                    this.updateVisibleRange();

                    // 获取可见区域的用户数据
                    const visibleUsers = this.filteredUsers.slice(
                        this.virtualScroll.startIndex,
                        this.virtualScroll.endIndex + 1
                    );

                    // 计算占位高度
                    const beforeHeight = this.virtualScroll.startIndex * this.virtualScroll.itemHeight;
                    const afterHeight = Math.max(0, (this.filteredUsers.length - this.virtualScroll.endIndex - 1) * this.virtualScroll.itemHeight);

                    // 判断权限管理按钮是否可用
                    const permissionBtnDisabled = (this.currentUser && this.currentUser.is_admin === 0) ? false : 
                                                 (this.userManagementPermissions.permission_manage !== 0);
                    const permissionBtnClass = permissionBtnDisabled ? 'permission-btn disabled-btn' : 'permission-btn';

                    // 渲染HTML
                    tbody.innerHTML = `
                        ${beforeHeight > 0 ? `<tr style="height: ${beforeHeight}px; padding: 0; border: none;"><td colspan="8" style="padding: 0; border: none;"></td></tr>` : ''}
                        ${visibleUsers.map((user, localIndex) => {
                        const globalIndex = this.virtualScroll.startIndex + localIndex;
                        return `
                            <tr>
                                <td>${globalIndex + 1}</td>
                                <td>${user.name}</td>
                                <td>${user.department}</td>
                                <td>${user.group_name || '-'}</td>
                                <td>${new Date(user.register_time).toLocaleString()}</td>
                                <td>${user.is_admin === 0 ? '超级管理员' : (user.is_admin === 1 ? '前端+后端' : '前端')}</td>
                                <td>
                                    <select class="role-select" onchange="app._instance.proxy.updateUserRole(${user.id}, this.value)" ${user.is_admin === 0 ? 'disabled' : ''}>
                                        <option value="1" ${user.is_admin === 1 ? 'selected' : ''}>前端+后端</option>
                                        <option value="2" ${user.is_admin === 2 ? 'selected' : ''}>前端</option>
                                    </select>
                                </td>
                                <td>
                                    <div class="action-buttons">
                                        <button class="approve-btn" onclick="app._instance.proxy.approveUser(${user.id})" ${user.is_admin === 0 ? 'disabled' : ''}>变更</button>
                                        <button class="reject-btn" onclick="app._instance.proxy.rejectUser(${user.id})" ${user.is_admin === 0 ? 'disabled' : ''} title="${user.is_admin === 0 ? '超级管理员账户不可删除' : ''}">删除</button>
                                    </div>
                                </td>
                            </tr>
                            `;
                    }).join('')}
                        ${afterHeight > 0 ? `<tr style="height: ${afterHeight}px; padding: 0; border: none;"><td colspan="8" style="padding: 0; border: none;"></td></tr>` : ''}
                    `;
                    
                    // 更新权限管理按钮状态
                    const permissionBtn = document.querySelector('.permission-btn');
                    if (permissionBtn) {
                        if (permissionBtnDisabled) {
                            permissionBtn.classList.add('disabled-btn');
                            // 完全替换onclick事件，阻止后续处理
                            permissionBtn.onclick = function(e) {
                                e.preventDefault();
                                e.stopPropagation(); // 阻止事件冒泡
                                ElementPlus.ElMessage({
                                    message: '您没有权限管理操作权限！',
                                    type: 'warning',
                                    duration: 2000
                                });
                                return false; // 确保不继续执行其他事件处理
                            };
                            // 移除任何可能存在的data-target属性，防止bootstrap模态窗口自动打开
                            permissionBtn.removeAttribute('data-target');
                            permissionBtn.removeAttribute('data-toggle');
                        } else {
                            permissionBtn.classList.remove('disabled-btn');
                            // 直接替换onclick事件
                            permissionBtn.onclick = function() {
                                app._instance.proxy.openPermissionModal();
                            };
                        }
                    }
                },
                async approveUser(userId) {
                    const roleSelect = document.querySelector(`select[onchange="app._instance.proxy.updateUserRole(${userId}, this.value)"]`);
                    const currentRole = roleSelect.value;

                    try {
                        // 直接发送角色更新请求
                        const response = await fetch(`/api/update-user-role/${userId}`, {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json'
                            },
                            body: JSON.stringify({
                                is_admin: parseInt(currentRole)
                            })
                        });

                        if (response.ok) {
                            ElementPlus.ElMessage({
                                message: '用户角色变更成功！',
                                type: 'success',
                                duration: 2000
                            });
                            await this.fetchActiveUsers();
                        } else {
                            // 尝试获取错误详情
                            let errorMsg = '角色变更失败！';
                            try {
                                const errorData = await response.json();
                                if (errorData && errorData.detail) {
                                    errorMsg = errorData.detail;
                                }
                            } catch (e) {
                                console.error('解析错误响应失败:', e);
                            }
                            
                            ElementPlus.ElMessage({
                                message: errorMsg,
                                type: 'error',
                                duration: 2000
                            });
                        }
                    } catch (error) {
                        ElementPlus.ElMessage({
                            message: '角色变更失败！',
                            type: 'error',
                            duration: 2000
                        });
                        console.error('变更操作失败:', error);
                    }
                },
                
                // 更新用户角色
                updateUserRole(userId, value) {
                    // 仅存储选中值，实际变更在点击变更按钮时执行
                    console.log(`用户${userId}角色被选中为: ${value}`);
                    // 此方法仅用于处理select的onchange事件
                },
                
                async rejectUser(userId) {
                    try {
                        const response = await fetch(`/api/reject-user/${userId}`, {
                            method: 'POST'
                        });
                        if (response.ok) {
                            ElementPlus.ElMessage({
                                message: '用户已删除！',
                                type: 'success',
                                duration: 2000
                            });
                            await this.fetchActiveUsers();
                        } else {
                            ElementPlus.ElMessage({
                                message: '删除操作失败',
                                type: 'error',
                                duration: 2000
                            });
                        }
                    } catch (error) {
                        ElementPlus.ElMessage({
                            message: '删除操作失败',
                            type: 'error',
                            duration: 2000
                        });
                        console.error('删除操作失败:', error);
                    }
                },
                openPermissionModal() {
                    // 检查权限 - 直接返回不执行后续操作
                    if (this.currentUser && this.currentUser.is_admin !== 0 && 
                        this.userManagementPermissions.permission_manage !== 0) {
                        ElementPlus.ElMessage({
                            message: '您没有权限管理操作权限！',
                            type: 'warning',
                            duration: 2000
                        });
                        return;
                    }
                    
                    document.getElementById('permissionModal').style.display = 'flex';

                    // 显示骨架屏
                    document.getElementById('permissionSkeleton').style.display = 'block';
                    document.getElementById('permissionContent').style.display = 'none';

                    // 重置权限管理面板的过滤条件
                    this.permSelectedDepartment = '';
                    
                    // 对于非超级管理员，默认按自己部门筛选
                    if (this.currentUser && this.currentUser.is_admin !== 0 && this.currentUser.department) {
                        this.permSelectedDepartment = this.currentUser.department;
                        const permDepartmentFilter = document.getElementById('permDepartmentFilter');
                        if (permDepartmentFilter) {
                            permDepartmentFilter.value = this.currentUser.department;
                        }
                        console.log('权限管理面板默认选中部门:', this.permSelectedDepartment);
                    }

                    // 根据用户角色显示/隐藏过滤器
                    this.updatePermissionUIByUserRole();

                    // 重置过滤器选中值
                    const permDepartmentFilter = document.getElementById('permDepartmentFilter');
                    if (permDepartmentFilter) {
                        permDepartmentFilter.value = '';
                    }

                    // 加载权限数据
                    Promise.all([
                        this.fetchUserPermissionsBackend(),
                        this.fetchUserPermissionsFrontend()
                    ]).then(() => {
                        this.isPermissionLoading = false;
                        this.showPermissionContent();
                        
                        // 确保两个选项卡的内容都已准备好
                        this.$nextTick(() => {
                            // 先渲染前端选项卡，因为它总是能正确加载
                            this.switchPermissionTab('distribution', true);
                            // 然后再切换回后端选项卡
                            setTimeout(() => {
                                this.switchPermissionTab('management', true);
                                
                                // 检查是否有重复的部门+身份组合
                                this.checkAllDuplicates();
                            }, 100);
                        });
                    }).catch(error => {
                        console.error('加载权限数据失败:', error);
                        ElementPlus.ElMessage.error('加载权限数据失败，请重试');
                        this.isPermissionLoading = false;
                        this.showPermissionContent();
                    });
                },
                closePermissionModal() {
                    document.getElementById('permissionModal').style.display = 'none';
                },

                // 更新后端权限的身份字段
                updateIdentityBackend(userId, value) {
                    // 找到对应用户的权限数据
                    const permissionIndex = this.userPermissionsBackend.findIndex(p => p.id === userId);
                    if (permissionIndex === -1) return;

                    // 更新身份值
                    this.userPermissionsBackend[permissionIndex].identity = value;
                    console.log(`后端身份已更改: 用户${userId} 身份${value}`);
                },

                // 更新前端权限的身份字段
                updateIdentityFrontend(userId, value) {
                    // 找到对应用户的前端权限数据
                    const permissionIndex = this.userPermissionsFrontend.findIndex(p => p.id === userId);
                    if (permissionIndex === -1) return;

                    // 更新身份值
                    this.userPermissionsFrontend[permissionIndex].identity = value;
                    console.log(`前端身份已更改: 用户${userId} 身份${value}`);
                },

                // 修改选项卡切换方法，加载对应的权限数据
                switchPermissionTab(tabName, loadData = true) {
                    console.log(`切换到${tabName}选项卡，loadData=${loadData}`);
                    
                    // 切换选项卡按钮状态
                    const tabBtns = document.querySelectorAll('.permission-tab-btn');
                    tabBtns.forEach(btn => {
                        btn.classList.remove('active');
                    });
                    const activeBtn = Array.from(tabBtns).find(btn => btn.textContent.includes(tabName === 'management' ? '后端' : '前端'));
                    if (activeBtn) {
                        activeBtn.classList.add('active');
                    }

                    // 切换选项卡内容
                    const tabPanes = document.querySelectorAll('.permission-tab-pane');
                    tabPanes.forEach(pane => {
                        pane.classList.remove('active');
                    });
                    const activePane = document.getElementById(tabName === 'management' ? 'managementTab' : 'distributionTab');
                    if (activePane) {
                        activePane.classList.add('active');
                    }

                    // 只在需要时加载和渲染数据
                    if (loadData) {
                        console.log(`开始加载${tabName}选项卡数据`);
                        if (tabName === 'management') {
                            if (this.allUserPermissionsBackend.length === 0) {
                                console.log('后端权限数据为空，重新获取');
                                this.fetchUserPermissionsBackend().then(() => {
                                    console.log('后端权限数据获取完成，渲染表格');
                                    this.renderPermissionsTableBackend();
                                });
                            } else {
                                console.log('使用已有后端权限数据渲染表格');
                                this.renderPermissionsTableBackend();
                            }
                        } else {
                            if (this.allUserPermissionsFrontend.length === 0) {
                                console.log('前端权限数据为空，重新获取');
                                this.fetchUserPermissionsFrontend().then(() => {
                                    console.log('前端权限数据获取完成，渲染表格');
                                    this.renderPermissionsTableFrontend();
                                });
                            } else {
                                console.log('使用已有前端权限数据渲染表格');
                                this.renderPermissionsTableFrontend();
                            }
                        }
                    }
                },
                renderNoPermissionMessage() {
                    const tbody = document.getElementById('userTableBody');
                    tbody.innerHTML = `
                        <tr>
                            <td colspan="8" style="text-align: center; padding: 20px;">
                                <div style="display: flex; flex-direction: column; align-items: center; gap: 10px;">
                                    <i class="fas fa-lock" style="font-size: 48px; color: #909399;"></i>
                                    <div style="font-size: 16px; color: #606266;">您没有权限访问此页面</div>
                                    <div style="font-size: 14px; color: #909399;">只有具有适当权限的用户才能访问此功能</div>
                                </div>
                            </td>
                        </tr>
                    `;
                },

                // 后端权限按钮处理
                togglePermissionBackend(userId, field, value) {
                    // 找到对应用户的权限数据
                    const permissionIndex = this.userPermissionsBackend.findIndex(p => String(p.id) === String(userId));
                    if (permissionIndex === -1) return;

                    // 直接设置新值
                    this.userPermissionsBackend[permissionIndex][field] = value;

                    // 更新按钮状态
                    const buttons = document.querySelector(`.permission-buttons[data-perm-id="${userId}"][data-field="${field}"]`);
                    if (buttons) {
                        const allowBtn = buttons.querySelector('.permission-btn-allow');
                        const denyBtn = buttons.querySelector('.permission-btn-deny');

                        // 清除两个按钮的active状态
                        allowBtn.classList.remove('active');
                        denyBtn.classList.remove('active');

                        // 根据新值激活对应按钮
                        if (value === 0) {
                            allowBtn.classList.add('active');
                        } else {
                            denyBtn.classList.add('active');
                        }
                    }

                    console.log(`后端权限已更改: 用户${userId} 字段${field} 值${value}`);
                },

                // 前端权限按钮处理
                togglePermissionFrontend(id, field, value) {
                    try {
                        // 找到对应记录的权限数据
                        const permissionIndex = this.userPermissionsFrontend.findIndex(p => String(p.id) === String(id));
                        if (permissionIndex === -1) return;

                        // 更新权限值
                        this.userPermissionsFrontend[permissionIndex][field] = value;

                        // 在全局变量中存储修改过的权限
                        if (!window.permissionsData.modifiedFrontendPermissions[id]) {
                            window.permissionsData.modifiedFrontendPermissions[id] = {};
                        }
                        window.permissionsData.modifiedFrontendPermissions[id][field] = value;

                        // === 新增：互斥规则处理 ===
                        // 当某个字段设置为"允许"(值为0)时，设置互斥字段为"禁止"(值为1)
                        if (value === 0) {
                            let mutexFields = [];

                            // 查询当前字段所属的互斥组
                            if (['check_all_lead', 'check_member_lead', 'check_mine_lead'].includes(field)) {
                                mutexFields = ['check_all_lead', 'check_member_lead', 'check_mine_lead'].filter(f => f !== field);
                            } else if (['edit_all_lead', 'edit_member_lead', 'edit_mine_lead', 'forbid_edit'].includes(field)) {
                                mutexFields = ['edit_all_lead', 'edit_member_lead', 'edit_mine_lead', 'forbid_edit'].filter(f => f !== field);
                            } else if (['delete_all_lead', 'delete_member_lead', 'delete_mine_lead'].includes(field)) {
                                mutexFields = ['delete_all_lead', 'delete_member_lead', 'delete_mine_lead'].filter(f => f !== field);
                            } else if (['check_all_bin', 'check_member_bin', 'check_mine_bin'].includes(field)) {
                                mutexFields = ['check_all_bin', 'check_member_bin', 'check_mine_bin'].filter(f => f !== field);
                            } else if (['check_all_messages', 'check_member_messages', 'check_mine_messages'].includes(field)) {
                                mutexFields = ['check_all_messages', 'check_member_messages', 'check_mine_messages'].filter(f => f !== field);
                            } else if (['check_all_shenpi', 'check_mine_shenpi'].includes(field)) {
                                mutexFields = ['check_all_shenpi', 'check_mine_shenpi'].filter(f => f !== field);
                            }

                            // 设置互斥字段为禁止
                            for (const mutexField of mutexFields) {
                                // 更新数据
                                this.userPermissionsFrontend[permissionIndex][mutexField] = 1;
                                window.permissionsData.modifiedFrontendPermissions[id][mutexField] = 1;

                                // 更新UI
                                const mutexButtonGroup = document.querySelector(`.permission-buttons[data-id="${id}"][data-field="${mutexField}"]`);
                                if (mutexButtonGroup) {
                                    const allowBtn = mutexButtonGroup.querySelector('.permission-btn-allow');
                                    const denyBtn = mutexButtonGroup.querySelector('.permission-btn-deny');

                                    if (allowBtn) allowBtn.classList.remove('active');
                                    if (denyBtn) denyBtn.classList.add('active');
                                }
                            }
                        }
                        // === 互斥规则处理结束 ===

                        // 更新按钮状态
                        const buttons = document.querySelector(`.permission-buttons[data-id="${id}"][data-field="${field}"]`);
                        if (buttons) {
                            const allowBtn = buttons.querySelector('.permission-btn-allow');
                            const denyBtn = buttons.querySelector('.permission-btn-deny');

                            // 清除两个按钮的active状态
                            allowBtn.classList.remove('active');
                            denyBtn.classList.remove('active');

                            // 根据新值激活对应按钮
                            if (value === 0) {
                                allowBtn.classList.add('active');
                            } else {
                                denyBtn.classList.add('active');
                            }
                        }

                        // === 新增：如果是lead_table相关子权限，刷新主按钮颜色 ===
                        if (['check_all_lead', 'check_member_lead', 'check_mine_lead',
                            'edit_all_lead', 'edit_member_lead', 'edit_mine_lead',
                            'forbid_edit', 'delete_all_lead', 'delete_member_lead',
                            'delete_mine_lead', 'export_lead_table', 'import_lead_table'].includes(field)) {
                            updateMainPermissionAllowBtnColorFrontend(id, field);
                        }
                        if (['check_all_bin', 'check_member_bin', 'check_mine_bin'].includes(field)) {
                            updateMainPermissionAllowBtnColorFrontend(id, field);
                        }
                        if (['check_all_messages', 'check_member_messages', 'check_mine_messages'].includes(field)) {
                            updateMainPermissionAllowBtnColorFrontend(id, field);
                        }
                        if (['check_all_shenpi', 'check_mine_shenpi'].includes(field)) {
                            updateMainPermissionAllowBtnColorFrontend(id, field);
                        }

                        console.log(`前端权限已更改: 用户${id} 字段${field} 值${value}`);
                    } catch (error) {
                        console.error('切换前端权限出错:', error);
                    }
                },

                applyFilters() {
                    // 重置虚拟滚动状态
                    this.virtualScroll.startIndex = 0;
                    this.virtualScroll.endIndex = 0;

                    // 更新表格
                    this.$nextTick(() => {
                        this.renderUserTable();
                        console.log('应用过滤器后的用户数量:', this.filteredUsers.length);
                    });
                },

                // 应用过滤器逻辑
                applyFiltersToData(data, department, group) {
                    return data.filter(item => {
                        const matchDepartment = !department || item.department === department;
                        const matchGroup = !group || (group === 'empty' ? !item.group_name : item.group_name === group);
                        return matchDepartment && matchGroup;
                    });
                },
                async fetchCurrentUser() {
                    try {
                        const response = await fetch('/api/user/profile');
                        if (response.ok) {
                            const userData = await response.json();
                            // 确保is_admin是数字类型
                            userData.is_admin = parseInt(userData.is_admin);
                            this.currentUser = userData;
                            console.log('当前用户信息:', this.currentUser);

                            // 更新UI上显示的用户权限
                            this.updatePermissionUIByUserRole();
                        }
                    } catch (error) {
                        console.error('获取当前用户信息失败:', error);
                        this.currentUser = { is_admin: 999 }; // 设置默认非超管
                    }
                },

                async fetchGroupList() {
                    try {
                        // 直接使用API获取所有分组
                        console.log('开始获取分组数据...');
                        const response = await fetch('/api/group-list');
                        console.log('API响应状态:', response.status);

                        if (response.ok) {
                            // 获取分组列表
                            const data = await response.json();
                            console.log('API返回的原始分组数据:', data);
                            this.groupList = data;
                            console.log('处理后的分组列表:', this.groupList);

                            // 参考user_review.html，直接操作DOM添加分组选项
                            this.renderGroupOptions();
                        } else {
                            console.error('获取分组列表失败:', response.status);
                            // 失败时尝试备用方法，确保备用方法能获取所有分组
                            await this.fetchGroupListFallback();
                        }
                    } catch (error) {
                        console.error('获取分组列表失败:', error);
                        await this.fetchGroupListFallback();
                    }
                },

                // 改进备用方法，确保获取所有用户数据
                async fetchGroupListFallback() {
                    try {
                        console.log('使用备用方法获取分组数据...');
                        // 直接查询数据库所有分组，不经过用户数据筛选
                        const groupResponse = await fetch('/api/all-groups'); // 创建一个新API专门获取所有分组
                        if (groupResponse.ok) {
                            this.groupList = await groupResponse.json();
                            console.log('备用方法直接获取的分组列表:', this.groupList);
                            this.renderGroupOptions();
                            return;
                        }

                        // 原来的备用逻辑作为第二备用
                        const userResponse = await fetch('/api/active-users');
                        if (userResponse.ok) {
                            const users = await userResponse.json();
                            console.log('获取到的用户总数:', users.length);

                            const groups = new Set();
                            users.forEach(user => {
                                if (user.group_name && user.group_name.trim() !== '') {
                                    groups.add(user.group_name);
                                }
                            });

                            this.groupList = [...groups];
                            console.log('从用户数据提取的分组列表:', this.groupList);
                            this.renderGroupOptions();
                        }
                    } catch (error) {
                        console.error('备用方法获取分组失败:', error);
                    }
                },

                // 改进分组选项渲染方法
                renderGroupOptions() {
                    try {
                        const groupSelect = document.getElementById('groupSelect');
                        if (!groupSelect) {
                            console.warn('警告: 未找到groupSelect元素，可能因为DOM尚未完全加载');
                            return; // 元素不存在时直接返回，避免错误
                        }

                        console.log('开始渲染分组选项，总数:', this.groupList.length);

                        // 清空现有选项，保留"全部分组"和"-"
                        groupSelect.innerHTML = '<option value="">全部分组</option><option value="empty">-</option>';

                        // 排序分组列表并添加选项
                        const sortedGroups = [...this.groupList].sort();
                        console.log('排序后的分组列表:', sortedGroups);

                        sortedGroups.forEach(group => {
                            if (group && group.trim() !== '') {
                                const option = document.createElement('option');
                                option.value = group;
                                option.textContent = group;
                                groupSelect.appendChild(option);
                            }
                        });

                        console.log('完成渲染分组选项,实际渲染数量:', groupSelect.options.length - 2);
                    } catch (error) {
                        console.error('渲染分组选项时出错:', error);
                        // 错误不影响主要功能继续执行
                    }
                },

                // 渲染权限管理面板的分组选项
                renderPermGroupOptions() {
                    const groupSelect = document.getElementById('permGroupSelect');
                    if (!groupSelect) return;

                    // 清空现有选项
                    groupSelect.innerHTML = `
            <option value="">全部分组</option>
            <option value="empty">-</option>
        `;

                    // 添加分组选项
                    this.sortedGroups.forEach(group => {
                        const option = document.createElement('option');
                        option.value = group;
                        option.textContent = group;
                        groupSelect.appendChild(option);
                    });
                },

                // 初始化虚拟滚动
                initVirtualScroll() {
                    const container = document.getElementById('userTableContainer');
                    if (!container) return;

                    // 计算可见数量，增加缓冲区以确保最后一个元素可见
                    this.virtualScroll.visibleCount = Math.ceil(container.clientHeight / this.virtualScroll.itemHeight) + 2;

                    // 设置初始显示范围
                    this.updateVisibleRange();

                    // 移除之前的滚动监听器（避免重复绑定）
                    container.removeEventListener('scroll', this.handleTableScroll);
                    // 添加滚动监听
                    container.addEventListener('scroll', this.handleTableScroll);
                },

                // 处理表格滚动
                handleTableScroll(e) {
                    if (this.scrollTimer) {
                        clearTimeout(this.scrollTimer);
                    }

                    this.scrollTimer = setTimeout(() => {
                        this.updateVisibleRange();
                        this.renderUserTable();
                    }, 50); // 50ms的节流时间
                },

                // 更新可见范围
                updateVisibleRange() {
                    const container = document.getElementById('userTableContainer');
                    if (!container || this.filteredUsers.length === 0) return;

                    const scrollTop = container.scrollTop;
                    const containerHeight = container.clientHeight;

                    // 计算开始索引
                    const startIndex = Math.max(0, Math.floor(scrollTop / this.virtualScroll.itemHeight) - this.virtualScroll.bufferCount);

                    // 计算结束索引，确保包含最后一个元素
                    let endIndex = Math.min(
                        this.filteredUsers.length - 1,
                        startIndex + this.virtualScroll.visibleCount + this.virtualScroll.bufferCount * 2
                    );

                    // 特殊处理：当滚动到底部时，确保显示最后一个元素
                    const maxScrollTop = Math.max(0, this.filteredUsers.length * this.virtualScroll.itemHeight - containerHeight);
                    if (scrollTop >= maxScrollTop - 10) { // 允许10px的误差
                        endIndex = this.filteredUsers.length - 1;
                    }

                    this.virtualScroll.startIndex = startIndex;
                    this.virtualScroll.endIndex = Math.max(startIndex, endIndex); // 确保endIndex不小于startIndex
                },

                // 添加窗口大小变化监听
                handleResize() {
                    const container = document.getElementById('userTableContainer');
                    if (!container) return;

                    // 重新计算可见数量，增加缓冲区
                    this.virtualScroll.visibleCount = Math.ceil(container.clientHeight / this.virtualScroll.itemHeight) + 2;

                    // 更新可见范围和表格
                    this.updateVisibleRange();
                    this.renderUserTable();
                },

                // 显示实际内容
                showContent() {
                    const skeletonContent = document.getElementById('skeletonContent');
                    const realContent = document.getElementById('realContent');

                    if (skeletonContent && realContent) {
                        skeletonContent.classList.add('skeleton-fade-out');

                        setTimeout(() => {
                            skeletonContent.style.display = 'none';
                            realContent.style.visibility = 'visible';
                            realContent.classList.add('content-fade-in');
                        }, 300);
                    }
                },

                // 显示权限内容
                showPermissionContent() {
                    const skeletonContent = document.getElementById('permissionSkeleton');
                    const realContent = document.getElementById('permissionContent');

                    if (skeletonContent && realContent) {
                        skeletonContent.classList.add('skeleton-fade-out');

                        setTimeout(() => {
                            skeletonContent.style.display = 'none';
                            realContent.style.display = 'block';
                            realContent.style.visibility = 'visible';
                            realContent.classList.add('content-fade-in');
                        }, 300);
                    }
                },

                // 添加更新部门的方法
                updateDepartment(permId, value) {
                    try {
                        // 查找权限记录
                        const permissionIndex = this.userPermissionsBackend.findIndex(p => p.id === permId);
                        if (permissionIndex === -1) {
                            console.error('未找到ID为', permId, '的权限记录');
                            return;
                        }

                        // 验证部门值
                        if (!value || value === '') {
                            console.warn('选择了空部门值');
                            return;
                        }

                        // 确保value是一个单一的部门值，而不是所有选项
                        const validDepartments = ['总经办', '电商部', '新媒体部', '销售部', '技术部', '设计部', '施工部', '访客'];
                        if (!validDepartments.includes(value)) {
                            console.error('选择了无效的部门值:', value);

                            // 尝试重置下拉框
                            const select = document.querySelector(`select[onchange*="${permId}"]`);
                            if (select) {
                                // 重置为当前值或空值
                                select.value = this.userPermissionsBackend[permissionIndex].department || '';
                                console.log('已重置部门选择框');
                            }
                            return;
                        }

                        // 更新部门值
                        this.userPermissionsBackend[permissionIndex].department = value;
                        console.log(`部门已更改: ID ${permId} 部门 ${value}`);
                    } catch (error) {
                        console.error('更新部门出错:', error);
                    }
                },

                // 更新前端权限部门值
                updateDepartmentFrontend(id, value) {
                    const permissionIndex = this.userPermissionsFrontend.findIndex(p => String(p.id) === String(id));
                    if (permissionIndex === -1) return;

                    this.userPermissionsFrontend[permissionIndex].department = value;
                    console.log(`前端权限部门已更改: ID ${id} 部门 ${value}`);
                },

                // 后端添加新角色
                addNewPermissionBackend() {
                    // const tbody = document.querySelector('#backendPermissionsBody');
                    const tbody = document.querySelector('#managementTab .permission-table tbody');
                    if (!tbody) return;

                    // 获取当前最后一行的序号(排除添加按钮行)
                    const rows = tbody.querySelectorAll('tr:not(:last-child)');
                    let lastIndex = 0;
                    if (rows.length > 0) {
                        const lastRow = rows[rows.length - 1];
                        const firstCell = lastRow.querySelector('td');
                        if (firstCell) {
                            lastIndex = parseInt(firstCell.textContent) || 0;
                        }
                    }

                    // 新的序号为最后一行的序号+1
                    const newIndex = lastIndex + 1;

                    // 创建新的权限对象
                    const newPermission = {
                        id: `temp_${Date.now()}`,
                        department: this.currentUser && this.currentUser.is_admin !== 0 ? this.currentUser.department : '',  // 非超管默认部门为自己的部门
                        identity: '',
                        user_review: 1,
                        user_management: 1,
                        form_preset: 1,
                        schedule_management: 1,
                        distribution_rules: 1,
                        distribution_plan: 1,
                        display_index: newIndex // 添加显示序号字段
                    };

                    // 根据用户角色创建不同的部门选择界面
                    let departmentSelection;
                    if (this.currentUser && this.currentUser.is_admin !== 0) {
                        // 非超管用户只显示固定部门文本
                        departmentSelection = `<span class="department-display">${this.currentUser.department}</span>`;
                    } else {
                        // 超管可以选择任意部门
                        departmentSelection = `
                            <select class="department-select" onchange="app._instance.proxy.updateDepartment('${newPermission.id}', this.value)">
                                <option value="">请选择部门</option>
                                <option value="总经办">总经办</option>
                                <option value="电商部">电商部</option>
                                <option value="新媒体部">新媒体部</option>
                                <option value="销售部">销售部</option>
                                <option value="技术部">技术部</option>
                                <option value="设计部">设计部</option>
                                <option value="施工部">施工部</option>
                                <option value="访客">访客</option>
                            </select>
                        `;
                    }

                    // 创建新行HTML
                    const newRowHtml = `
                        <tr>
                            <td>${newIndex}</td>
                            <td>${departmentSelection}</td>
                            <td>
                                <input type="text" class="identity-input" 
                                    value="" 
                                    onchange="app._instance.proxy.updateIdentityBackend('${newPermission.id}', this.value)" 
                                    oninput="app._instance.proxy.checkPermissionDuplicate('${newPermission.id}', 'backend')"
                                    placeholder="请输入身份">
                                <div class="duplicate-warning" id="backend-warning-${newPermission.id}" style="color: red; font-size: 12px; display: none;">该部门下已存在相同身份！</div>
                            </td>
                            <td>${renderBackendPermissionButtons(newPermission, 'user_review', true)}</td>
                            <td>${renderBackendPermissionButtons(newPermission, 'user_management', true)}</td>
                            <td>${renderBackendPermissionButtons(newPermission, 'form_preset')}</td>
                            <td>${renderBackendPermissionButtons(newPermission, 'schedule_management')}</td>
                            <td>${renderBackendPermissionButtons(newPermission, 'distribution_rules')}</td>
                            <td>${renderBackendPermissionButtons(newPermission, 'distribution_plan')}</td>
                            <td>
                                <button class="reject-btn" style="padding: 5px 10px;" 
                                    onclick="app._instance.proxy.deletePermission('${newPermission.id}', null)">
                                    删除
                                </button>
                            </td>
                        </tr>
                    `;

                    // 移除旧的添加按钮行
                    const oldAddButtonRow = tbody.lastElementChild;
                    if (oldAddButtonRow) {
                        oldAddButtonRow.remove();
                    }

                    // 插入新行
                    tbody.insertAdjacentHTML('beforeend', newRowHtml);

                    // 重新添加添加按钮行
                    tbody.insertAdjacentHTML('beforeend', `
                        <tr>
                            <td colspan="15" style="text-align: center; padding: 15px;">
                                <button class="add-permission-btn" onclick="app._instance.proxy.addNewPermissionBackend()">
                                    <i class="fas fa-plus"></i> 添加新角色
                                </button>
                            </td>
                        </tr>
                    `);

                    // 将新权限对象添加到数据中
                    this.userPermissionsBackend.push(newPermission);
                },

                // 前端添加新角色
                addNewPermissionFrontend() {
                    const tbody = document.querySelector('#distributionTab .permission-table tbody');
                    if (!tbody) return;

                    // 获取当前最后一行的序号(排除添加按钮行)
                    const rows = tbody.querySelectorAll('tr:not(:last-child)');
                    let lastIndex = 0;
                    if (rows.length > 0) {
                        const lastRow = rows[rows.length - 1];
                        const firstCell = lastRow.querySelector('td');
                        if (firstCell) {
                            lastIndex = parseInt(firstCell.textContent) || 0;
                        }
                    }

                    // 新的序号为最后一行的序号+1
                    const newIndex = lastIndex + 1;

                    // 创建新的前端权限对象
                    const newPermission = {
                        id: `temp_${Date.now()}`,
                        department: this.currentUser && this.currentUser.is_admin !== 0 ? this.currentUser.department : '',
                        identity: '',
                        lead_table: 1,
                        lead_submit: 1,
                        lead_crm: 1,
                        gonghai_crm: 1, // 新增
                        gonghai_all_crm: 1, // 新增
                        gonghai_group_crm: 1, // 新增
                        gonghai_person_crm: 1, // 新增
                        recycle_bin: 1,
                        user_messages: 1,
                        check_all_lead: 1,
                        check_member_lead: 1,
                        check_mine_lead: 1,
                        edit_all_lead: 1,
                        edit_member_lead: 1,
                        edit_mine_lead: 1,
                        forbid_edit: 1,
                        delete_all_lead: 1,
                        delete_member_lead: 1,
                        delete_mine_lead: 1,
                        export_lead_table: 1,
                        import_lead_table: 1,
                        check_person_crm: 1,
                        check_group_crm: 1,
                        check_all_crm: 1,
                        check_all_bin: 1,
                        check_member_bin: 1,
                        check_mine_bin: 1,
                        check_all_messages: 1,
                        check_member_messages: 1,
                        check_mine_messages: 1,
                        gonghai_crm: 1, // 新增
                        gonghai_all_crm: 1, // 新增
                        gonghai_group_crm: 1, // 新增
                        gonghai_person_crm: 1, // 新增
                        display_index: newIndex,
                        check_shenpi: 1,
                        check_all_shenpi: 1,
                        check_mine_shenpi: 1
                    };

                    // 根据用户角色创建不同的部门选择界面
                    let departmentSelection;
                    if (this.currentUser && this.currentUser.is_admin !== 0) {
                        // 非超管用户只显示固定部门文本
                        departmentSelection = `<span class="department-display">${this.currentUser.department}</span>`;
                    } else {
                        // 超管可以选择任意部门
                        departmentSelection = `
                            <select class="department-select" onchange="app._instance.proxy.updateDepartmentFrontend('${newPermission.id}', this.value)">
                                <option value="">请选择部门</option>
                                <option value="总经办">总经办</option>
                                <option value="电商部">电商部</option>
                                <option value="新媒体部">新媒体部</option>
                                <option value="销售部">销售部</option>
                                <option value="技术部">技术部</option>
                                <option value="设计部">设计部</option>
                                <option value="施工部">施工部</option>
                                <option value="访客">访客</option>
                            </select>
                        `;
                    }

                    // 创建新行HTML
                    const newRowHtml = `
                        <tr class="new-permission-row">
                            <td>${newIndex}</td>
                            <td>${departmentSelection}</td>
                            <td>
                                <input type="text" class="identity-input" 
                                    value="" 
                                    onchange="app._instance.proxy.updateIdentityFrontend('${newPermission.id}', this.value)" 
                                    oninput="app._instance.proxy.checkPermissionDuplicate('${newPermission.id}', 'frontend')"
                                    placeholder="请输入身份">
                                <div class="duplicate-warning" id="frontend-warning-${newPermission.id}" style="color: red; font-size: 12px; display: none;">该部门下已存在相同身份！</div>
                            </td>
                            <td>${renderFrontendPermissionButtons(newPermission, 'lead_table', true)}</td>
                            <td>${renderFrontendPermissionButtons(newPermission, 'lead_submit', true)}</td>
                            <td>${renderFrontendPermissionButtons(newPermission, 'lead_crm', true)}</td>
                            <td>${renderFrontendPermissionButtons(newPermission, 'gonghai_crm', true)}</td>
                            <td>${renderFrontendPermissionButtons(newPermission, 'recycle_bin', true)}</td>
                            <td>${renderFrontendPermissionButtons(newPermission, 'user_messages', true)}</td>
                            <td>${renderFrontendPermissionButtons(newPermission, 'check_shenpi', true)}</td>
                            <td>
                                <button class="reject-btn" style="padding: 5px 10px;" 
                                    onclick="app._instance.proxy.deletePermission('${newPermission.department}', '${newPermission.identity}')">
                                    删除
                                </button>
                            </td>
                        </tr>
                    `;

                    // 移除旧的添加按钮行
                    const oldAddButtonRow = tbody.lastElementChild;
                    if (oldAddButtonRow) {
                        oldAddButtonRow.remove();
                    }

                    // 插入新行
                    tbody.insertAdjacentHTML('beforeend', newRowHtml);

                    // 重新添加添加按钮行
                    tbody.insertAdjacentHTML('beforeend', `
                        <tr>
                            <td colspan="25" style="text-align: center; padding: 15px;">
                                <button class="add-permission-btn" onclick="app._instance.proxy.addNewPermissionFrontend()">
                                    <i class="fas fa-plus"></i> 添加新角色
                                </button>
                            </td>
                        </tr>
                    `);

                    // 将新权限对象添加到数据中
                    this.userPermissionsFrontend.push(newPermission);
                },

                // 根据选项卡决定调用哪个权限保存函数
                savePermissions() {
                    // 检查当前选中的是哪个选项卡
                    const activePane = document.querySelector('.permission-tab-pane.active');
                    const isManagementTab = activePane && activePane.id === 'managementTab';

                    if (isManagementTab) {
                        // 后端权限保存的逻辑
                        this.saveBackendPermissions();
                    } else {
                        // 前端权限保存的逻辑
                        this.saveFrontendPermissions();
                    }
                },

                // 后端权限保存
                saveBackendPermissions() {
                    // 构建权限数据
                    const permissionsToUpdate = [];
                    
                    // 首先检查是否有任何重复项
                    if (this.checkAnyDuplicates('backend')) {
                        ElementPlus.ElMessage({
                            message: '存在重复的部门+身份组合，无法保存',
                            type: 'error',
                            duration: 2000
                        });
                        return;
                    }

                    // 收集所有权限数据，包括新增和修改的
                    for (const permission of this.userPermissionsBackend) {
                        // 检查必填字段
                        if (!permission.department) {
                            ElementPlus.ElMessage({
                                message: '请选择部门！',
                                type: 'warning',
                                duration: 2000
                            });
                            return;
                        }

                        if (!permission.identity) {
                            ElementPlus.ElMessage({
                                message: '请输入身份！',
                                type: 'warning',
                                duration: 2000
                            });
                            return;
                        }
                        
                        // 构建权限对象
                        const permObj = {
                            id: String(permission.id),
                            department: permission.department,
                            identity: permission.identity,
                            user_management: parseInt(permission.user_management),
                            form_preset: parseInt(permission.form_preset),
                            schedule_management: parseInt(permission.schedule_management),
                            distribution_rules: parseInt(permission.distribution_rules),
                            distribution_plan: parseInt(permission.distribution_plan)
                        };

                        // 对于新记录，添加显示序号
                        const isNewRecord = typeof permission.id === 'string' && permission.id.startsWith('temp_');
                        if (isNewRecord && permission.display_index) {
                            permObj.display_index = permission.display_index;
                            console.log(`新增记录的序号: ${permission.display_index}`);
                        }

                        // 添加调试日志
                        console.log(`准备${isNewRecord ? '新增' : '更新'}权限记录:`, permObj);
                        permissionsToUpdate.push(permObj);
                    }

                    console.log('准备更新的后端权限数据:', permissionsToUpdate);

                    // 发送到后端保存前显示加载中状态
                    const loadingInstance = ElementPlus.ElLoading.service({
                        fullscreen: true,
                        text: '正在保存权限数据...',
                        background: 'rgba(0, 0, 0, 0.7)'
                    });

                    // 发送到后端保存
                    fetch('/api/update-user-permissions-backend', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify(permissionsToUpdate)
                    })
                        .then(async response => {
                            loadingInstance.close(); // 关闭加载状态

                            const text = await response.text();
                            console.log('服务器响应:', text);

                            if (response.ok) {
                                let data;
                                try {
                                    data = JSON.parse(text);
                                    console.log('解析的响应数据:', data);

                                    // 显示成功消息
                                    ElementPlus.ElMessage({
                                        message: `权限保存成功！${data.updated_count ? `更新了 ${data.updated_count} 条记录` : ''}${data.created_count ? `，新增了 ${data.created_count} 条记录` : ''}${data.frontend_created_count ? `，同步创建了 ${data.frontend_created_count} 条前端权限记录` : ''}`,
                                        type: 'success',
                                        duration: 2000
                                    });

                                    // 重新获取权限数据以确保显示最新状态
                                    this.fetchUserPermissionsBackend();
                                    this.closePermissionModal();
                                    return;
                                } catch (e) {
                                    console.log('响应不是有效的JSON:', text);
                                    throw new Error('服务器响应格式错误');
                                }
                            }

                            throw new Error(text || '保存失败');
                        })
                        .catch(error => {
                            loadingInstance.close(); // 确保关闭加载状态
                            console.error('保存权限失败:', error);
                            ElementPlus.ElMessage({
                                message: `保存权限失败: ${error.message}`,
                                type: 'error',
                                duration: 2000
                            });
                        });
                },

                // 前端权限保存
                saveFrontendPermissions() {
                    try {
                        // 构建权限数据
                        const permissionsToUpdate = [];
                        
                        // 首先检查是否有任何重复项
                        if (this.checkAnyDuplicates('frontend')) {
                            ElementPlus.ElMessage({
                                message: '存在重复的部门+身份组合，无法保存',
                                type: 'error',
                                duration: 2000
                            });
                            return;
                        }

                        // 收集所有用户的前端权限
                        for (const permission of this.userPermissionsFrontend) {
                            // 检查必填字段
                            if (!permission.department) {
                                ElementPlus.ElMessage({
                                    message: '请选择部门！',
                                    type: 'warning',
                                    duration: 2000
                                });
                                return;
                            }

                            // 获取更新后的身份值
                            const identityInput = document.querySelector(`input[onchange="app._instance.proxy.updateIdentityFrontend('${permission.id}', this.value)"]`);
                            const identityValue = identityInput ? identityInput.value : (permission.identity || '');

                            if (!identityValue) {
                                ElementPlus.ElMessage({
                                    message: '请输入身份！',
                                    type: 'warning',
                                    duration: 2000
                                });
                                return;
                            }

                            // 更新内存中的身份值以确保一致性
                            permission.identity = identityValue;

                            const permObj = {
                                id: String(permission.id),
                                department: permission.department,
                                identity: identityValue
                            };

                            // 添加所有权限字段，优先使用修改过的值
                            const modifiedFields = window.permissionsData.modifiedFrontendPermissions[permission.id] || {};

                            // 定义所有权限字段
                            const permissionFields = [
                                'lead_table', 'check_all_lead', 'check_member_lead', 'check_mine_lead',
                                'edit_all_lead', 'edit_member_lead', 'edit_mine_lead', 'forbid_edit',
                                'delete_all_lead', 'delete_member_lead', 'delete_mine_lead',
                                'export_lead_table', 'import_lead_table', 'lead_submit', 'lead_crm',
                                'check_person_crm', 'check_group_crm', 'check_all_crm',
                                'gonghai_crm', 'gonghai_all_crm', 'gonghai_group_crm', 'gonghai_person_crm', // 新增
                                'recycle_bin', 'check_all_bin', 'check_member_bin', 'check_mine_bin',
                                'user_messages', 'check_all_messages', 'check_member_messages', 'check_mine_messages',
                                'check_shenpi', 'check_all_shenpi', 'check_mine_shenpi'
                            ];

                            // 遍历所有权限字段，复用现有逻辑
                            permissionFields.forEach(field => {
                                // 如果字段被修改过，使用修改后的值
                                if (field in modifiedFields) {
                                    permObj[field] = parseInt(modifiedFields[field]);
                                    console.log(`使用修改后的值: ${field}=${modifiedFields[field]}`);
                                }
                                // 否则使用原始值
                                else if (field in permission) {
                                    permObj[field] = parseInt(permission[field]);
                                }
                                // 如果都没有，使用默认值1（禁止）
                                else {
                                    permObj[field] = 1;
                                }
                            });

                            // 对于新记录，添加显示序号
                            const isNewRecord = typeof permission.id === 'string' && permission.id.startsWith('temp_');
                            if (isNewRecord && permission.display_index) {
                                permObj.display_index = permission.display_index;
                                console.log(`新增前端记录的序号: ${permission.display_index}`);
                            }

                            // 添加调试日志
                            console.log(`准备${isNewRecord ? '新增' : '更新'}前端权限记录:`, permObj);
                            permissionsToUpdate.push(permObj);
                        }

                        console.log('准备更新的前端权限数据:', permissionsToUpdate);

                        // 显示加载中状态
                        const loadingInstance = ElementPlus.ElLoading.service({
                            fullscreen: true,
                            text: '正在保存权限数据...',
                            background: 'rgba(0, 0, 0, 0.7)'
                        });

                        // 发送到后端保存
                        fetch('/api/update-user-permissions-frontend', {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json'
                            },
                            body: JSON.stringify(permissionsToUpdate)
                        })
                            .then(async response => {
                                loadingInstance.close();

                                const text = await response.text();
                                console.log('服务器响应:', text);

                                if (response.ok) {
                                    try {
                                        const data = JSON.parse(text);
                                        console.log('解析的响应数据:', data);

                                        // 清空修改记录
                                        window.permissionsData.modifiedFrontendPermissions = {};

                                        ElementPlus.ElMessage({
                                            message: '前端权限保存成功！',
                                            type: 'success',
                                            duration: 2000
                                        });

                                        // === 新增：先应用新的黄色按钮逻辑，再获取最新数据 ===
                                        try {
                                            console.log('保存成功，先应用新的黄色按钮逻辑...');
                                            // 更新前端权限数据
                                            fetch('/api/user-permissions-frontend')
                                                .then(response => response.json())
                                                .then(updatedData => {
                                                    // 更新数据
                                                    this.allUserPermissionsFrontend = updatedData;
                                                    this.userPermissionsFrontend = [...updatedData];
                                                    window.allFrontendPermissions = [...updatedData];

                                                    // 重新渲染表格
                                                    this.renderPermissionsTableFrontend();
                                                    console.log('已重新渲染前端权限表格');

                                                    // 循环更新所有权限的主按钮颜色
                                                    if (this.userPermissionsFrontend.length > 0) {
                                                        this.userPermissionsFrontend.forEach(perm => {
                                                            // 更新lead_table主按钮颜色
                                                            updateMainPermissionAllowBtnColorFrontend(perm.id, 'check_all_lead');
                                                            // 更新recycle_bin主按钮颜色
                                                            updateMainPermissionAllowBtnColorFrontend(perm.id, 'check_all_bin');
                                                            // 更新user_messages主按钮颜色
                                                            updateMainPermissionAllowBtnColorFrontend(perm.id, 'check_all_messages');
                                                            // 更新lead_crm主按钮颜色
                                                            updateMainPermissionAllowBtnColorFrontend(perm.id, 'check_person_crm');
                                                            // 更新check_shenpi主按钮颜色
                                                            updateMainPermissionAllowBtnColorFrontend(perm.id, 'check_all_shenpi');
                                                            // 更新gonghai_crm主按钮颜色
                                                            updateMainPermissionAllowBtnColorFrontend(perm.id, 'gonghai_crm');
                                                            // 更新gonghai_all_crm主按钮颜色
                                                            updateMainPermissionAllowBtnColorFrontend(perm.id, 'gonghai_all_crm');
                                                            // 更新gonghai_group_crm主按钮颜色
                                                            updateMainPermissionAllowBtnColorFrontend(perm.id, 'gonghai_group_crm');
                                                            // 更新gonghai_person_crm主按钮颜色
                                                            updateMainPermissionAllowBtnColorFrontend(perm.id, 'gonghai_person_crm');
                                                        });
                                                    }

                                                    console.log('黄色按钮逻辑应用完成');
                                                })
                                                .catch(error => {
                                                    console.error('获取最新前端权限数据失败:', error);
                                                    // 失败时仍尝试获取数据
                                                    this.fetchUserPermissionsFrontend();
                                                });
                                        } catch (error) {
                                            console.error('应用新黄色按钮逻辑失败:', error);
                                            // 错误时也尝试获取数据
                                            this.fetchUserPermissionsFrontend();
                                        }
                                        // === 黄色按钮逻辑处理结束 ===

                                        return;
                                    } catch (e) {
                                        console.log('响应不是有效的JSON:', text);
                                        ElementPlus.ElMessage({
                                            message: '前端权限保存成功！',
                                            type: 'success',
                                            duration: 2000
                                        });

                                        // 错误情况下仍尝试获取最新数据
                                        this.fetchUserPermissionsFrontend();
                                    }
                                    return;
                                }

                                throw new Error(text || '保存失败');
                            })
                            .catch(error => {
                                loadingInstance.close();
                                console.error('保存前端权限失败:', error);
                                ElementPlus.ElMessage({
                                    message: `保存前端权限失败: ${error.message}`,
                                    type: 'error',
                                    duration: 2000
                                });
                            });
                    } catch (error) {
                        console.error('保存前端权限函数执行出错:', error);
                        ElementPlus.ElMessage({
                            message: `操作失败: ${error.message}`,
                            type: 'error',
                            duration: 2000
                        });
                    }
                },

                // 删除按钮逻辑
                async deletePermission(department, identity) {
                    try {
                        // 检查是否是临时记录（ID以temp_开头）
                        const isTempRecord = typeof department === 'string' && department.startsWith('temp_');

                        if (isTempRecord) {
                            // 临时记录，从内存中删除
                            if (!confirm('确定要删除此临时权限记录吗？')) {
                                return;
                            }

                            // 从数组中找到并移除
                            const index = this.userPermissionsBackend.findIndex(p => p.id === department);
                            if (index > -1) {
                                this.userPermissionsBackend.splice(index, 1);
                                console.log('已删除临时权限记录:', department);

                                // 刷新表格
                                this.renderPermissionsTableBackend();

                                ElementPlus.ElMessage({
                                    message: '已删除临时权限记录',
                                    type: 'success',
                                    duration: 2000
                                });
                            } else {
                                console.error('未找到临时权限记录:', department);
                            }
                            return;
                        }

                        // 常规记录，调用API删除
                        if (!confirm(`确定要删除部门为"${department}"且身份为"${identity}"的权限记录吗？此操作将同时删除前端和后端的权限记录，且不可恢复。`)) {
                            return;
                        }

                        // 调用API删除两个表中的权限记录
                        const response = await fetch(`/api/delete-permissions/${encodeURIComponent(department)}/${encodeURIComponent(identity)}`, {
                            method: 'DELETE'
                        });

                        const result = await response.json();

                        if (response.ok) {
                            ElementPlus.ElMessage({
                                message: result.message || '删除权限成功',
                                type: 'success',
                                duration: 2000
                            });

                            // 重新加载权限数据
                            await this.fetchUserPermissionsBackend();
                            await this.fetchUserPermissionsFrontend();

                            // 重新渲染表格
                            this.renderPermissionsTableBackend();
                            this.renderPermissionsTableFrontend();
                        } else {
                            throw new Error(result.detail || '删除权限失败');
                        }
                    } catch (error) {
                        console.error('删除权限出错:', error);
                        ElementPlus.ElMessage({
                            message: `删除权限失败: ${error.message}`,
                            type: 'error',
                            duration: 2000
                        });
                    }
                },

                // 开始编辑身份
                editIdentity(element, id, type) {
                    // 隐藏显示文本
                    element.style.display = 'none';

                    // 显示输入框
                    const inputElement = element.nextElementSibling;
                    inputElement.style.display = 'inline-block';
                    inputElement.focus();
                },

                // 完成编辑身份
                finishEditIdentity(inputElement, id, type) {
                    // 获取新的值
                    const newValue = inputElement.value;

                    // 更新显示文本
                    const displayElement = inputElement.previousElementSibling;
                    displayElement.textContent = newValue || '点击编辑';

                    // 隐藏输入框，显示文本
                    inputElement.style.display = 'none';
                    displayElement.style.display = 'inline-block';

                    // 更新数据
                    if (type === 'backend') {
                        this.updateIdentityBackend(id, newValue);
                    } else {
                        this.updateIdentityFrontend(id, newValue);
                    }
                },

                // 添加一个根据用户角色更新UI的方法
                updatePermissionUIByUserRole() {
                    // 获取过滤器容器
                    const mainFilterContainer = document.querySelector('.header .filter-container');
                    const permFilterContainer = document.getElementById('permFilterContainer');

                    if (this.currentUser && this.currentUser.is_admin === 0) {
                        // 超管可以看到过滤器
                        console.log('当前用户是超管，显示过滤器');
                        if (mainFilterContainer) mainFilterContainer.style.display = 'flex';
                        if (permFilterContainer) permFilterContainer.style.display = 'flex';
                    } else {
                        // 非超管不能看到过滤器
                        console.log('当前用户不是超管，隐藏过滤器');
                        if (mainFilterContainer) mainFilterContainer.style.display = 'none';
                        if (permFilterContainer) permFilterContainer.style.display = 'none';
                    }
                },

                // 修改editUser方法
                editUser(userId) {
                    const user = this.users.find(u => u.id === userId);
                    if (!user) {
                        ElementPlus.ElMessage({
                            message: '用户数据不存在',
                            type: 'error',
                            duration: 2000
                        });
                        return;
                    }
                },

                // 修改deleteUser方法
                deleteUser(userId) {
                    const user = this.users.find(u => u.id === userId);
                    if (!user) {
                        ElementPlus.ElMessage({
                            message: '用户数据不存在',
                            type: 'error',
                            duration: 2000
                        });
                        return;
                    }
                },

                // 获取用户管理权限
                async fetchUserManagementPermissions() {
                    try {
                        const response = await fetch('/api/user-management-permissions');
                        if (response.ok) {
                            this.userManagementPermissions = await response.json();
                            console.log('获取到的用户管理权限:', this.userManagementPermissions);
                            return this.userManagementPermissions;
                        } else {
                            console.error('获取用户管理权限失败: 服务器返回非200状态码');
                            return { manage_all: 1, manage_group: 1, permission_manage: 1 };
                        }
                    } catch (error) {
                        console.error('获取用户管理权限失败:', error);
                        return { manage_all: 1, manage_group: 1, permission_manage: 1 };
                    }
                },

                // 绑定权限管理按钮事件
                bindPermissionBtnEvent() {
                    const permissionBtn = document.getElementById('permissionModalBtn');
                    if (!permissionBtn) return;
                    
                    const permissionBtnDisabled = (this.currentUser && this.currentUser.is_admin === 0) ? false : 
                                                (this.userManagementPermissions.permission_manage !== 0);
                    
                    if (permissionBtnDisabled) {
                        permissionBtn.classList.add('disabled-btn');
                        permissionBtn.onclick = (e) => {
                            e.preventDefault();
                            e.stopPropagation();
                            ElementPlus.ElMessage({
                                message: '您没有权限管理操作权限！',
                                type: 'warning',
                                duration: 2000
                            });
                            return false;
                        };
                    } else {
                        permissionBtn.classList.remove('disabled-btn');
                        permissionBtn.onclick = () => {
                            this.openPermissionModal();
                        };
                    }
                    
                    console.log('权限管理按钮事件已绑定，禁用状态:', permissionBtnDisabled);
                },

                // 更新部门过滤UI
                updateDepartmentFilterUI() {
                    // 更新下拉框选中状态
                    const departmentSelect = document.querySelector('.filter-select[v-model="selectedDepartment"]');
                    if (departmentSelect && this.selectedDepartment) {
                        departmentSelect.value = this.selectedDepartment;
                        console.log('已更新部门过滤UI:', this.selectedDepartment);
                    }
                },

                // 添加检查部门+身份是否重复的功能
                checkPermissionExists(department, identity, type, excludeId = null) {
                    // 确保两个字段都有值
                    if (!department || !identity) return false;
                    
                    // 检查对应类型的权限数据中是否已有相同部门+身份
                    const permissions = type === 'backend' ? this.allUserPermissionsBackend : this.allUserPermissionsFrontend;
                    
                    // 忽略大小写和空格进行比较
                    const normalizedDept = department.trim().toLowerCase();
                    const normalizedIdentity = identity.trim().toLowerCase();
                    
                    // 查找是否有匹配的记录，除了自己
                    const existingPerm = permissions.find(p => {
                        // 如果指定了忽略的ID，则跳过该记录
                        if (excludeId && String(p.id) === String(excludeId)) return false;
                        
                        if (!p.department || !p.identity) return false;
                        return p.department.trim().toLowerCase() === normalizedDept && 
                               p.identity.trim().toLowerCase() === normalizedIdentity;
                    });
                    
                    return !!existingPerm; // 转换为布尔值
                },

                // 修改实时检查重复的功能
                checkPermissionDuplicate(permId, type) {
                    // 获取对应的权限数据
                    const permissions = type === 'backend' ? this.userPermissionsBackend : this.userPermissionsFrontend;
                    const permIndex = permissions.findIndex(p => String(p.id) === String(permId));
                    
                    if (permIndex === -1) return;
                    
                    const permission = permissions[permIndex];
                    const department = permission.department;
                    const identity = permission.identity;
                    
                    // 检查是否重复（排除自己）
                    const isDuplicate = this.checkPermissionExists(department, identity, type, permId);
                    
                    // 找到对应的警告元素
                    const warningId = `${type}-warning-${permId}`;
                    const warningElement = document.getElementById(warningId);
                    
                    if (!warningElement) return;
                    
                    // 更新警告显示
                    if (isDuplicate && department && identity) {
                        warningElement.style.display = 'block';
                        
                        // 找到对应的保存按钮并禁用
                        const saveBtn = document.querySelector('.modal-btn-confirm');
                        if (saveBtn) {
                            saveBtn.disabled = true;
                            saveBtn.classList.add('disabled-btn');
                            saveBtn.title = "存在重复的部门+身份组合，请修改后再保存";
                        }
                    } else {
                        warningElement.style.display = 'none';
                        
                        // 检查是否还有其他重复项，如果没有则启用保存按钮
                        const stillHasDuplicates = this.checkAnyDuplicates(type);
                        if (!stillHasDuplicates) {
                            const saveBtn = document.querySelector('.modal-btn-confirm');
                            if (saveBtn) {
                                saveBtn.disabled = false;
                                saveBtn.classList.remove('disabled-btn');
                                saveBtn.title = "";
                            }
                        }
                    }
                    
                    return isDuplicate;
                },
                
                // 检查是否存在任何重复项
                checkAnyDuplicates(type) {
                    const permissions = type === 'backend' ? this.userPermissionsBackend : this.userPermissionsFrontend;
                    
                    // 使用Set来跟踪已检查过的部门+身份组合
                    const seen = new Set();
                    
                    // 查找是否有任何重复的部门+身份组合
                    for (const permission of permissions) {
                        if (!permission.department || !permission.identity) continue;
                        
                        const key = `${permission.department.trim().toLowerCase()}::${permission.identity.trim().toLowerCase()}`;
                        
                        if (seen.has(key)) {
                            return true; // 找到重复项
                        }
                        
                        seen.add(key);
                    }
                    
                    return false; // 没有找到重复项
                },
                
                // 在权限模态窗口打开时检查所有重复项
                checkAllDuplicates() {
                    const backend = this.checkAnyDuplicates('backend');
                    const frontend = this.checkAnyDuplicates('frontend');
                    
                    // 如果有重复项，禁用保存按钮
                    if (backend || frontend) {
                        const saveBtn = document.querySelector('.modal-btn-confirm');
                        if (saveBtn) {
                            saveBtn.disabled = true;
                            saveBtn.classList.add('disabled-btn');
                            saveBtn.title = "存在重复的部门+身份组合，请修改后再保存";
                        }
                        
                        // 显示全局警告消息
                        ElementPlus.ElMessage({
                            message: '检测到重复的部门+身份组合，请修改后再保存',
                            type: 'warning',
                            duration: 3000
                        });
                    }
                },

                // 修改更新部门方法，增加重复检查
                updateDepartment(permId, value) {
                    try {
                        // 查找权限记录
                        const permissionIndex = this.userPermissionsBackend.findIndex(p => p.id === permId);
                        if (permissionIndex === -1) {
                            console.error('未找到ID为', permId, '的权限记录');
                            return;
                        }

                        // 验证部门值
                        if (!value || value === '') {
                            console.warn('选择了空部门值');
                            return;
                        }

                        // 确保value是一个单一的部门值，而不是所有选项
                        const validDepartments = ['总经办', '电商部', '新媒体部', '销售部', '技术部', '设计部', '施工部', '访客'];
                        if (!validDepartments.includes(value)) {
                            console.error('选择了无效的部门值:', value);

                            // 尝试重置下拉框
                            const select = document.querySelector(`select[onchange*="${permId}"]`);
                            if (select) {
                                // 重置为当前值或空值
                                select.value = this.userPermissionsBackend[permissionIndex].department || '';
                                console.log('已重置部门选择框');
                            }
                            return;
                        }

                        // 更新部门值
                        this.userPermissionsBackend[permissionIndex].department = value;
                        console.log(`部门已更改: ID ${permId} 部门 ${value}`);
                        
                        // 更新后检查重复
                        this.checkPermissionDuplicate(permId, 'backend');
                    } catch (error) {
                        console.error('更新部门出错:', error);
                    }
                },

                // 更新前端权限部门值，增加重复检查
                updateDepartmentFrontend(id, value) {
                    const permissionIndex = this.userPermissionsFrontend.findIndex(p => String(p.id) === String(id));
                    if (permissionIndex === -1) return;

                    this.userPermissionsFrontend[permissionIndex].department = value;
                    console.log(`前端权限部门已更改: ID ${id} 部门 ${value}`);
                    
                    // 更新后检查重复
                    this.checkPermissionDuplicate(id, 'frontend');
                },
                
                // 修改更新身份方法，增加重复检查
                updateIdentityBackend(userId, value) {
                    // 找到对应用户的权限数据
                    const permissionIndex = this.userPermissionsBackend.findIndex(p => p.id === userId);
                    if (permissionIndex === -1) return;

                    // 更新身份值
                    this.userPermissionsBackend[permissionIndex].identity = value;
                    console.log(`后端身份已更改: 用户${userId} 身份${value}`);
                    
                    // 更新后检查重复
                    this.checkPermissionDuplicate(userId, 'backend');
                },

                // 修改更新前端身份方法，增加重复检查
                updateIdentityFrontend(userId, value) {
                    // 找到对应用户的前端权限数据
                    const permissionIndex = this.userPermissionsFrontend.findIndex(p => p.id === userId);
                    if (permissionIndex === -1) return;

                    // 更新身份值
                    this.userPermissionsFrontend[permissionIndex].identity = value;
                    console.log(`前端身份已更改: 用户${userId} 身份${value}`);
                    
                    // 更新后检查重复
                    this.checkPermissionDuplicate(userId, 'frontend');
                },
            },
            mounted() {
                // 获取当前用户信息
                this.fetchCurrentUser().then(() => {
                    // 如果是超级管理员，直接显示全部功能和数据
                    if (this.currentUser && this.currentUser.is_admin === 0) {
                        // 获取分组列表
                        this.fetchGroupList().then(() => {
                            // 获取用户列表
                            this.fetchActiveUsers();
                            
                            // 初始化虚拟滚动
                            this.$nextTick(() => {
                                this.initVirtualScroll();
                                // 绑定权限管理按钮事件
                                this.bindPermissionBtnEvent();
                            });
                        });
                        return;
                    }
                    
                    // 非超级管理员默认选中自己的部门
                    if (this.currentUser && this.currentUser.department) {
                        this.selectedDepartment = this.currentUser.department;
                        console.log('非超级管理员，默认选中部门:', this.selectedDepartment);
                    }
                    
                    // 获取用户管理权限
                    this.fetchUserManagementPermissions().then(() => {
                        // 如果用户没有用户管理权限，显示无权限信息
                        if (this.userManagementPermissions.manage_all === 1 && this.userManagementPermissions.manage_group === 1) {
                            this.renderNoPermissionMessage();
                            this.isLoading = false;
                            this.showContent();
                            return;
                        }
                        
                        // 获取分组列表
                        this.fetchGroupList().then(() => {
                            // 获取用户列表
                            this.fetchActiveUsers();
                            
                            // 初始化虚拟滚动
                            this.$nextTick(() => {
                                this.initVirtualScroll();
                                // 绑定权限管理按钮事件
                                this.bindPermissionBtnEvent();
                                
                                // 同步下拉框选中状态
                                this.updateDepartmentFilterUI();
                            });
                        });
                    });
                });
                
                // 添加窗口大小变化监听
                window.addEventListener('resize', this.handleResize);
            },
            beforeUnmount() {
                // 移除窗口大小变化监听
                window.removeEventListener('resize', this.handleResize);

                // 移除表格滚动监听
                const container = document.getElementById('userTableContainer');
                if (container) {
                    container.removeEventListener('scroll', this.handleTableScroll);
                }
            },
            // 确保当currentUser更新时，重新检查权限UI
            watch: {
                'currentUser.is_admin': function (newVal, oldVal) {
                    console.log('用户角色变更:', oldVal, '->', newVal);
                    this.updatePermissionUIByUserRole();
                }
            }
        });

        app.mount('.container');

        // 添加一个全局变量来存储权限数据
        window.permissionsData = {
            backend: [],
            frontend: [],
            modifiedPermissions: {}, // 用于存储修改过的后端权限
            modifiedFrontendPermissions: {} // 用于存储修改过的前端权限
        };

        // 处理弹窗的显示和隐藏
        function togglePopup(element) {
            const buttonGroup = element.closest('.permission-buttons');
            if (buttonGroup) {
                // 支持后端和前端主权限字段
                const mainFields = ['user_review', 'user_management', 'lead_table', 'recycle_bin', 'user_messages'];
                // 兼容后端(data-perm-id)和前端(data-id)
                const field = buttonGroup.getAttribute('data-field');
                const isBackend = buttonGroup.hasAttribute('data-perm-id');
                const isFrontend = buttonGroup.hasAttribute('data-id');
                const isMainField = (isBackend || isFrontend) && mainFields.includes(field);
                // 查找当前按钮组下的禁止按钮
                const denyBtn = Array.from(buttonGroup.children).find(
                    el => el.classList && el.classList.contains('permission-btn-deny') && el.classList.contains('active')
                );
                // 新角色行不受影响
                const isNewRole = buttonGroup.closest('tr.new-permission-row') !== null;
                if (isMainField && denyBtn && !isNewRole) {
                    // 主权限字段为禁止，禁用三角形按钮
                    return;
                }
            }

            // 后续弹窗逻辑：只允许一个弹窗显示
            forceCloseAllPopups();

            // 找到对应的弹窗
            const popup = element.parentElement.querySelector('.permission-popup');
            if (popup) {
                popup.classList.add('show');
                popup.style.display = 'block';

                // 让弹窗可拖动
                makeDraggable(popup);

                // 定位弹窗
                const rect = element.getBoundingClientRect();
                popup.style.position = 'fixed';
                popup.style.left = `${rect.left + window.scrollX - popup.offsetWidth / 2 + element.offsetWidth / 2}px`;
                popup.style.top = `${rect.bottom + window.scrollY + 8}px`;
            }
        }

        // 关闭当前弹窗
        function closeCurrentPopup(element) {
            const popup = element.closest('.permission-popup');
            if (popup) {
                popup.classList.remove('show');
            }
        }

        // 让弹窗可拖动
        function makeDraggable(element) {
            let initialX = 0, initialY = 0;
            let isDragging = false;

            // 获取弹窗标题作为拖动句柄
            const header = element.querySelector('.popup-title');
            if (!header) return;

            // 添加拖动样式
            header.style.cursor = 'move';

            // 鼠标按下时的处理
            header.addEventListener('mousedown', startDragging);

            function startDragging(e) {
                e.preventDefault();

                // 记录初始位置
                const rect = element.getBoundingClientRect();
                initialX = e.clientX - rect.left;
                initialY = e.clientY - rect.top;

                isDragging = true;

                // 添加临时样式
                element.style.transition = 'none';
                element.style.userSelect = 'none';

                // 添加移动和释放事件监听
                document.addEventListener('mousemove', handleDrag);
                document.addEventListener('mouseup', stopDragging);
            }

            function handleDrag(e) {
                if (!isDragging) return;

                // 计算新位置
                const newX = e.clientX - initialX;
                const newY = e.clientY - initialY;

                // 添加边界检查
                const maxX = window.innerWidth - element.offsetWidth;
                const maxY = window.innerHeight - element.offsetHeight;

                // 应用新位置
                element.style.left = `${Math.min(Math.max(0, newX), maxX)}px`;
                element.style.top = `${Math.min(Math.max(0, newY), maxY)}px`;

                e.preventDefault();
            }

            function stopDragging() {
                if (!isDragging) return;

                isDragging = false;

                // 恢复样式
                element.style.transition = '';
                element.style.userSelect = '';

                // 移除事件监听
                document.removeEventListener('mousemove', handleDrag);
                document.removeEventListener('mouseup', stopDragging);
            }
        }

        // 强制关闭所有弹窗
        function forceCloseAllPopups() {
            try {
                // 关闭所有权限弹窗
                document.querySelectorAll('.permission-popup').forEach(popup => {
                    popup.style.display = 'none';
                    popup.classList.remove('show');
                });

                console.log('已关闭所有弹窗');
            } catch (error) {
                console.error('关闭弹窗出错:', error);
            }
        }

        // 辅助函数：生成后端权限按钮
        function renderBackendPermissionButtons(permission, field, hasTriangle = false) {
            // 获取对应字段的值
            const value = permission[field] !== undefined ? permission[field] : 1;

            // 为特殊字段准备弹窗内容
            let popupContent = '';

            if (field === 'user_review') {
                popupContent = `
                    <div class="popup-title">用户审核权限设置</div>
                    <div class="popup-item">
                        <div class="popup-item-label">审核所有</div>
                        <div class="permission-buttons" data-perm-id="${permission.id}" data-field="review_all">
                            <button class="permission-btn-allow ${permission.review_all === 0 ? 'active' : ''}" 
                                onclick="togglePopupPermission(this, '${permission.id}', 'review_all', 0)"
                                title="允许"></button>
                            <button class="permission-btn-deny ${permission.review_all === 1 ? 'active' : ''}"
                                onclick="togglePopupPermission(this, '${permission.id}', 'review_all', 1)"
                                title="禁止"></button>
                        </div>
                    </div>
                    <div class="popup-item">
                        <div class="popup-item-label">审核组内</div>
                        <div class="permission-buttons" data-perm-id="${permission.id}" data-field="review_group">
                            <button class="permission-btn-allow ${permission.review_group === 0 ? 'active' : ''}" 
                                onclick="togglePopupPermission(this, '${permission.id}', 'review_group', 0)"
                                title="允许"></button>
                            <button class="permission-btn-deny ${permission.review_group === 1 ? 'active' : ''}"
                                onclick="togglePopupPermission(this, '${permission.id}', 'review_group', 1)"
                                title="禁止"></button>
                        </div>
                    </div>
                    <div class="popup-footer">
                        <button class="popup-confirm" onclick="savePopupPermissions(this, '${permission.id}', 'user_review'); forceCloseAllPopups();">确认</button>
                    </div>
                `;
            } else if (field === 'user_management') {
                popupContent = `
                    <div class="popup-title">用户管理权限设置</div>
                    <div class="popup-item">
                        <div class="popup-item-label">管理所有</div>
                        <div class="permission-buttons" data-perm-id="${permission.id}" data-field="manage_all">
                            <button class="permission-btn-allow ${permission.manage_all === 0 ? 'active' : ''}" 
                                onclick="togglePopupPermission(this, '${permission.id}', 'manage_all', 0)"
                                title="允许"></button>
                            <button class="permission-btn-deny ${permission.manage_all === 1 ? 'active' : ''}"
                                onclick="togglePopupPermission(this, '${permission.id}', 'manage_all', 1)"
                                title="禁止"></button>
                        </div>
                    </div>
                    <div class="popup-item">
                        <div class="popup-item-label">管理组内</div>
                        <div class="permission-buttons" data-perm-id="${permission.id}" data-field="manage_group">
                            <button class="permission-btn-allow ${permission.manage_group === 0 ? 'active' : ''}" 
                                onclick="togglePopupPermission(this, '${permission.id}', 'manage_group', 0)"
                                title="允许"></button>
                            <button class="permission-btn-deny ${permission.manage_group === 1 ? 'active' : ''}"
                                onclick="togglePopupPermission(this, '${permission.id}', 'manage_group', 1)"
                                title="禁止"></button>
                        </div>
                    </div>
                    <div class="popup-item">
                        <div class="popup-item-label">权限管理</div>
                        <div class="permission-buttons" data-perm-id="${permission.id}" data-field="permission_manage">
                            <button class="permission-btn-allow ${permission.permission_manage === 0 ? 'active' : ''}" 
                                onclick="togglePopupPermission(this, '${permission.id}', 'permission_manage', 0)"
                                title="允许"></button>
                            <button class="permission-btn-deny ${permission.permission_manage === 1 ? 'active' : ''}"
                                onclick="togglePopupPermission(this, '${permission.id}', 'permission_manage', 1)"
                                title="禁止"></button>
                        </div>
                    </div>
                    <div class="popup-footer">
                        <button class="popup-confirm" onclick="savePopupPermissions(this, '${permission.id}', 'user_management'); forceCloseAllPopups();">确认</button>
                    </div>
                `;
            }

            let allowBtnClass = '';
            if (value === 0) {
                // 根据优化后的逻辑设置按钮颜色
                if (field === 'user_review') {
                    // 对于用户审核，review_all或review_group任意一个为0就显示绿色
                    const reviewAllPermission = permission.review_all === 0 || permission.review_all === undefined;
                    const reviewGroupPermission = permission.review_group === 0 || permission.review_group === undefined;

                    if (reviewAllPermission || reviewGroupPermission) {
                        // 任意一个子权限为绿色，主权限显示为绿色
                        allowBtnClass = 'active';
                    } else {
                        // 所有子权限都为红色，主权限显示为黄色
                        allowBtnClass = 'partial';
                    }
                } else if (field === 'user_management') {
                    // 对于用户管理，管理所有/组内任意一个为0且权限管理为0时为绿色
                    const manageAllPermission = permission.manage_all === 0 || permission.manage_all === undefined;
                    const manageGroupPermission = permission.manage_group === 0 || permission.manage_group === undefined;
                    const permissionManagePermission = permission.permission_manage === 0 || permission.permission_manage === undefined;

                    // 判断manage相关的权限是否有一个为绿色
                    const hasManagePermission = manageAllPermission || manageGroupPermission;

                    if (hasManagePermission && permissionManagePermission) {
                        // 所有相关子权限都是绿色，显示为绿色
                        allowBtnClass = 'active';
                    } else {
                        // 其他情况显示为黄色
                        allowBtnClass = 'partial';
                    }
                } else {
                    allowBtnClass = 'active';
                }
            }

            return `
                <div class="permission-buttons" style="position: relative;" data-perm-id="${permission.id}" data-field="${field}">
                    <button class="permission-btn-allow ${allowBtnClass}" 
                        onclick="togglePermissionBackendNative('${permission.id}', '${field}', 0)"
                        title="允许"></button>
                    <button class="permission-btn-deny ${value === 1 ? 'active' : ''}"
                        onclick="togglePermissionBackendNative('${permission.id}', '${field}', 1)"
                        title="禁止"></button>
                    ${hasTriangle ? `
                        <div class="permission-btn-triangle" onclick="togglePopup(this)"></div>
                        <div class="permission-popup">
                            ${popupContent}
                        </div>
                    ` : ''}
                </div>
            `;
        }

        // 保存弹窗中的权限设置
        function savePopupPermissions(element, permissionId, parentField) {
            try {
                console.log('保存弹窗权限开始，ID:', permissionId, '字段:', parentField);

                // 获取弹窗
                const popup = element.closest('.permission-popup');
                if (!popup) {
                    console.error('找不到弹窗元素');
                    return;
                }

                // 检查保存的是前端权限还是后端权限
                const isFrontendPermission = ['lead_table', 'recycle_bin', 'user_messages', 'lead_crm'].includes(parentField);

                // 根据权限类型选择正确的存储对象
                let permissionsData, permissionsArray;
                if (isFrontendPermission) {
                    // 前端权限
                    permissionsData = window.permissionsData.modifiedFrontendPermissions;
                    permissionsArray = window.app && window.app._instance ? window.app._instance.proxy.userPermissionsFrontend : [];
                } else {
                    // 后端权限
                    permissionsData = window.permissionsData.modifiedPermissions;
                    if (!window.permissionsData.backend.length && window.app && window.app._instance) {
                        window.permissionsData.backend = [...window.app._instance.proxy.userPermissionsBackend];
                    }
                    permissionsArray = window.permissionsData.backend;
                }

                // 确保修改记录对象存在
                if (!permissionsData[permissionId]) {
                    permissionsData[permissionId] = {};
                }

                // 获取权限对象信息
                let permission = null;
                let permissionIndex = -1;

                // 从对应的权限数组中查找
                permissionIndex = permissionsArray.findIndex(p => String(p.id) === String(permissionId));
                if (permissionIndex !== -1) {
                    permission = { ...permissionsArray[permissionIndex] };
                } else {
                    // 从DOM中获取权限信息
                    let permRow = null;
                    if (isFrontendPermission) {
                        // 前端权限使用 data-id 属性
                        permRow = document.querySelector(`tr [data-id="${permissionId}"]`)?.closest('tr');
                    } else {
                        // 后端权限使用 data-perm-id 属性
                        permRow = document.querySelector(`tr [data-perm-id="${permissionId}"]`)?.closest('tr');
                    }
                    if (permRow) {
                        // 获取部门和身份
                        const department = permRow.cells[1].textContent.trim();
                        const identityInput = permRow.cells[2].querySelector('input');
                        const identity = identityInput ? identityInput.value.trim() : '';

                        permission = {
                            id: permissionId,
                            department: department,
                            identity: identity
                        };

                        // 尝试从DOM中获取主权限字段的值
                        const mainPermButtons = permRow.querySelectorAll('.permission-buttons');
                        mainPermButtons.forEach(btnGroup => {
                            const field = btnGroup.getAttribute('data-field');
                            if (field) {
                                const allowBtn = btnGroup.querySelector('.permission-btn-allow.active');
                                const value = allowBtn ? 0 : 1;
                                permission[field] = value;
                            }
                        });
                    } else {
                        // 如果找不到DOM元素，则创建一个基本对象
                        permission = {
                            id: permissionId
                        };
                    }
                }

                // 根据父字段决定要保存哪些子字段
                if (parentField === 'user_review') {
                    // 在关闭前获取当前弹窗内的权限状态，确保使用最新的值
                    const reviewAllButtons = popup.querySelector('.permission-buttons[data-field="review_all"]');
                    const reviewGroupButtons = popup.querySelector('.permission-buttons[data-field="review_group"]');

                    if (reviewAllButtons) {
                        const isAllowed = reviewAllButtons.querySelector('.permission-btn-allow.active') !== null;
                        const newValue = isAllowed ? 0 : 1;
                        permissionsData[permissionId].review_all = newValue;
                        permission.review_all = newValue;
                    }

                    if (reviewGroupButtons) {
                        const isAllowed = reviewGroupButtons.querySelector('.permission-btn-allow.active') !== null;
                        const newValue = isAllowed ? 0 : 1;
                        permissionsData[permissionId].review_group = newValue;
                        permission.review_group = newValue;
                    }

                    console.log('暂存用户审核权限:', permission.id,
                        '审核所有:', permission.review_all,
                        '审核组内:', permission.review_group);
                }
                else if (parentField === 'user_management') {
                    // 获取用户管理权限
                    const manageAllButtons = popup.querySelector('.permission-buttons[data-field="manage_all"]');
                    const manageGroupButtons = popup.querySelector('.permission-buttons[data-field="manage_group"]');
                    const permissionManageButtons = popup.querySelector('.permission-buttons[data-field="permission_manage"]');

                    if (manageAllButtons) {
                        const isAllowed = manageAllButtons.querySelector('.permission-btn-allow.active') !== null;
                        const newValue = isAllowed ? 0 : 1;
                        permissionsData[permissionId].manage_all = newValue;
                        permission.manage_all = newValue;
                    }

                    if (manageGroupButtons) {
                        const isAllowed = manageGroupButtons.querySelector('.permission-btn-allow.active') !== null;
                        const newValue = isAllowed ? 0 : 1;
                        permissionsData[permissionId].manage_group = newValue;
                        permission.manage_group = newValue;
                    }

                    if (permissionManageButtons) {
                        const isAllowed = permissionManageButtons.querySelector('.permission-btn-allow.active') !== null;
                        const newValue = isAllowed ? 0 : 1;
                        permissionsData[permissionId].permission_manage = newValue;
                        permission.permission_manage = newValue;
                    }

                    console.log('暂存用户管理权限:', permission.id,
                        '管理所有:', permission.manage_all,
                        '管理组内:', permission.manage_group,
                        '权限管理:', permission.permission_manage);
                }
                // 处理前端权限中的页面访问权限弹窗
                else if (parentField === 'lead_table') {
                    // 获取查看线索相关权限
                    const checkAllLeadButtons = popup.querySelector('.permission-buttons[data-field="check_all_lead"]');
                    const checkMemberLeadButtons = popup.querySelector('.permission-buttons[data-field="check_member_lead"]');
                    const checkMineLeadButtons = popup.querySelector('.permission-buttons[data-field="check_mine_lead"]');

                    if (checkAllLeadButtons) {
                        const isAllowed = checkAllLeadButtons.querySelector('.permission-btn-allow.active') !== null;
                        const newValue = isAllowed ? 0 : 1;
                        permissionsData[permissionId].check_all_lead = newValue;
                        permission.check_all_lead = newValue;
                    }

                    if (checkMemberLeadButtons) {
                        const isAllowed = checkMemberLeadButtons.querySelector('.permission-btn-allow.active') !== null;
                        const newValue = isAllowed ? 0 : 1;
                        permissionsData[permissionId].check_member_lead = newValue;
                        permission.check_member_lead = newValue;
                    }

                    if (checkMineLeadButtons) {
                        const isAllowed = checkMineLeadButtons.querySelector('.permission-btn-allow.active') !== null;
                        const newValue = isAllowed ? 0 : 1;
                        permissionsData[permissionId].check_mine_lead = newValue;
                        permission.check_mine_lead = newValue;
                    }

                    console.log('暂存线索表格权限:', permission.id,
                        '查看所有线索:', permission.check_all_lead,
                        '查看组内线索:', permission.check_member_lead,
                        '查看个人线索:', permission.check_mine_lead);
                }
                else if (parentField === 'recycle_bin') {
                    // 获取回收站相关权限
                    const checkAllBinButtons = popup.querySelector('.permission-buttons[data-field="check_all_bin"]');
                    const checkMemberBinButtons = popup.querySelector('.permission-buttons[data-field="check_member_bin"]');
                    const checkMineBinButtons = popup.querySelector('.permission-buttons[data-field="check_mine_bin"]');

                    if (checkAllBinButtons) {
                        const isAllowed = checkAllBinButtons.querySelector('.permission-btn-allow.active') !== null;
                        const newValue = isAllowed ? 0 : 1;
                        permissionsData[permissionId].check_all_bin = newValue;
                        permission.check_all_bin = newValue;
                    }

                    if (checkMemberBinButtons) {
                        const isAllowed = checkMemberBinButtons.querySelector('.permission-btn-allow.active') !== null;
                        const newValue = isAllowed ? 0 : 1;
                        permissionsData[permissionId].check_member_bin = newValue;
                        permission.check_member_bin = newValue;
                    }

                    if (checkMineBinButtons) {
                        const isAllowed = checkMineBinButtons.querySelector('.permission-btn-allow.active') !== null;
                        const newValue = isAllowed ? 0 : 1;
                        permissionsData[permissionId].check_mine_bin = newValue;
                        permission.check_mine_bin = newValue;
                    }

                    console.log('暂存回收站权限:', permission.id,
                        '查看所有回收记录:', permission.check_all_bin,
                        '查看组内回收记录:', permission.check_member_bin,
                        '查看个人回收记录:', permission.check_mine_bin);
                }
                else if (parentField === 'user_messages') {
                    // 获取用户消息相关权限
                    const checkAllMsgButtons = popup.querySelector('.permission-buttons[data-field="check_all_messages"]');
                    const checkMemberMsgButtons = popup.querySelector('.permission-buttons[data-field="check_member_messages"]');
                    const checkMineMsgButtons = popup.querySelector('.permission-buttons[data-field="check_mine_messages"]');

                    if (checkAllMsgButtons) {
                        const isAllowed = checkAllMsgButtons.querySelector('.permission-btn-allow.active') !== null;
                        const newValue = isAllowed ? 0 : 1;
                        permissionsData[permissionId].check_all_messages = newValue;
                        permission.check_all_messages = newValue;
                    }

                    if (checkMemberMsgButtons) {
                        const isAllowed = checkMemberMsgButtons.querySelector('.permission-btn-allow.active') !== null;
                        const newValue = isAllowed ? 0 : 1;
                        permissionsData[permissionId].check_member_messages = newValue;
                        permission.check_member_messages = newValue;
                    }

                    if (checkMineMsgButtons) {
                        const isAllowed = checkMineMsgButtons.querySelector('.permission-btn-allow.active') !== null;
                        const newValue = isAllowed ? 0 : 1;
                        permissionsData[permissionId].check_mine_messages = newValue;
                        permission.check_mine_messages = newValue;
                    }

                    console.log('暂存用户消息权限:', permission.id,
                        '查看所有消息:', permission.check_all_messages,
                        '查看组内消息:', permission.check_member_messages,
                        '查看个人消息:', permission.check_mine_messages);
                }
                else if (parentField === 'lead_crm') {
                    // 获取客户关系管理相关权限
                    const checkPersonCrmButtons = popup.querySelector('.permission-buttons[data-field="check_person_crm"]');
                    const checkGroupCrmButtons = popup.querySelector('.permission-buttons[data-field="check_group_crm"]');
                    const checkAllCrmButtons = popup.querySelector('.permission-buttons[data-field="check_all_crm"]');

                    if (checkPersonCrmButtons) {
                        const isAllowed = checkPersonCrmButtons.querySelector('.permission-btn-allow.active') !== null;
                        const newValue = isAllowed ? 0 : 1;
                        permissionsData[permissionId].check_person_crm = newValue;
                        permission.check_person_crm = newValue;
                    }

                    if (checkGroupCrmButtons) {
                        const isAllowed = checkGroupCrmButtons.querySelector('.permission-btn-allow.active') !== null;
                        const newValue = isAllowed ? 0 : 1;
                        permissionsData[permissionId].check_group_crm = newValue;
                        permission.check_group_crm = newValue;
                    }

                    if (checkAllCrmButtons) {
                        const isAllowed = checkAllCrmButtons.querySelector('.permission-btn-allow.active') !== null;
                        const newValue = isAllowed ? 0 : 1;
                        permissionsData[permissionId].check_all_crm = newValue;
                        permission.check_all_crm = newValue;
                    }

                    console.log('暂存客户关系管理权限:', permission.id,
                        '查看个人线索:', permission.check_person_crm,
                        '查看组内线索:', permission.check_group_crm,
                        '查看全部线索:', permission.check_all_crm);
                }
                // 处理新增的三个页面权限弹窗
                else if (parentField === 'view_lead_page') {
                    // 获取线索页面权限
                    const viewAllLeadPageButtons = popup.querySelector('.permission-buttons[data-field="view_all_lead_page"]');
                    const viewGroupLeadPageButtons = popup.querySelector('.permission-buttons[data-field="view_group_lead_page"]');
                    const viewOwnLeadPageButtons = popup.querySelector('.permission-buttons[data-field="view_own_lead_page"]');

                    if (viewAllLeadPageButtons) {
                        const isAllowed = viewAllLeadPageButtons.querySelector('.permission-btn-allow.active') !== null;
                        const newValue = isAllowed ? 0 : 1;
                        permissionsData[permissionId].view_all_lead_page = newValue;
                        permission.view_all_lead_page = newValue;
                    }

                    if (viewGroupLeadPageButtons) {
                        const isAllowed = viewGroupLeadPageButtons.querySelector('.permission-btn-allow.active') !== null;
                        const newValue = isAllowed ? 0 : 1;
                        permissionsData[permissionId].view_group_lead_page = newValue;
                        permission.view_group_lead_page = newValue;
                    }

                    if (viewOwnLeadPageButtons) {
                        const isAllowed = viewOwnLeadPageButtons.querySelector('.permission-btn-allow.active') !== null;
                        const newValue = isAllowed ? 0 : 1;
                        permissionsData[permissionId].view_own_lead_page = newValue;
                        permission.view_own_lead_page = newValue;
                    }

                    console.log('暂存线索页面权限:', permission.id,
                        '查看所有用户线索页面:', permission.view_all_lead_page,
                        '查看组内用户线索页面:', permission.view_group_lead_page,
                        '查看个人线索页面:', permission.view_own_lead_page);
                }
                else if (parentField === 'view_recycle_page') {
                    // 获取回收站页面权限
                    const viewAllRecyclePageButtons = popup.querySelector('.permission-buttons[data-field="view_all_recycle_page"]');
                    const viewGroupRecyclePageButtons = popup.querySelector('.permission-buttons[data-field="view_group_recycle_page"]');
                    const viewOwnRecyclePageButtons = popup.querySelector('.permission-buttons[data-field="view_own_recycle_page"]');

                    if (viewAllRecyclePageButtons) {
                        const isAllowed = viewAllRecyclePageButtons.querySelector('.permission-btn-allow.active') !== null;
                        const newValue = isAllowed ? 0 : 1;
                        permissionsData[permissionId].view_all_recycle_page = newValue;
                        permission.view_all_recycle_page = newValue;
                    }

                    if (viewGroupRecyclePageButtons) {
                        const isAllowed = viewGroupRecyclePageButtons.querySelector('.permission-btn-allow.active') !== null;
                        const newValue = isAllowed ? 0 : 1;
                        permissionsData[permissionId].view_group_recycle_page = newValue;
                        permission.view_group_recycle_page = newValue;
                    }

                    if (viewOwnRecyclePageButtons) {
                        const isAllowed = viewOwnRecyclePageButtons.querySelector('.permission-btn-allow.active') !== null;
                        const newValue = isAllowed ? 0 : 1;
                        permissionsData[permissionId].view_own_recycle_page = newValue;
                        permission.view_own_recycle_page = newValue;
                    }

                    console.log('暂存回收站页面权限:', permission.id,
                        '查看所有用户回收站页面:', permission.view_all_recycle_page,
                        '查看组内用户回收站页面:', permission.view_group_recycle_page,
                        '查看个人回收站页面:', permission.view_own_recycle_page);
                }
                else if (parentField === 'view_message_page') {
                    // 获取消息页面权限
                    const viewAllMessagePageButtons = popup.querySelector('.permission-buttons[data-field="view_all_message_page"]');
                    const viewGroupMessagePageButtons = popup.querySelector('.permission-buttons[data-field="view_group_message_page"]');
                    const viewOwnMessagePageButtons = popup.querySelector('.permission-buttons[data-field="view_own_message_page"]');

                    if (viewAllMessagePageButtons) {
                        const isAllowed = viewAllMessagePageButtons.querySelector('.permission-btn-allow.active') !== null;
                        const newValue = isAllowed ? 0 : 1;
                        permissionsData[permissionId].view_all_message_page = newValue;
                        permission.view_all_message_page = newValue;
                    }

                    if (viewGroupMessagePageButtons) {
                        const isAllowed = viewGroupMessagePageButtons.querySelector('.permission-btn-allow.active') !== null;
                        const newValue = isAllowed ? 0 : 1;
                        permissionsData[permissionId].view_group_message_page = newValue;
                        permission.view_group_message_page = newValue;
                    }

                    if (viewOwnMessagePageButtons) {
                        const isAllowed = viewOwnMessagePageButtons.querySelector('.permission-btn-allow.active') !== null;
                        const newValue = isAllowed ? 0 : 1;
                        permissionsData[permissionId].view_own_message_page = newValue;
                        permission.view_own_message_page = newValue;
                    }

                    console.log('暂存消息页面权限:', permission.id,
                        '查看所有用户消息页面:', permission.view_all_message_page,
                        '查看组内用户消息页面:', permission.view_group_message_page,
                        '查看个人消息页面:', permission.view_own_message_page);
                }
                // 处理审批管理权限弹窗
                else if (parentField === 'check_shenpi') {
                    // 获取审批管理相关权限
                    const checkAllShenpiButtons = popup.querySelector('.permission-buttons[data-field="check_all_shenpi"]');
                    const checkMineShenpiButtons = popup.querySelector('.permission-buttons[data-field="check_mine_shenpi"]');

                    if (checkAllShenpiButtons) {
                        const isAllowed = checkAllShenpiButtons.querySelector('.permission-btn-allow.active') !== null;
                        const newValue = isAllowed ? 0 : 1;
                        permissionsData[permissionId].check_all_shenpi = newValue;
                        permission.check_all_shenpi = newValue;
                    }

                    if (checkMineShenpiButtons) {
                        const isAllowed = checkMineShenpiButtons.querySelector('.permission-btn-allow.active') !== null;
                        const newValue = isAllowed ? 0 : 1;
                        permissionsData[permissionId].check_mine_shenpi = newValue;
                        permission.check_mine_shenpi = newValue;
                    }

                    console.log('暂存审批管理权限:', permission.id,
                        '查看所有审批:', permission.check_all_shenpi,
                        '查看个人审批:', permission.check_mine_shenpi);

                    // === 审批管理权限互斥处理 ===
                    // 确保审批管理权限的互斥关系：只允许一个权限为0（允许）
                    if (permission.check_all_shenpi === 0 && permission.check_mine_shenpi === 0) {
                        // 如果两个都被设置为允许，保持最后修改的，将另一个设为禁止
                        // 这里我们假设最后操作的是有效的，所以需要检查临时修改记录
                        const tempData = window.crmPermissionsTemp && window.crmPermissionsTemp[permissionId];
                        if (tempData) {
                            if (tempData.check_all_shenpi === 0) {
                                permission.check_mine_shenpi = 1;
                                permissionsData[permissionId].check_mine_shenpi = 1;
                            } else if (tempData.check_mine_shenpi === 0) {
                                permission.check_all_shenpi = 1;
                                permissionsData[permissionId].check_all_shenpi = 1;
                            }
                        }
                    }

                    console.log('审批管理权限互斥处理后:', permission.id,
                        '查看所有审批:', permission.check_all_shenpi,
                        '查看个人审批:', permission.check_mine_shenpi);
                }

                // 更新对应的权限数组
                if (isFrontendPermission) {
                    if (permissionIndex !== -1) {
                        if (window.app && window.app._instance) {
                            try {
                                const vueApp = window.app._instance.proxy;
                                const vuePermIndex = vueApp.userPermissionsFrontend.findIndex(p => String(p.id) === String(permissionId));
                                if (vuePermIndex !== -1) {
                                    vueApp.userPermissionsFrontend.splice(vuePermIndex, 1, permission);
                                }
                            } catch (vueError) {
                                console.warn('Vue实例更新失败，将只使用原生JS存储:', vueError);
                            }
                        }
                    }
                } else {
                    // 后端权限更新
                    if (permissionIndex !== -1) {
                        if (window.app && window.app._instance) {
                            try {
                                const vueApp = window.app._instance.proxy;
                                const vuePermIndex = vueApp.userPermissionsBackend.findIndex(p => String(p.id) === String(permissionId));
                                if (vuePermIndex !== -1) {
                                    vueApp.userPermissionsBackend.splice(vuePermIndex, 1, permission);
                                }
                            } catch (vueError) {
                                console.warn('Vue实例更新失败，将只使用原生JS存储:', vueError);
                            }
                        }

                        window.permissionsData.backend[permissionIndex] = permission;
                    }
                }

                // 关闭弹窗 - 使用多种方式确保关闭
                console.log('正在关闭弹窗...');
                popup.classList.remove('show');
                popup.style.display = 'none';

                if (isFrontendPermission) {
                    console.log('弹窗已关闭，前端权限修改已暂存:', window.permissionsData.modifiedFrontendPermissions);
                } else {
                    console.log('弹窗已关闭，后端权限修改已暂存:', window.permissionsData.modifiedPermissions);
                }

                // === 新增：保存后重新渲染表格以更新显示 ===
                try {
                    if (isFrontendPermission) {
                        // 如果修改的是前端权限，重新渲染前端权限表格
                        if (window.app && window.app._instance) {
                            window.app._instance.proxy.renderPermissionsTableFrontend();
                            console.log('已重新渲染前端权限表格');
                        }
                    } else {
                        // 如果修改的是后端权限，重新渲染后端权限表格
                        if (window.app && window.app._instance) {
                            window.app._instance.proxy.renderPermissionsTableBackend();
                            console.log('已重新渲染后端权限表格');
                        }
                    }
                } catch (renderError) {
                    console.error('重新渲染表格失败:', renderError);
                }
                // === 重新渲染表格结束 ===

            } catch (error) {
                console.error('保存弹窗权限出错:', error);
                alert('保存权限失败：' + error.message);
            }
        }

        // 处理权限按钮的切换
        function togglePopupPermission(element, permId, field, value) {
            try {
                console.log('切换弹窗权限:', permId, field, value);

                // 查找按钮组
                const buttonGroup = element.closest('.permission-buttons');
                if (!buttonGroup) {
                    console.error('未找到按钮组');
                    return;
                }

                // 清除两个按钮的active状态
                const allowBtn = buttonGroup.querySelector('.permission-btn-allow');
                const denyBtn = buttonGroup.querySelector('.permission-btn-deny');

                if (allowBtn) allowBtn.classList.remove('active');
                if (denyBtn) denyBtn.classList.remove('active');

                // 根据新值激活对应按钮
                if (value === 0) {
                    if (allowBtn) allowBtn.classList.add('active');
                } else {
                    if (denyBtn) denyBtn.classList.add('active');
                }

                // 在全局变量中存储临时修改
                if (!window.permissionsData.modifiedPermissions[permId]) {
                    window.permissionsData.modifiedPermissions[permId] = {};
                }
                window.permissionsData.modifiedPermissions[permId][field] = value;

                // === 新增：互斥规则处理 ===
                // 当某个字段设置为"允许"(值为0)时，设置互斥字段为"禁止"(值为1)
                if (value === 0) {
                    // 处理review_all和review_group互斥
                    if (field === 'review_all' || field === 'review_group') {
                        const oppositeField = field === 'review_all' ? 'review_group' : 'review_all';
                        // 将互斥字段设置为1（禁止）
                        window.permissionsData.modifiedPermissions[permId][oppositeField] = 1;

                        // 查找互斥字段的按钮组并更新UI
                        const popup = buttonGroup.closest('.permission-popup');
                        if (popup) {
                            const oppositeButtonGroup = popup.querySelector(`.permission-buttons[data-field="${oppositeField}"]`);
                            if (oppositeButtonGroup) {
                                const oppAllowBtn = oppositeButtonGroup.querySelector('.permission-btn-allow');
                                const oppDenyBtn = oppositeButtonGroup.querySelector('.permission-btn-deny');

                                if (oppAllowBtn) oppAllowBtn.classList.remove('active');
                                if (oppDenyBtn) oppDenyBtn.classList.add('active');
                            }
                        }
                    }

                    // 处理manage_all和manage_group互斥
                    if (field === 'manage_all' || field === 'manage_group') {
                        const oppositeField = field === 'manage_all' ? 'manage_group' : 'manage_all';
                        // 将互斥字段设置为1（禁止）
                        window.permissionsData.modifiedPermissions[permId][oppositeField] = 1;

                        // 查找互斥字段的按钮组并更新UI
                        const popup = buttonGroup.closest('.permission-popup');
                        if (popup) {
                            const oppositeButtonGroup = popup.querySelector(`.permission-buttons[data-field="${oppositeField}"]`);
                            if (oppositeButtonGroup) {
                                const oppAllowBtn = oppositeButtonGroup.querySelector('.permission-btn-allow');
                                const oppDenyBtn = oppositeButtonGroup.querySelector('.permission-btn-deny');

                                if (oppAllowBtn) oppAllowBtn.classList.remove('active');
                                if (oppDenyBtn) oppDenyBtn.classList.add('active');
                            }
                        }
                    }
                }
                // === 互斥规则处理结束 ===

                console.log('弹窗中临时修改权限:', permId, field, value);
                console.log('提示: 此时只更新了UI状态，真正的数据修改会在点击确认按钮时进行');

                // 更新主按钮颜色
                if (['review_all', 'review_group'].includes(field)) {
                    updateMainPermissionAllowBtnColor(permId, field);
                } else if (['manage_all', 'manage_group', 'permission_manage'].includes(field)) {
                    updateMainPermissionAllowBtnColor(permId, field);
                } else {
                    updateMainPermissionAllowBtnColor(permId, field);
                }
            } catch (error) {
                console.error('切换弹窗权限出错:', error);
            }
        }

        // 处理后端权限按钮颜色
        function updateMainPermissionAllowBtnColor(permId, subField) {
            console.log('执行updateMainPermissionAllowBtnColor:', permId, subField);

            // 判断属于哪个主权限
            let mainField = null, subFields = [];
            if (['review_all', 'review_group'].includes(subField)) {
                mainField = 'user_review';
                subFields = ['review_all', 'review_group'];
            } else if (['manage_all', 'manage_group', 'permission_manage'].includes(subField)) {
                mainField = 'user_management';
                subFields = ['manage_all', 'manage_group', 'permission_manage'];
            } else {
                console.log('不属于需要处理的字段，退出函数');
                return;
            }

            // 获取主按钮
            const mainBtnGroup = document.querySelector(`.permission-buttons[data-perm-id="${permId}"][data-field="${mainField}"]`);
            if (!mainBtnGroup) {
                console.log('找不到主按钮组元素:', `.permission-buttons[data-perm-id="${permId}"][data-field="${mainField}"]`);
                return;
            }

            const allowBtn = mainBtnGroup.querySelector('.permission-btn-allow');
            if (!allowBtn) {
                console.log('找不到允许按钮');
                return;
            }

            // 获取当前权限对象和修改记录
            let permission = null;
            if (window.app && window.app._instance) {
                permission = window.app._instance.proxy.userPermissionsBackend.find(p => String(p.id) === String(permId));
            }
            if (!permission) {
                console.log('找不到权限对象:', permId);
                return;
            }

            // 合并修改记录中的值到权限对象
            const modifiedValues = window.permissionsData?.modifiedPermissions?.[permId] || {};
            const effectivePermission = { ...permission, ...modifiedValues };

            console.log('当前权限对象:', effectivePermission);
            console.log('主字段状态:', mainField, effectivePermission[mainField]);

            // 先检查主权限状态
            if (effectivePermission[mainField] !== 0) {
                console.log('主权限不是允许状态，移除所有样式');
                // 主权限不是允许，直接移除partial/active
                allowBtn.classList.remove('partial');
                allowBtn.classList.remove('active');
                return;
            }

            // === 优化后的逻辑 ===
            if (mainField === 'user_review') {
                // 对于用户审核，review_all或review_group任意一个为0就显示绿色
                const reviewAllPermission = effectivePermission.review_all === 0 || effectivePermission.review_all === undefined;
                const reviewGroupPermission = effectivePermission.review_group === 0 || effectivePermission.review_group === undefined;

                console.log('用户审核子权限状态:', 'review_all=', reviewAllPermission, 'review_group=', reviewGroupPermission);

                if (reviewAllPermission || reviewGroupPermission) {
                    // 任意一个子权限为绿色，主权限显示为绿色
                    console.log('显示绿色');
                    allowBtn.classList.add('active');
                    allowBtn.classList.remove('partial');
                } else {
                    // 所有子权限都为红色，主权限显示为黄色
                    console.log('显示黄色');
                    allowBtn.classList.remove('active');
                    allowBtn.classList.add('partial');
                }
            } else if (mainField === 'user_management') {
                // 对于用户管理，需要考虑三个子权限
                const manageAllPermission = effectivePermission.manage_all === 0 || effectivePermission.manage_all === undefined;
                const manageGroupPermission = effectivePermission.manage_group === 0 || effectivePermission.manage_group === undefined;
                const permissionManagePermission = effectivePermission.permission_manage === 0 || effectivePermission.permission_manage === undefined;

                console.log('用户管理子权限状态:',
                    'manage_all=', manageAllPermission,
                    'manage_group=', manageGroupPermission,
                    'permission_manage=', permissionManagePermission);

                // 判断manage相关的权限是否有一个为绿色
                const hasManagePermission = manageAllPermission || manageGroupPermission;

                if (hasManagePermission && permissionManagePermission) {
                    // 所有相关子权限都是绿色，显示为绿色
                    console.log('显示绿色');
                    allowBtn.classList.add('active');
                    allowBtn.classList.remove('partial');
                } else if (hasManagePermission && !permissionManagePermission) {
                    // manage相关权限为绿色，但permission_manage为红色，显示为黄色
                    console.log('显示黄色（manage有权限，permission无权限）');
                    allowBtn.classList.remove('active');
                    allowBtn.classList.add('partial');
                } else if (!hasManagePermission && permissionManagePermission) {
                    // manage相关权限为红色，但permission_manage为绿色，显示为黄色
                    console.log('显示黄色（manage无权限，permission有权限）');
                    allowBtn.classList.remove('active');
                    allowBtn.classList.add('partial');
                } else {
                    // 所有子权限都为红色，主权限显示为黄色
                    console.log('显示黄色（所有子权限都无权限）');
                    allowBtn.classList.remove('active');
                    allowBtn.classList.add('partial');
                }
            }

            console.log('更新完成，当前按钮类名:', allowBtn.className);
        }

        // 添加一个全局函数用于保存后端权限数据（不依赖Vue实例）
        function saveBackendPermissionsGlobal() {
            // 显示加载状态
            let loadingInstance;
            try {
                loadingInstance = ElementPlus.ElLoading.service({
                    fullscreen: true,
                    text: '正在保存权限数据...',
                    background: 'rgba(0, 0, 0, 0.7)'
                });

                // 构建权限数据
                const permissionsToUpdate = [];
                let basePermissions = [];

                // 首先尝试从全局变量中获取数据
                if (window.permissionsData.backend.length > 0) {
                    basePermissions = window.permissionsData.backend;
                }
                // 其次尝试从Vue实例获取数据（如果可用）
                else if (window.app && window.app._instance && window.app._instance.proxy.userPermissionsBackend) {
                    basePermissions = [...window.app._instance.proxy.userPermissionsBackend];
                }
                // 如果都没有数据，则尝试从页面DOM元素收集数据
                else {
                    const permissionRows = document.querySelectorAll('#backendPermissionsBody tr');
                    permissionRows.forEach(row => {
                        if (row.cells && row.cells.length >= 3) {
                            // 查找权限ID
                            const idCell = row.querySelector('[data-perm-id]');
                            if (idCell) {
                                const permId = idCell.getAttribute('data-perm-id');
                                if (permId) {
                                    // 获取部门和身份
                                    const department = row.cells[1].textContent.trim();
                                    const identityInput = row.cells[2].querySelector('input');
                                    const identity = identityInput ? identityInput.value.trim() : '';

                                    // 收集基本数据
                                    const perm = {
                                        id: permId,
                                        department: department,
                                        identity: identity
                                    };

                                    // 收集所有权限字段
                                    const permButtons = row.querySelectorAll('.permission-buttons');
                                    permButtons.forEach(btnGroup => {
                                        const field = btnGroup.getAttribute('data-field');
                                        if (field) {
                                            const allowBtn = btnGroup.querySelector('.permission-btn-allow.active');
                                            const value = allowBtn ? 0 : 1;
                                            perm[field] = value;
                                        }
                                    });

                                    basePermissions.push(perm);
                                }
                            }
                        }
                    });
                }

                // 如果没有获取到任何权限数据，显示错误并返回
                if (basePermissions.length === 0) {
                    if (loadingInstance) loadingInstance.close();
                    alert('未找到任何权限数据，请刷新页面再试');
                    return;
                }

                // 收集所有权限数据，包括新增和修改的
                for (const permission of basePermissions) {
                    // 处理新增记录的特殊逻辑
                    const isTempNewRecord = typeof permission.id === 'string' && permission.id.startsWith('temp_');

                    // 对于新记录，确保从DOM中获取最新的部门和身份值
                    if (isTempNewRecord) {
                        // 查找对应的DOM元素
                        const tr = document.querySelector(`tr [data-perm-id="${permission.id}"]`)?.closest('tr');
                        if (tr) {
                            // 重要：从select元素中获取选中的值，而不是整个选项列表
                            const departmentSelect = tr.querySelector('select.department-select');
                            if (departmentSelect) {
                                permission.department = departmentSelect.value;
                                console.log(`从DOM获取新记录部门: ID=${permission.id}, 部门=${permission.department}`);
                            }

                            const identityInput = tr.querySelector('input.identity-input');
                            if (identityInput) {
                                permission.identity = identityInput.value;
                            }
                        }
                    }

                    // 校验必填字段
                    if (!permission.department || permission.department.trim() === '') {
                        if (loadingInstance) loadingInstance.close();
                        alert('请选择部门！');
                        return;
                    }

                    if (!permission.identity || permission.identity.trim() === '') {
                        if (loadingInstance) loadingInstance.close();
                        alert('请输入身份！');
                        return;
                    }

                    // 构建基本权限对象 - 只包含ID、部门和身份
                    const permObj = {
                        id: String(permission.id),
                        department: permission.department,
                        identity: permission.identity
                    };

                    // 添加原始权限字段 - 保留原始值而不是默认为禁止
                    const fieldList = [
                        'user_review', 'review_all', 'review_group',
                        'user_management', 'manage_all', 'manage_group', 'permission_manage',
                        'form_preset', 'schedule_management', 'distribution_rules', 'distribution_plan'
                    ];

                    // 首先添加原始权限字段的值
                    fieldList.forEach(field => {
                        if (permission[field] !== undefined) {
                            permObj[field] = parseInt(permission[field]);
                        }
                    });

                    // 然后用已修改的值覆盖
                    if (window.permissionsData.modifiedPermissions[permission.id]) {
                        const mods = window.permissionsData.modifiedPermissions[permission.id];
                        // 添加所有修改过的字段
                        for (const field in mods) {
                            permObj[field] = parseInt(mods[field]);
                            console.log(`修改权限: ${permission.id} ${field}=${mods[field]}`);
                        }
                    }

                    // 对于新记录，添加显示序号
                    const isNewRecord = typeof permission.id === 'string' && permission.id.startsWith('temp_');
                    if (isNewRecord && permission.display_index) {
                        permObj.display_index = permission.display_index;
                        console.log(`新增记录的序号: ${permission.display_index}`);
                    }

                    // 添加调试日志
                    console.log(`准备${isNewRecord ? '新增' : '更新'}权限记录:`, permObj);
                    permissionsToUpdate.push(permObj);
                }

                console.log('准备更新的后端权限数据:', permissionsToUpdate);

                // 发送到后端保存
                fetch('/api/update-user-permissions-backend', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(permissionsToUpdate)
                })
                    .then(async response => {
                        // 关闭加载状态
                        if (loadingInstance) loadingInstance.close();

                        const text = await response.text();
                        console.log('服务器响应:', text);

                        if (response.ok) {
                            let data;
                            try {
                                data = JSON.parse(text);
                                console.log('解析的响应数据:', data);

                                // 显示成功消息
                                alert(`权限保存成功！${data.updated_count ? `更新了 ${data.updated_count} 条记录` : ''}${data.created_count ? `，新增了 ${data.created_count} 条记录` : ''}${data.frontend_created_count ? `，同步创建了 ${data.frontend_created_count} 条前端权限记录` : ''}`);

                                // 清空修改记录
                                window.permissionsData.modifiedPermissions = {};

                                // === 新增：先应用新的黄色按钮逻辑，然后再刷新页面 ===
                                try {
                                    console.log('保存成功，先应用新的黄色按钮逻辑...');
                                    // 更新后端权限数据并重新渲染表格
                                    if (window.app && window.app._instance) {
                                        // 更新页面数据，不直接刷新
                                        fetch('/api/user-permissions-backend')
                                            .then(response => response.json())
                                            .then(updatedData => {
                                                if (window.app && window.app._instance) {
                                                    window.app._instance.proxy.allUserPermissionsBackend = updatedData;
                                                    window.app._instance.proxy.userPermissionsBackend = [...updatedData];
                                                    window.permissionsData.backend = [...updatedData];

                                                    // 重新渲染表格
                                                    window.app._instance.proxy.renderPermissionsTableBackend();
                                                    console.log('已重新渲染表格以显示最新状态');

                                                    // 延迟2秒后刷新页面，让用户先看到正确的按钮颜色
                                                    setTimeout(() => {
                                                        window.location.reload();
                                                    }, 2000);
                                                } else {
                                                    // 如果Vue实例不可用，直接刷新
                                                    window.location.reload();
                                                }
                                            })
                                            .catch(error => {
                                                console.error('获取最新权限数据失败:', error);
                                                window.location.reload();
                                            });
                                    } else {
                                        // 如果Vue实例不可用，直接刷新
                                        window.location.reload();
                                    }
                                } catch (error) {
                                    console.error('应用新黄色按钮逻辑失败:', error);
                                    // 出错时也刷新页面
                                    window.location.reload();
                                }
                                // === 应用新逻辑结束 ===

                                return;
                            } catch (e) {
                                console.log('响应不是有效的JSON:', text);
                                throw new Error('服务器响应格式错误');
                            }
                        }

                        throw new Error(text || '保存失败');
                    })
                    .catch(error => {
                        // 确保关闭加载状态
                        if (loadingInstance) loadingInstance.close();
                        console.error('保存权限失败:', error);
                        alert(`保存权限失败: ${error.message}`);
                    });
            } catch (error) {
                // 确保关闭加载状态
                if (loadingInstance) loadingInstance.close();
                console.error('保存权限函数执行出错:', error);
                alert('保存权限时发生错误: ' + error.message);
            }
        }

        // 修改togglePermissionBackendNative函数
        function togglePermissionBackendNative(permId, field, value) {
            try {
                console.log('切换权限:', permId, field, value);

                // 在全局变量中存储该权限
                if (!window.permissionsData.modifiedPermissions[permId]) {
                    window.permissionsData.modifiedPermissions[permId] = {};
                }
                window.permissionsData.modifiedPermissions[permId][field] = value;

                // 查找DOM中对应的按钮组
                const buttonGroup = document.querySelector(`.permission-buttons[data-perm-id="${permId}"][data-field="${field}"]`);
                if (!buttonGroup) {
                    console.error('未找到按钮组:', permId, field);
                    return;
                }

                // 更新按钮状态
                const allowBtn = buttonGroup.querySelector('.permission-btn-allow');
                const denyBtn = buttonGroup.querySelector('.permission-btn-deny');
                const triangleBtn = buttonGroup.querySelector('.permission-btn-triangle');

                if (allowBtn) allowBtn.classList.remove('active');
                if (denyBtn) denyBtn.classList.remove('active');

                // 根据值设置按钮状态
                if (value === 0) {
                    if (allowBtn) allowBtn.classList.add('active');
                } else {
                    if (denyBtn) denyBtn.classList.add('active');
                }

                // 更新数据存储
                const permIndex = window.permissionsData.backend.findIndex(p => String(p.id) === String(permId));
                if (permIndex !== -1) {
                    window.permissionsData.backend[permIndex][field] = value;
                } else {
                    // 如果找不到，尝试从DOM中获取完整信息并添加到存储中
                    const permRow = buttonGroup.closest('tr');
                    if (permRow) {
                        const department = permRow.cells[1].textContent.trim();
                        const identityInput = permRow.cells[2].querySelector('input');
                        const identity = identityInput ? identityInput.value.trim() : '';

                        const newPerm = {
                            id: permId,
                            department: department,
                            identity: identity,
                            [field]: value // 设置当前修改的字段
                        };

                        window.permissionsData.backend.push(newPerm);
                        console.log('添加新的权限对象到存储:', newPerm);
                    }
                }

                // 如果Vue实例存在，也尝试更新Vue数据
                if (window.app && window.app._instance) {
                    try {
                        window.app._instance.proxy.togglePermissionBackend(permId, field, value);
                    } catch (vueError) {
                        console.warn('Vue实例更新失败，将只使用原生JS:', vueError);
                    }
                }

                console.log('权限已修改:', permId, field, value);
                console.log('当前修改记录:', window.permissionsData.modifiedPermissions);

                // 关键：刷新三角形按钮的样式
                if (triangleBtn) {
                    // 只针对主权限字段
                    if (
                        field === 'user_review' ||
                        field === 'user_management' ||
                        field === 'lead_table' ||
                        field === 'recycle_bin' ||
                        field === 'user_messages'
                    ) {
                        // 只有主权限字段为禁止且不是新角色行才禁用
                        if (value === 1 && !buttonGroup.closest('tr.new-permission-row')) {
                            triangleBtn.style.borderTopColor = '#dcdfe6';
                            triangleBtn.style.cursor = 'not-allowed';
                            triangleBtn.style.opacity = '0.6';
                        } else {
                            triangleBtn.style.borderTopColor = '#909399';
                            triangleBtn.style.cursor = 'pointer';
                            triangleBtn.style.opacity = '1';
                        }
                    }
                }

                // 主权限切换为允许时，刷新颜色
                if (value === 0 && (field === 'user_review' || field === 'user_management')) {
                    updateMainPermissionAllowBtnColor(permId, field);
                }

                // === 新增：子权限修改时也更新主权限颜色 ===
                if (['review_all', 'review_group'].includes(field)) {
                    updateMainPermissionAllowBtnColor(permId, field);
                } else if (['manage_all', 'manage_group', 'permission_manage'].includes(field)) {
                    updateMainPermissionAllowBtnColor(permId, field);
                }

            } catch (error) {
                console.error('切换权限出错:', error);
                alert('操作失败：' + error.message);
            }
        }

        // 生成前端权限按钮，支持三级弹窗
        function renderFrontendPermissionButtons(permission, field, hasTriangle = false) {
            // 获取对应字段的值
            const value = permission[field] !== undefined ? permission[field] : 1;

            // 提交线索页没有三级弹窗，不显示三角形按钮
            if (field === 'lead_submit') {
                hasTriangle = false;
            }

            // 为特殊字段准备弹窗内容
            let popupContent = '';
            let popupTitle = '';

            if (field === 'lead_table') {
                popupTitle = '线索页权限设置';
                popupContent = `
                    <div class="popup-title">${popupTitle}</div>
                    <div class="popup-item">
                        <div class="popup-item-label">查看所有线索</div>
                        <div class="permission-buttons" data-id="${permission.id}" data-field="check_all_lead">
                            <button class="permission-btn-allow ${permission.check_all_lead === 0 ? 'active' : ''}" 
                                onclick="app._instance.proxy.togglePermissionFrontend('${permission.id}', 'check_all_lead', 0)"
                                title="允许"></button>
                            <button class="permission-btn-deny ${permission.check_all_lead === 1 ? 'active' : ''}"
                                onclick="app._instance.proxy.togglePermissionFrontend('${permission.id}', 'check_all_lead', 1)"
                                title="禁止"></button>
                        </div>
                    </div>
                    <div class="popup-item">
                        <div class="popup-item-label">查看组内线索</div>
                        <div class="permission-buttons" data-id="${permission.id}" data-field="check_member_lead">
                            <button class="permission-btn-allow ${permission.check_member_lead === 0 ? 'active' : ''}" 
                                onclick="app._instance.proxy.togglePermissionFrontend('${permission.id}', 'check_member_lead', 0)"
                                title="允许"></button>
                            <button class="permission-btn-deny ${permission.check_member_lead === 1 ? 'active' : ''}"
                                onclick="app._instance.proxy.togglePermissionFrontend('${permission.id}', 'check_member_lead', 1)"
                                title="禁止"></button>
                        </div>
                    </div>
                    <div class="popup-item">
                        <div class="popup-item-label">查看个人线索</div>
                        <div class="permission-buttons" data-id="${permission.id}" data-field="check_mine_lead">
                            <button class="permission-btn-allow ${permission.check_mine_lead === 0 ? 'active' : ''}" 
                                onclick="app._instance.proxy.togglePermissionFrontend('${permission.id}', 'check_mine_lead', 0)"
                                title="允许"></button>
                            <button class="permission-btn-deny ${permission.check_mine_lead === 1 ? 'active' : ''}"
                                onclick="app._instance.proxy.togglePermissionFrontend('${permission.id}', 'check_mine_lead', 1)"
                                title="禁止"></button>
                        </div>
                    </div>
                    <div class="popup-item">
                        <div class="popup-item-label">编辑所有线索</div>
                        <div class="permission-buttons" data-id="${permission.id}" data-field="edit_all_lead">
                            <button class="permission-btn-allow ${permission.edit_all_lead === 0 ? 'active' : ''}" 
                                onclick="app._instance.proxy.togglePermissionFrontend('${permission.id}', 'edit_all_lead', 0)"
                                title="允许"></button>
                            <button class="permission-btn-deny ${permission.edit_all_lead === 1 ? 'active' : ''}"
                                onclick="app._instance.proxy.togglePermissionFrontend('${permission.id}', 'edit_all_lead', 1)"
                                title="禁止"></button>
                        </div>
                    </div>
                    <div class="popup-item">
                        <div class="popup-item-label">编辑组内线索</div>
                        <div class="permission-buttons" data-id="${permission.id}" data-field="edit_member_lead">
                            <button class="permission-btn-allow ${permission.edit_member_lead === 0 ? 'active' : ''}" 
                                onclick="app._instance.proxy.togglePermissionFrontend('${permission.id}', 'edit_member_lead', 0)"
                                title="允许"></button>
                            <button class="permission-btn-deny ${permission.edit_member_lead === 1 ? 'active' : ''}"
                                onclick="app._instance.proxy.togglePermissionFrontend('${permission.id}', 'edit_member_lead', 1)"
                                title="禁止"></button>
                        </div>
                    </div>
                    <div class="popup-item">
                        <div class="popup-item-label">编辑个人线索</div>
                        <div class="permission-buttons" data-id="${permission.id}" data-field="edit_mine_lead">
                            <button class="permission-btn-allow ${permission.edit_mine_lead === 0 ? 'active' : ''}" 
                                onclick="app._instance.proxy.togglePermissionFrontend('${permission.id}', 'edit_mine_lead', 0)"
                                title="允许"></button>
                            <button class="permission-btn-deny ${permission.edit_mine_lead === 1 ? 'active' : ''}"
                                onclick="app._instance.proxy.togglePermissionFrontend('${permission.id}', 'edit_mine_lead', 1)"
                                title="禁止"></button>
                        </div>
                    </div>
                    <div class="popup-item">
                        <div class="popup-item-label">禁止编辑权限</div>
                        <div class="permission-buttons" data-id="${permission.id}" data-field="forbid_edit">
                            <button class="permission-btn-allow ${permission.forbid_edit === 0 ? 'active' : ''}" 
                                onclick="app._instance.proxy.togglePermissionFrontend('${permission.id}', 'forbid_edit', 0)"
                                title="允许"></button>
                            <button class="permission-btn-deny ${permission.forbid_edit === 1 ? 'active' : ''}"
                                onclick="app._instance.proxy.togglePermissionFrontend('${permission.id}', 'forbid_edit', 1)"
                                title="禁止"></button>
                        </div>
                    </div>
                    <div class="popup-item">
                        <div class="popup-item-label">删除所有线索</div>
                        <div class="permission-buttons" data-id="${permission.id}" data-field="delete_all_lead">
                            <button class="permission-btn-allow ${permission.delete_all_lead === 0 ? 'active' : ''}" 
                                onclick="app._instance.proxy.togglePermissionFrontend('${permission.id}', 'delete_all_lead', 0)"
                                title="允许"></button>
                            <button class="permission-btn-deny ${permission.delete_all_lead === 1 ? 'active' : ''}"
                                onclick="app._instance.proxy.togglePermissionFrontend('${permission.id}', 'delete_all_lead', 1)"
                                title="禁止"></button>
                        </div>
                    </div>
                    <div class="popup-item">
                        <div class="popup-item-label">删除组内线索</div>
                        <div class="permission-buttons" data-id="${permission.id}" data-field="delete_member_lead">
                            <button class="permission-btn-allow ${permission.delete_member_lead === 0 ? 'active' : ''}" 
                                onclick="app._instance.proxy.togglePermissionFrontend('${permission.id}', 'delete_member_lead', 0)"
                                title="允许"></button>
                            <button class="permission-btn-deny ${permission.delete_member_lead === 1 ? 'active' : ''}"
                                onclick="app._instance.proxy.togglePermissionFrontend('${permission.id}', 'delete_member_lead', 1)"
                                title="禁止"></button>
                        </div>
                    </div>
                    <div class="popup-item">
                        <div class="popup-item-label">删除个人线索</div>
                        <div class="permission-buttons" data-id="${permission.id}" data-field="delete_mine_lead">
                            <button class="permission-btn-allow ${permission.delete_mine_lead === 0 ? 'active' : ''}" 
                                onclick="app._instance.proxy.togglePermissionFrontend('${permission.id}', 'delete_mine_lead', 0)"
                                title="允许"></button>
                            <button class="permission-btn-deny ${permission.delete_mine_lead === 1 ? 'active' : ''}"
                                onclick="app._instance.proxy.togglePermissionFrontend('${permission.id}', 'delete_mine_lead', 1)"
                                title="禁止"></button>
                        </div>
                    </div>
                    <div class="popup-item">
                        <div class="popup-item-label">导出线索</div>
                        <div class="permission-buttons" data-id="${permission.id}" data-field="export_lead_table">
                            <button class="permission-btn-allow ${permission.export_lead_table === 0 ? 'active' : ''}" 
                                onclick="app._instance.proxy.togglePermissionFrontend('${permission.id}', 'export_lead_table', 0)"
                                title="允许"></button>
                            <button class="permission-btn-deny ${permission.export_lead_table === 1 ? 'active' : ''}"
                                onclick="app._instance.proxy.togglePermissionFrontend('${permission.id}', 'export_lead_table', 1)"
                                title="禁止"></button>
                        </div>
                    </div>
                    <div class="popup-item">
                        <div class="popup-item-label">导入线索</div>
                        <div class="permission-buttons" data-id="${permission.id}" data-field="import_lead_table">
                            <button class="permission-btn-allow ${permission.import_lead_table === 0 ? 'active' : ''}" 
                                onclick="app._instance.proxy.togglePermissionFrontend('${permission.id}', 'import_lead_table', 0)"
                                title="允许"></button>
                            <button class="permission-btn-deny ${permission.import_lead_table === 1 ? 'active' : ''}"
                                onclick="app._instance.proxy.togglePermissionFrontend('${permission.id}', 'import_lead_table', 1)"
                                title="禁止"></button>
                        </div>
                    </div>
                    <div class="popup-footer">
                        <button class="popup-confirm" onclick="savePopupPermissions(this, '${permission.id}', 'lead_table'); forceCloseAllPopups();">确认</button>
                    </div>
                `;
            } else if (field === 'recycle_bin') {
                popupTitle = '回收站页权限设置';
                popupContent = `
                    <div class="popup-title">${popupTitle}</div>
                    <div class="popup-item">
                        <div class="popup-item-label">查看所有线索</div>
                        <div class="permission-buttons" data-id="${permission.id}" data-field="check_all_bin">
                            <button class="permission-btn-allow ${permission.check_all_bin === 0 ? 'active' : ''}" 
                                onclick="app._instance.proxy.togglePermissionFrontend('${permission.id}', 'check_all_bin', 0)"
                                title="允许"></button>
                            <button class="permission-btn-deny ${permission.check_all_bin === 1 ? 'active' : ''}"
                                onclick="app._instance.proxy.togglePermissionFrontend('${permission.id}', 'check_all_bin', 1)"
                                title="禁止"></button>
                        </div>
                    </div>
                    <div class="popup-item">
                        <div class="popup-item-label">查看组内线索</div>
                        <div class="permission-buttons" data-id="${permission.id}" data-field="check_member_bin">
                            <button class="permission-btn-allow ${permission.check_member_bin === 0 ? 'active' : ''}" 
                                onclick="app._instance.proxy.togglePermissionFrontend('${permission.id}', 'check_member_bin', 0)"
                                title="允许"></button>
                            <button class="permission-btn-deny ${permission.check_member_bin === 1 ? 'active' : ''}"
                                onclick="app._instance.proxy.togglePermissionFrontend('${permission.id}', 'check_member_bin', 1)"
                                title="禁止"></button>
                        </div>
                    </div>
                    <div class="popup-item">
                        <div class="popup-item-label">查看个人线索</div>
                        <div class="permission-buttons" data-id="${permission.id}" data-field="check_mine_bin">
                            <button class="permission-btn-allow ${permission.check_mine_bin === 0 ? 'active' : ''}" 
                                onclick="app._instance.proxy.togglePermissionFrontend('${permission.id}', 'check_mine_bin', 0)"
                                title="允许"></button>
                            <button class="permission-btn-deny ${permission.check_mine_bin === 1 ? 'active' : ''}"
                                onclick="app._instance.proxy.togglePermissionFrontend('${permission.id}', 'check_mine_bin', 1)"
                                title="禁止"></button>
                        </div>
                    </div>
                    <div class="popup-footer">
                        <button class="popup-confirm" onclick="savePopupPermissions(this, '${permission.id}', 'recycle_bin'); forceCloseAllPopups();">确认</button>
                    </div>
                `;
            } else if (field === 'lead_crm') {
                popupTitle = '客户关系管理权限设置';
                popupContent = `
                    <div class="popup-title">${popupTitle}</div>
                    <div class="popup-item">
                        <div class="popup-item-label">查看个人线索</div>
                        <div class="permission-buttons" data-id="${permission.id}" data-field="check_person_crm">
                            <button class="permission-btn-allow ${permission.check_person_crm === 0 ? 'active' : ''}" 
                                onclick="toggleCrmPermission('${permission.id}', 'check_person_crm', 0)"
                                title="允许"></button>
                            <button class="permission-btn-deny ${permission.check_person_crm === 1 ? 'active' : ''}"
                                onclick="toggleCrmPermission('${permission.id}', 'check_person_crm', 1)"
                                title="禁止"></button>
                        </div>
                    </div>
                    <div class="popup-item">
                        <div class="popup-item-label">查看组内线索</div>
                        <div class="permission-buttons" data-id="${permission.id}" data-field="check_group_crm">
                            <button class="permission-btn-allow ${permission.check_group_crm === 0 ? 'active' : ''}" 
                                onclick="toggleCrmPermission('${permission.id}', 'check_group_crm', 0)"
                                title="允许"></button>
                            <button class="permission-btn-deny ${permission.check_group_crm === 1 ? 'active' : ''}"
                                onclick="toggleCrmPermission('${permission.id}', 'check_group_crm', 1)"
                                title="禁止"></button>
                        </div>
                    </div>
                    <div class="popup-item">
                        <div class="popup-item-label">查看全部线索</div>
                        <div class="permission-buttons" data-id="${permission.id}" data-field="check_all_crm">
                            <button class="permission-btn-allow ${permission.check_all_crm === 0 ? 'active' : ''}" 
                                onclick="toggleCrmPermission('${permission.id}', 'check_all_crm', 0)"
                                title="允许"></button>
                            <button class="permission-btn-deny ${permission.check_all_crm === 1 ? 'active' : ''}"
                                onclick="toggleCrmPermission('${permission.id}', 'check_all_crm', 1)"
                                title="禁止"></button>
                        </div>
                    </div>
                    <div class="popup-footer">
                        <button class="popup-confirm" onclick="savePopupPermissions(this, '${permission.id}', 'lead_crm'); forceCloseAllPopups();">确认</button>
                    </div>
                `;
            } else if (field === 'user_messages') {
                popupTitle = '用户消息页权限设置';
                popupContent = `
                    <div class="popup-title">${popupTitle}</div>
                    <div class="popup-item">
                        <div class="popup-item-label">查看所有消息</div>
                        <div class="permission-buttons" data-id="${permission.id}" data-field="check_all_messages">
                            <button class="permission-btn-allow ${permission.check_all_messages === 0 ? 'active' : ''}" 
                                onclick="app._instance.proxy.togglePermissionFrontend('${permission.id}', 'check_all_messages', 0)"
                                title="允许"></button>
                            <button class="permission-btn-deny ${permission.check_all_messages === 1 ? 'active' : ''}"
                                onclick="app._instance.proxy.togglePermissionFrontend('${permission.id}', 'check_all_messages', 1)"
                                title="禁止"></button>
                        </div>
                    </div>
                    <div class="popup-item">
                        <div class="popup-item-label">查看组内消息</div>
                        <div class="permission-buttons" data-id="${permission.id}" data-field="check_member_messages">
                            <button class="permission-btn-allow ${permission.check_member_messages === 0 ? 'active' : ''}" 
                                onclick="app._instance.proxy.togglePermissionFrontend('${permission.id}', 'check_member_messages', 0)"
                                title="允许"></button>
                            <button class="permission-btn-deny ${permission.check_member_messages === 1 ? 'active' : ''}"
                                onclick="app._instance.proxy.togglePermissionFrontend('${permission.id}', 'check_member_messages', 1)"
                                title="禁止"></button>
                        </div>
                    </div>
                    <div class="popup-item">
                        <div class="popup-item-label">查看个人消息</div>
                        <div class="permission-buttons" data-id="${permission.id}" data-field="check_mine_messages">
                            <button class="permission-btn-allow ${permission.check_mine_messages === 0 ? 'active' : ''}" 
                                onclick="app._instance.proxy.togglePermissionFrontend('${permission.id}', 'check_mine_messages', 0)"
                                title="允许"></button>
                            <button class="permission-btn-deny ${permission.check_mine_messages === 1 ? 'active' : ''}"
                                onclick="app._instance.proxy.togglePermissionFrontend('${permission.id}', 'check_mine_messages', 1)"
                                title="禁止"></button>
                        </div>
                    </div>
                    <div class="popup-footer">
                        <button class="popup-confirm" onclick="savePopupPermissions(this, '${permission.id}', 'user_messages'); forceCloseAllPopups();">确认</button>
                    </div>
                `;
            } else if (field === 'check_shenpi') {
                popupTitle = '审批管理权限设置';
                popupContent = `
                    <div class="popup-title">${popupTitle}</div>
                    <div class="popup-item">
                        <div class="popup-item-label">查看所有审批</div>
                        <div class="permission-buttons" data-id="${permission.id}" data-field="check_all_shenpi">
                            <button class="permission-btn-allow ${permission.check_all_shenpi === 0 ? 'active' : ''}" 
                                onclick="app._instance.proxy.togglePermissionFrontend('${permission.id}', 'check_all_shenpi', 0)"
                                title="允许"></button>
                            <button class="permission-btn-deny ${permission.check_all_shenpi === 1 ? 'active' : ''}"
                                onclick="app._instance.proxy.togglePermissionFrontend('${permission.id}', 'check_all_shenpi', 1)"
                                title="禁止"></button>
                        </div>
                    </div>
                    <div class="popup-item">
                        <div class="popup-item-label">查看个人审批</div>
                        <div class="permission-buttons" data-id="${permission.id}" data-field="check_mine_shenpi">
                            <button class="permission-btn-allow ${permission.check_mine_shenpi === 0 ? 'active' : ''}" 
                                onclick="app._instance.proxy.togglePermissionFrontend('${permission.id}', 'check_mine_shenpi', 0)"
                                title="允许"></button>
                            <button class="permission-btn-deny ${permission.check_mine_shenpi === 1 ? 'active' : ''}"
                                onclick="app._instance.proxy.togglePermissionFrontend('${permission.id}', 'check_mine_shenpi', 1)"
                                title="禁止"></button>
                        </div>
                    </div>
                    <div class="popup-footer">
                        <button class="popup-confirm" onclick="savePopupPermissions(this, '${permission.id}', 'check_shenpi'); forceCloseAllPopups();">确认</button>
                    </div>
                `;
            } else if (field === 'gonghai_crm') {
                popupTitle = '公海客情管理权限设置';
                popupContent = `
                    <div class="popup-title">${popupTitle}</div>
                    <div class="popup-item">
                        <div class="popup-item-label">查看所有公海客情</div>
                        <div class="permission-buttons" data-id="${permission.id}" data-field="gonghai_all_crm">
                            <button class="permission-btn-allow ${permission.gonghai_all_crm === 0 ? 'active' : ''}"
                                onclick="app._instance.proxy.togglePermissionFrontend('${permission.id}', 'gonghai_all_crm', 0)" title="允许"></button>
                            <button class="permission-btn-deny ${permission.gonghai_all_crm === 1 ? 'active' : ''}"
                                onclick="app._instance.proxy.togglePermissionFrontend('${permission.id}', 'gonghai_all_crm', 1)" title="禁止"></button>
                        </div>
                    </div>
                    <div class="popup-item">
                        <div class="popup-item-label">查看组内公海客情</div>
                        <div class="permission-buttons" data-id="${permission.id}" data-field="gonghai_group_crm">
                            <button class="permission-btn-allow ${permission.gonghai_group_crm === 0 ? 'active' : ''}"
                                onclick="app._instance.proxy.togglePermissionFrontend('${permission.id}', 'gonghai_group_crm', 0)" title="允许"></button>
                            <button class="permission-btn-deny ${permission.gonghai_group_crm === 1 ? 'active' : ''}"
                                onclick="app._instance.proxy.togglePermissionFrontend('${permission.id}', 'gonghai_group_crm', 1)" title="禁止"></button>
                        </div>
                    </div>
                    <div class="popup-item">
                        <div class="popup-item-label">查看个人公海客情</div>
                        <div class="permission-buttons" data-id="${permission.id}" data-field="gonghai_person_crm">
                            <button class="permission-btn-allow ${permission.gonghai_person_crm === 0 ? 'active' : ''}"
                                onclick="app._instance.proxy.togglePermissionFrontend('${permission.id}', 'gonghai_person_crm', 0)" title="允许"></button>
                            <button class="permission-btn-deny ${permission.gonghai_person_crm === 1 ? 'active' : ''}"
                                onclick="app._instance.proxy.togglePermissionFrontend('${permission.id}', 'gonghai_person_crm', 1)" title="禁止"></button>
                        </div>
                    </div>
                    <div class="popup-footer">
                        <button class="popup-confirm" onclick="savePopupPermissions(this, '${permission.id}', 'gonghai_crm'); forceCloseAllPopups();">确认</button>
                    </div>
                `;
            }

            // 新增主按钮黄色机制
            let allowBtnClass = '';
            if (value === 0) {
                if (field === 'lead_table') {
                    // 检查check组是否有任意一个子权限为绿色
                    const checkGroupHasGreen = ['check_all_lead', 'check_member_lead', 'check_mine_lead'].some(
                        f => permission[f] === 0 || permission[f] === undefined
                    );

                    // 检查edit组是否有任意一个子权限为绿色
                    const editGroupHasGreen = ['edit_all_lead', 'edit_member_lead', 'edit_mine_lead', 'forbid_edit'].some(
                        f => permission[f] === 0 || permission[f] === undefined
                    );

                    // 检查delete组是否有任意一个子权限为绿色
                    const deleteGroupHasGreen = ['delete_all_lead', 'delete_member_lead', 'delete_mine_lead'].some(
                        f => permission[f] === 0 || permission[f] === undefined
                    );

                    // 获取其他独立权限的状态
                    const exportLeadTableIsGreen = permission.export_lead_table === 0 || permission.export_lead_table === undefined;
                    const importLeadTableIsGreen = permission.import_lead_table === 0 || permission.import_lead_table === undefined;
                    // 检查所有组和独立权限是否都为绿色
                    const allGroupsGreen = checkGroupHasGreen && editGroupHasGreen && deleteGroupHasGreen &&
                        exportLeadTableIsGreen && importLeadTableIsGreen;

                    // 检查所有组和独立权限是否都为红色
                    const allGroupsRed = !checkGroupHasGreen && !editGroupHasGreen && !deleteGroupHasGreen &&
                        !exportLeadTableIsGreen && !importLeadTableIsGreen;

                    if (allGroupsGreen) {
                        // 所有组都是绿色，主权限显示为绿色
                        allowBtnClass = 'active';
                    } else if (allGroupsRed) {
                        // 所有组都是红色，主权限显示为红色
                        allowBtnClass = '';
                    } else {
                        // 混合状态，主权限显示为黄色
                        allowBtnClass = 'partial';
                    }
                } else if (field === 'recycle_bin') {
                    // 对于recycle_bin，只要任意一个子权限为绿色，主权限就显示为绿色
                    const hasGreenPermission = ['check_all_bin', 'check_member_bin', 'check_mine_bin'].some(
                        f => permission[f] === 0 || permission[f] === undefined
                    );

                    if (hasGreenPermission) {
                        // 有允许的子权限，主权限显示为绿色
                        allowBtnClass = 'active';
                    } else {
                        // 全部子权限为禁止，主权限显示为黄色
                        allowBtnClass = 'partial';
                    }
                } else if (field === 'user_messages') {
                    // 对于user_messages，只要任意一个子权限为绿色，主权限就显示为绿色
                    const hasGreenPermission = ['check_all_messages', 'check_member_messages', 'check_mine_messages'].some(
                        f => permission[f] === 0 || permission[f] === undefined
                    );

                    if (hasGreenPermission) {
                        // 有允许的子权限，主权限显示为绿色
                        allowBtnClass = 'active';
                    } else {
                        // 全部子权限为禁止，主权限显示为黄色
                        allowBtnClass = 'partial';
                    }
                } else if (field === 'lead_crm') {
                    // 对于lead_crm（客户关系管理），检查子权限状态
                    const crmSubFields = ['check_person_crm', 'check_group_crm', 'check_all_crm'];
                    const hasGreenPermission = crmSubFields.some(f => permission[f] === 0);
                    const allRed = crmSubFields.every(f => permission[f] === 1);

                    if (hasGreenPermission && !allRed) {
                        // 有允许的子权限，主权限显示为绿色或黄色
                        const allGreen = crmSubFields.every(f => permission[f] === 0 || permission[f] === undefined);
                        allowBtnClass = allGreen ? 'active' : 'partial';
                    } else if (allRed) {
                        // 全部子权限为禁止，主权限显示为黄色
                        allowBtnClass = 'partial';
                    } else {
                        // 默认状态
                        allowBtnClass = 'active';
                    }
                } else if (field === 'check_shenpi') {
                    // 对于check_shenpi（审批管理），只要任意一个子权限为绿色，主权限就显示为绿色
                    const hasGreenPermission = ['check_all_shenpi', 'check_mine_shenpi'].some(
                        f => permission[f] === 0 || permission[f] === undefined
                    );

                    if (hasGreenPermission) {
                        // 有允许的子权限，主权限显示为绿色
                        allowBtnClass = 'active';
                    } else {
                        // 全部子权限为禁止，主权限显示为黄色
                        allowBtnClass = 'partial';
                    }
                } else if (field === 'gonghai_crm') {
                    // 对于gonghai_crm，检查子权限状态
                    const crmSubFields = ['gonghai_all_crm', 'gonghai_group_crm', 'gonghai_person_crm'];
                    const hasGreenPermission = crmSubFields.some(f => permission[f] === 0);
                    const allRed = crmSubFields.every(f => permission[f] === 1);

                    if (hasGreenPermission && !allRed) {
                        // 有允许的子权限，主权限显示为绿色或黄色
                        const allGreen = crmSubFields.every(f => permission[f] === 0 || permission[f] === undefined);
                        allowBtnClass = allGreen ? 'active' : 'partial';
                    } else if (allRed) {
                        // 全部子权限为禁止，主权限显示为黄色
                        allowBtnClass = 'partial';
                    } else {
                        // 默认状态
                        allowBtnClass = 'active';
                    }
                } else {
                    allowBtnClass = 'active';
                }
            }

            return `
                <div class="permission-buttons" style="position: relative;" data-id="${permission.id}" data-field="${field}">
                    <button class="permission-btn-allow ${allowBtnClass}" 
                        onclick="app._instance.proxy.togglePermissionFrontend('${permission.id}', '${field}', 0)"
                        title="允许"></button>
                    <button class="permission-btn-deny ${value === 1 ? 'active' : ''}"
                        onclick="app._instance.proxy.togglePermissionFrontend('${permission.id}', '${field}', 1)"
                        title="禁止"></button>
                    ${hasTriangle ? `
                        <div class="permission-btn-triangle" onclick="togglePopup(this)"></div>
                        <div class="permission-popup">
                            ${popupContent}
                        </div>
                    ` : ''}
                </div>
            `;
        }

        // 保存前端权限弹窗的权限设置
        function saveFrontendPopupPermissions(permId, permField) {
            try {
                console.log(`正在保存前端权限弹窗设置：ID=${permId}, 字段=${permField}`);

                // 查找弹窗容器
                const popupId = `frontend-popup-${permId}-${permField}`;
                const popup = document.getElementById(popupId);
                if (!popup) {
                    console.error(`找不到对应的弹窗容器：${popupId}`);
                    return;
                }

                // 获取权限记录
                let permissionRecord = null;
                const allPermissions = window.allUserPermissionsFrontendGlobal || [];

                for (let i = 0; i < allPermissions.length; i++) {
                    if (allPermissions[i].id == permId) {
                        permissionRecord = allPermissions[i];
                        break;
                    }
                }

                if (!permissionRecord) {
                    console.error(`找不到对应的权限记录：ID=${permId}`);
                    return;
                }

                // 收集弹窗中的所有权限设置
                const updatedPermission = {};

                // 主字段 - lead_table, recycle_bin, user_messages
                updatedPermission[permField] = permissionRecord[permField];

                // 子字段映射
                const subFieldsMapping = {
                    'lead_table': ['check_all_lead', 'check_member_lead', 'check_mine_lead',
                        'edit_all_lead', 'edit_member_lead', 'edit_mine_lead',
                        'forbid_edit', 'delete_all_lead', 'delete_member_lead',
                        'delete_mine_lead', 'export_lead_table', 'import_lead_table'],
                    'recycle_bin': ['check_all_bin', 'check_member_bin', 'check_mine_bin'],
                    'user_messages': ['check_all_messages', 'check_member_messages', 'check_mine_messages'],
                    'lead_crm': ['check_person_crm', 'check_group_crm', 'check_all_crm'],
                    'gonghai_crm': ['gonghai_all_crm', 'gonghai_group_crm', 'gonghai_person_crm'], // 新增
                    'check_shenpi': ['check_all_shenpi', 'check_mine_shenpi']
                };

                // 根据当前字段获取对应的子字段
                const subFields = subFieldsMapping[permField] || [];

                // 获取弹窗中每个子字段的按钮组状态
                subFields.forEach(subField => {
                    const radioAll = popup.querySelector(`input[name="${subField}-radio"][value="0"]`);
                    const radioGroup = popup.querySelector(`input[name="${subField}-radio"][value="1"]`);
                    const radioMine = popup.querySelector(`input[name="${subField}-radio"][value="2"]`);

                    if (radioAll && radioAll.checked) {
                        updatedPermission[subField] = 0;
                    } else if (radioGroup && radioGroup.checked) {
                        updatedPermission[subField] = 1;
                    } else if (radioMine && radioMine.checked) {
                        updatedPermission[subField] = 2;
                    }
                });

                console.log('更新的权限设置：', updatedPermission);

                // 更新权限记录
                Object.keys(updatedPermission).forEach(key => {
                    permissionRecord[key] = updatedPermission[key];
                });

                // 更新UI
                renderPermissionsTableFrontendGlobal();

                // 关闭弹窗
                hideAllPopups();

                // 显示提示
                showNotification('权限设置已保存！');

            } catch (error) {
                console.error('保存前端权限弹窗设置时出错：', error);
                alert('保存权限设置失败，请刷新页面后重试');
            }
        }

        // 切换前端权限弹窗中的权限状态
        function toggleFrontendPopupPermission(event, permId, permField, subField) {
            try {
                console.log(`切换前端权限弹窗 ID=${permId}, 字段=${permField}, 子字段=${subField}`);

                // 获取点击的按钮组
                const buttonGroup = event.target.closest('.btn-group');
                if (!buttonGroup) {
                    console.error('找不到按钮组元素');
                    return;
                }

                // 移除所有按钮的激活状态
                buttonGroup.querySelectorAll('.btn').forEach(btn => {
                    btn.classList.remove('active');
                    btn.classList.remove('btn-success');
                    btn.classList.remove('btn-primary');
                    btn.classList.remove('btn-secondary');
                    btn.classList.add('btn-outline-secondary');
                });

                // 激活当前点击的按钮
                const clickedButton = event.target.closest('.btn');
                clickedButton.classList.add('active');
                clickedButton.classList.remove('btn-outline-secondary');

                // 根据不同的值设置不同的按钮样式
                const value = parseInt(clickedButton.getAttribute('data-value'));
                if (value === 0) {
                    clickedButton.classList.add('btn-success');
                } else if (value === 1) {
                    clickedButton.classList.add('btn-primary');
                } else if (value === 2) {
                    clickedButton.classList.add('btn-secondary');
                }

                // 更新相应的单选按钮
                const radioInput = buttonGroup.querySelector(`input[name="${subField}-radio"][value="${value}"]`);
                if (radioInput) {
                    radioInput.checked = true;
                }

                console.log(`已将 ${subField} 权限值更新为 ${value}（仅更新UI，确认后才会保存）`);
            } catch (error) {
                console.error('切换前端权限弹窗状态时出错：', error);
            }
        }

        // 根据选项卡保存对应权限
        function saveAllPermissions() {
            // 获取当前活动的选项卡
            const activeTab = document.querySelector('.permission-tab-pane.active').id;

            // 根据活动选项卡调用对应的保存函数
            if (activeTab === 'managementTab') {
                // 保存后端权限
                saveBackendPermissionsGlobal();
            } else if (activeTab === 'distributionTab') {
                // 保存前端权限
                app._instance.proxy.saveFrontendPermissions();
            }
        }

        // 更新主权限按钮颜色
        function updateMainPermissionAllowBtnColorFrontend(permId, subField) {
            console.log(`开始更新前端主权限按钮颜色: permId=${permId}, subField=${subField}`);

            let mainField = null;

                                        // 确定主字段
            if (['check_all_lead', 'check_member_lead', 'check_mine_lead',
                'edit_all_lead', 'edit_member_lead', 'edit_mine_lead',
                'forbid_edit', 'delete_all_lead', 'delete_member_lead',
                'delete_mine_lead', 'export_lead_table', 'import_lead_table'].includes(subField)) {
                mainField = 'lead_table';
            } else if (['check_all_bin', 'check_member_bin', 'check_mine_bin'].includes(subField)) {
                mainField = 'recycle_bin';
            } else if (['check_all_messages', 'check_member_messages', 'check_mine_messages'].includes(subField)) {
                mainField = 'user_messages';
            } else if (['check_person_crm', 'check_group_crm', 'check_all_crm'].includes(subField)) {
                mainField = 'lead_crm';
            } else if (['gonghai_crm', 'gonghai_all_crm', 'gonghai_group_crm', 'gonghai_person_crm'].includes(subField)) {
                mainField = 'gonghai_crm';
            } else {
                console.log(`未知的子字段: ${subField}，无法确定主字段`);
                return;
            }

            console.log(`确定的主字段: ${mainField}`);

            // 获取主按钮 - 尝试两种选择器
            let mainBtnGroup = document.querySelector(`.permission-buttons[data-id="${permId}"][data-field="${mainField}"]`);

            // 如果第一种选择器失败，尝试使用另一种选择器格式
            if (!mainBtnGroup) {
                console.log(`无法找到按钮组，尝试替代选择器: .permission-buttons[data-perm-id="${permId}"][data-field="${mainField}"]`);
                mainBtnGroup = document.querySelector(`.permission-buttons[data-perm-id="${permId}"][data-field="${mainField}"]`);
            }

            if (!mainBtnGroup) {
                console.log(`无法找到前端权限按钮组: data-id="${permId}" data-field="${mainField}"`);

                // 尝试列出所有权限按钮
                const allButtons = document.querySelectorAll('.permission-buttons');
                console.log(`页面上共有 ${allButtons.length} 个权限按钮组`);
                console.log('搜索匹配的按钮组...');

                allButtons.forEach((btn, index) => {
                    const dataId = btn.getAttribute('data-id');
                    const dataPermId = btn.getAttribute('data-perm-id');
                    const dataField = btn.getAttribute('data-field');
                    if ((dataId === permId || dataPermId === permId) && dataField === mainField) {
                        console.log(`找到匹配按钮组 #${index}: data-id=${dataId}, data-perm-id=${dataPermId}, data-field=${dataField}`);
                        mainBtnGroup = btn;
                    } else if (dataId === permId || dataPermId === permId) {
                        console.log(`找到部分匹配按钮组 #${index}: data-id=${dataId}, data-perm-id=${dataPermId}, data-field=${dataField}`);
                    }
                });

                if (!mainBtnGroup) {
                    return;
                }
            }

            const allowBtn = mainBtnGroup.querySelector('.permission-btn-allow');
            if (!allowBtn) {
                console.log(`找不到主权限允许按钮`);
                return;
            }

            // 获取当前权限对象
            let permission = null;
            if (window.app && window.app._instance) {
                permission = window.app._instance.proxy.userPermissionsFrontend.find(p => String(p.id) === String(permId));
                console.log(`找到权限对象: ${permission ? 'true' : 'false'}`);
                if (permission) {
                    console.log(`权限对象信息: id=${permission.id}, ${mainField}=${permission[mainField]}`);
                }
            }

            if (!permission) {
                console.log(`找不到ID为${permId}的权限对象`);
                return;
            }

            // 如果主字段不是允许状态，则不需要处理黄色逻辑
            if (permission[mainField] !== 0) {
                console.log(`主字段${mainField}不是允许状态(${permission[mainField]})，移除所有颜色状态`);
                allowBtn.classList.remove('partial');
                allowBtn.classList.remove('active');
                return;
            }

            // 根据主字段的不同应用不同的逻辑
            if (mainField === 'lead_table') {
                // 对于lead_table，需要计算check、edit、delete这三组的状态

                // 检查check组是否有任意一个子权限为绿色
                const checkFields = ['check_all_lead', 'check_member_lead', 'check_mine_lead'];
                const checkGroupValues = checkFields.map(f => permission[f]);
                const checkGroupHasGreen = checkFields.some(f => permission[f] === 0 || permission[f] === undefined);

                // 检查edit组是否有任意一个子权限为绿色
                const editFields = ['edit_all_lead', 'edit_member_lead', 'edit_mine_lead', 'forbid_edit'];
                const editGroupValues = editFields.map(f => permission[f]);
                const editGroupHasGreen = editFields.some(f => permission[f] === 0 || permission[f] === undefined);

                // 检查delete组是否有任意一个子权限为绿色
                const deleteFields = ['delete_all_lead', 'delete_member_lead', 'delete_mine_lead'];
                const deleteGroupValues = deleteFields.map(f => permission[f]);
                const deleteGroupHasGreen = deleteFields.some(f => permission[f] === 0 || permission[f] === undefined);

                // 获取其他独立权限的状态
                const exportLeadTableIsGreen = permission.export_lead_table === 0 || permission.export_lead_table === undefined;
                const importLeadTableIsGreen = permission.import_lead_table === 0 || permission.import_lead_table === undefined;

                // 检查所有组和独立权限是否都为绿色
                const allGroupsGreen = checkGroupHasGreen && editGroupHasGreen && deleteGroupHasGreen &&
                    exportLeadTableIsGreen && importLeadTableIsGreen;

                // 检查所有组和独立权限是否都为红色
                const allGroupsRed = !checkGroupHasGreen && !editGroupHasGreen && !deleteGroupHasGreen &&
                    !exportLeadTableIsGreen && !importLeadTableIsGreen;

                console.log(`lead_table子字段状态:
                    check组: ${JSON.stringify(checkGroupValues)} -> ${checkGroupHasGreen ? '绿色' : '红色'}
                    edit组: ${JSON.stringify(editGroupValues)} -> ${editGroupHasGreen ? '绿色' : '红色'}
                    delete组: ${JSON.stringify(deleteGroupValues)} -> ${deleteGroupHasGreen ? '绿色' : '红色'}
                    export_lead_table: ${permission.export_lead_table} -> ${exportLeadTableIsGreen ? '绿色' : '红色'}
                    import_lead_table: ${permission.import_lead_table} -> ${importLeadTableIsGreen ? '绿色' : '红色'}
                    结论: ${allGroupsGreen ? '全绿' : (allGroupsRed ? '全红' : '混合')}`);

                if (allGroupsGreen) {
                    // 所有组都是绿色，主权限显示为绿色
                    console.log(`所有组都是绿色，设置主权限显示为绿色`);
                    allowBtn.classList.add('active');
                    allowBtn.classList.remove('partial');
                } else if (allGroupsRed) {
                    // 所有组都是红色，主权限显示为红色
                    console.log(`所有组都是红色，设置主权限显示为红色`);
                    allowBtn.classList.remove('active');
                    allowBtn.classList.remove('partial');
                } else {
                    // 混合状态，主权限显示为黄色
                    console.log(`混合状态，设置主权限显示为黄色`);
                    allowBtn.classList.remove('active');
                    allowBtn.classList.add('partial');
                }
            }
            else if (mainField === 'recycle_bin') {
                // 对于recycle_bin，只要任意一个子权限为绿色，主权限就显示为绿色
                const binFields = ['check_all_bin', 'check_member_bin', 'check_mine_bin'];
                const binValues = binFields.map(f => permission[f]);
                const hasGreenPermission = binFields.some(
                    f => permission[f] === 0 || permission[f] === undefined
                );

                console.log(`recycle_bin子字段状态: ${JSON.stringify(binValues)} -> ${hasGreenPermission ? '有绿色' : '全红'}`);

                if (hasGreenPermission) {
                    // 有允许的子权限，主权限显示为绿色
                    console.log(`有允许的子权限，设置主权限显示为绿色`);
                    allowBtn.classList.add('active');
                    allowBtn.classList.remove('partial');
                } else {
                    // 全部子权限为禁止，主权限显示为黄色
                    console.log(`全部子权限为禁止，设置主权限显示为黄色`);
                    allowBtn.classList.remove('active');
                    allowBtn.classList.add('partial');
                }
            }
            else if (mainField === 'user_messages') {
                // 对于user_messages，只要任意一个子权限为绿色，主权限就显示为绿色
                const messageFields = ['check_all_messages', 'check_member_messages', 'check_mine_messages'];
                const messageValues = messageFields.map(f => permission[f]);
                const hasGreenPermission = messageFields.some(
                    f => permission[f] === 0 || permission[f] === undefined
                );

                console.log(`user_messages子字段状态: ${JSON.stringify(messageValues)} -> ${hasGreenPermission ? '有绿色' : '全红'}`);

                if (hasGreenPermission) {
                    // 有允许的子权限，主权限显示为绿色
                    console.log(`有允许的子权限，设置主权限显示为绿色`);
                    allowBtn.classList.add('active');
                    allowBtn.classList.remove('partial');
                } else {
                    // 全部子权限为禁止，主权限显示为黄色
                    console.log(`全部子权限为禁止，设置主权限显示为黄色`);
                    allowBtn.classList.remove('active');
                    allowBtn.classList.add('partial');
                }
            }
            else if (mainField === 'check_shenpi') {
                // 对于check_shenpi（审批管理），只要任意一个子权限为绿色，主权限就显示为绿色
                const shenpiFields = ['check_all_shenpi', 'check_mine_shenpi'];
                const shenpiValues = shenpiFields.map(f => permission[f]);
                const hasGreenPermission = shenpiFields.some(
                    f => permission[f] === 0 || permission[f] === undefined
                );

                console.log(`check_shenpi子字段状态: ${JSON.stringify(shenpiValues)} -> ${hasGreenPermission ? '有绿色' : '全红'}`);

                if (hasGreenPermission) {
                    // 有允许的子权限，主权限显示为绿色
                    console.log(`有允许的子权限，设置主权限显示为绿色`);
                    allowBtn.classList.add('active');
                    allowBtn.classList.remove('partial');
                } else {
                    // 全部子权限为禁止，主权限显示为黄色
                    console.log(`全部子权限为禁止，设置主权限显示为黄色`);
                    allowBtn.classList.remove('active');
                    allowBtn.classList.add('partial');
                }
            } else if (mainField === 'gonghai_crm') {
                // 对于gonghai_crm，检查子权限状态
                const crmSubFields = ['gonghai_all_crm', 'gonghai_group_crm', 'gonghai_person_crm'];
                const hasGreenPermission = crmSubFields.some(f => permission[f] === 0);
                const allRed = crmSubFields.every(f => permission[f] === 1);

                if (hasGreenPermission && !allRed) {
                    // 有允许的子权限，主权限显示为绿色或黄色
                    const allGreen = crmSubFields.every(f => permission[f] === 0 || permission[f] === undefined);
                    allowBtnClass = allGreen ? 'active' : 'partial';
                } else if (allRed) {
                    // 全部子权限为禁止，主权限显示为黄色
                    allowBtnClass = 'partial';
                } else {
                    // 默认状态
                    allowBtnClass = 'active';
                }
            }

            console.log(`前端主权限颜色更新完成: ${mainField}`);
        }

        // 添加全局函数处理权限管理面板过滤器变化
        function handlePermFilterChange(selectElement) {
            try {
                const department = selectElement.value;
                console.log('过滤器选择变更:', department);

                // 直接过滤并更新DOM（不依赖Vue实例）
                if (!window.allBackendPermissions || !window.allFrontendPermissions) {
                    console.warn('权限数据尚未完全加载');
                    alert('请等待权限数据加载完成后再尝试过滤');
                    return;
                }

                const allBackendPerms = window.allBackendPermissions;
                const allFrontendPerms = window.allFrontendPermissions;

                // 过滤数据
                let filteredBackendData = [...allBackendPerms];
                let filteredFrontendData = [...allFrontendPerms];

                if (department) {
                    filteredBackendData = filteredBackendData.filter(item => item.department === department);
                    filteredFrontendData = filteredFrontendData.filter(item => item.department === department);
                }

                console.log(`过滤后: 后端=${filteredBackendData.length}, 前端=${filteredFrontendData.length}`);

                // 确定当前活动的选项卡并渲染对应表格
                const activeTab = document.querySelector('.permission-tab-pane.active');
                if (!activeTab) {
                    console.error('未找到活动选项卡');
                    return;
                }

                const activeTabId = activeTab.id;
                console.log('当前活动选项卡:', activeTabId);

                // 根据当前活动的选项卡渲染对应表格
                if (activeTabId === 'managementTab') {
                    console.log('渲染后端权限表格');
                    renderPermTableDirectly('#managementTab tbody', filteredBackendData, 'backend');
                } else {
                    console.log('渲染前端权限表格');
                    renderPermTableDirectly('#distributionTab tbody', filteredFrontendData, 'frontend');
                }

                // 显示过滤成功消息
                ElementPlus.ElMessage({
                    message: `已过滤显示"${department || '全部'}"部门的权限设置`,
                    type: 'success',
                    duration: 2000
                });
            } catch (error) {
                console.error('过滤器处理出错:', error);
                alert('过滤操作失败: ' + error.message);
            }
        }

        // 添加一个不依赖Vue实例的表格渲染函数
        function renderPermTableDirectly(selector, data, type) {
            try {
                // 修复选择器处理逻辑
                let tbody;
                if (selector === 'backendPermissionsBody') {
                    // 直接使用getElementById更可靠
                    tbody = document.getElementById('backendPermissionsBody');
                } else {
                    // 对于其他选择器使用querySelector
                    tbody = document.querySelector(selector);
                }

                if (!tbody) {
                    console.error(`未找到表格元素: ${selector}`);
                    // 添加额外的调试信息
                    const allTables = document.querySelectorAll('table');
                    console.log(`页面上共有 ${allTables.length} 个表格`);
                    const allTbodies = document.querySelectorAll('tbody');
                    console.log(`页面上共有 ${allTbodies.length} 个tbody元素`);

                    // 尝试使用备选方法查找表格
                    if (type === 'backend') {
                        tbody = document.querySelector('#managementTab tbody');
                        if (!tbody) {
                            throw new Error(`未能找到后端权限表格(#managementTab tbody)`);
                        }
                        console.log('使用备选选择器找到后端表格');
                    } else {
                        tbody = document.querySelector('#distributionTab tbody');
                        if (!tbody) {
                            throw new Error(`未能找到前端权限表格(#distributionTab tbody)`);
                        }
                        console.log('使用备选选择器找到前端表格');
                    }
                }

                // 清空表格
                tbody.innerHTML = '';

                // 剩余代码不变...
                // 显示空状态
                if (!data || data.length === 0) {
                    tbody.innerHTML = `
                        <tr>
                            <td colspan="10" style="text-align: center; padding: 20px;">
                                <div style="color: #909399;">
                                    <i class="fas fa-info-circle" style="font-size: 24px; margin-bottom: 10px;"></i>
                                    <div>没有符合条件的权限数据</div>
                                </div>
                            </td>
                        </tr>
                    `;
                    return;
                }

                // 根据类型渲染不同的表格
                if (type === 'backend') {
                    // 渲染后端权限表格
                    data.forEach((permission, index) => {
                        // 使用现有的renderBackendPermissionButtons函数
                        const row = document.createElement('tr');
                        row.innerHTML = `
                            <td>${index + 1}</td>
                            <td>${permission.department || ''}</td>
                            <td>${permission.identity || ''}</td>
                            <td>${renderBackendPermissionButtons(permission, 'user_review', true)}</td>
                            <td>${renderBackendPermissionButtons(permission, 'user_management', true)}</td>
                            <td>${renderBackendPermissionButtons(permission, 'form_preset')}</td>
                            <td>${renderBackendPermissionButtons(permission, 'schedule_management')}</td>
                            <td>${renderBackendPermissionButtons(permission, 'distribution_rules')}</td>
                            <td>${renderBackendPermissionButtons(permission, 'distribution_plan')}</td>
                            <td>
                                <button class="reject-btn" style="padding: 5px 10px;" 
                                    onclick="app._instance.proxy.deletePermission('${permission.department}', '${permission.identity}')">
                                    删除
                                </button>
                            </td>
                        `;
                        tbody.appendChild(row);
                    });

                    // 添加"添加新身份"按钮行
                    tbody.insertAdjacentHTML('beforeend', `
                        <tr>
                            <td colspan="15" style="text-align: center; padding: 15px;">
                                <button class="add-permission-btn" onclick="app._instance.proxy.addNewPermissionBackend()">
                                    <i class="fas fa-plus"></i> 添加新身份
                                </button>
                            </td>
                        </tr>
                    `);
                } else {
                    // 渲染前端权限表格
                    data.forEach((permission, index) => {
                        const row = document.createElement('tr');
                        row.innerHTML = `
                            <td>${index + 1}</td>
                            <td>${permission.department || ''}</td>
                            <td>${permission.identity || ''}</td>
                            <td>${renderFrontendPermissionButtons(permission, 'lead_table', true)}</td>
                            <td>${renderFrontendPermissionButtons(permission, 'lead_submit', true)}</td>
                            <td>${renderFrontendPermissionButtons(permission, 'lead_crm', true)}</td>
                            <td>${renderFrontendPermissionButtons(permission, 'gonghai_crm', true)}</td>
                            <td>${renderFrontendPermissionButtons(permission, 'recycle_bin', true)}</td>
                            <td>${renderFrontendPermissionButtons(permission, 'user_messages', true)}</td>
                            <td>
                                <button class="reject-btn" style="padding: 5px 10px;" 
                                    onclick="app._instance.proxy.deletePermission('${permission.department}', '${permission.identity}')">
                                    删除
                                </button>
                            </td>
                        `;
                        tbody.appendChild(row);
                    });

                    // 添加"添加新角色"按钮行
                    tbody.insertAdjacentHTML('beforeend', `
                        <tr>
                            <td colspan="25" style="text-align: center; padding: 15px;">
                                <button class="add-permission-btn" onclick="app._instance.proxy.addNewPermissionFrontend()">
                                    <i class="fas fa-plus"></i> 添加新角色
                                </button>
                            </td>
                        </tr>
                    `);
                }
            } catch (error) {
                console.error('渲染表格出错:', error);
                alert('渲染表格失败: ' + error.message);
            }
        }

        // 全局权限数据存储对象
        window.permissionsData = {
            modifiedPermissions: {},
            modifiedFrontendPermissions: {},
            backend: [],
            frontend: []
        };

        // CRM权限管理函数
        // 切换CRM权限状态
        function toggleCrmPermission(permId, field, value) {
            try {
                console.log(`切换CRM权限: permId=${permId}, field=${field}, value=${value}`);
                
                // 获取按钮组
                const buttonGroup = document.querySelector(`.permission-buttons[data-id="${permId}"][data-field="${field}"]`);
                if (!buttonGroup) {
                    console.error(`找不到CRM权限按钮组: data-id="${permId}" data-field="${field}"`);
                    return;
                }

                // 移除所有按钮的激活状态
                buttonGroup.querySelectorAll('.permission-btn-allow, .permission-btn-deny').forEach(btn => {
                    btn.classList.remove('active');
                });

                // 激活当前点击的按钮
                if (value === 0) {
                    buttonGroup.querySelector('.permission-btn-allow').classList.add('active');
                } else {
                    buttonGroup.querySelector('.permission-btn-deny').classList.add('active');
                }

                // 暂存权限值（实际保存在点击确认按钮时进行）
                if (!window.crmPermissionsTemp) {
                    window.crmPermissionsTemp = {};
                }
                if (!window.crmPermissionsTemp[permId]) {
                    window.crmPermissionsTemp[permId] = {};
                }
                window.crmPermissionsTemp[permId][field] = value;

                // === 新增：审批管理权限互斥规则处理 ===
                // 当某个审批权限字段设置为"允许"(值为0)时，设置互斥字段为"禁止"(值为1)
                if (value === 0) {
                    let oppositeField = null;
                    
                    // 处理审批管理权限的互斥
                    if (field === 'check_all_shenpi') {
                        oppositeField = 'check_mine_shenpi';
                    } else if (field === 'check_mine_shenpi') {
                        oppositeField = 'check_all_shenpi';
                    }

                    if (oppositeField) {
                        // 将互斥字段设置为1（禁止）
                        window.crmPermissionsTemp[permId][oppositeField] = 1;

                        // 查找互斥字段的按钮组并更新UI
                        const popup = buttonGroup.closest('.permission-popup');
                        if (popup) {
                            const oppositeButtonGroup = popup.querySelector(`.permission-buttons[data-field="${oppositeField}"]`);
                            if (oppositeButtonGroup) {
                                const oppAllowBtn = oppositeButtonGroup.querySelector('.permission-btn-allow');
                                const oppDenyBtn = oppositeButtonGroup.querySelector('.permission-btn-deny');

                                if (oppAllowBtn) oppAllowBtn.classList.remove('active');
                                if (oppDenyBtn) oppDenyBtn.classList.add('active');
                            }
                        }
                        
                        console.log(`审批管理权限互斥处理: ${field}=0 导致 ${oppositeField}=1`);
                    }
                }
                // === 审批管理权限互斥规则处理结束 ===
                
                console.log(`CRM权限临时存储: ${field}=${value}`);
            } catch (error) {
                console.error('切换CRM权限出错:', error);
            }
        }

        // 保存CRM权限设置
        function saveCrmPermissions(permId) {
            try {
                console.log(`保存CRM权限设置: permId=${permId}`);
                
                if (!window.crmPermissionsTemp || !window.crmPermissionsTemp[permId]) {
                    console.log('没有CRM权限更改，直接关闭弹窗');
                    return;
                }

                const crmPermissions = window.crmPermissionsTemp[permId];
                console.log('即将保存的CRM权限:', crmPermissions);

                // 显示加载状态
                const loadingInstance = ElementPlus.ElLoading.service({
                    fullscreen: true,
                    text: '正在保存CRM权限...',
                    background: 'rgba(0, 0, 0, 0.7)'
                });

                // 调用后端API保存CRM权限
                // TODO: 这里需要替换为实际的后端API地址
                fetch('/api/update-crm-permissions', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        permission_id: permId,
                        permissions: crmPermissions
                    })
                })
                .then(async response => {
                    loadingInstance.close();
                    
                    if (response.ok) {
                        const result = await response.json();
                        console.log('CRM权限保存成功:', result);
                        
                        // 清除临时数据
                        delete window.crmPermissionsTemp[permId];
                        
                        // 更新本地权限数据
                        if (window.app && window.app._instance) {
                            const permission = window.app._instance.proxy.userPermissionsFrontend.find(p => String(p.id) === String(permId));
                            if (permission) {
                                Object.assign(permission, crmPermissions);
                                console.log('已更新本地CRM权限数据');
                            }
                        }
                        
                        ElementPlus.ElMessage({
                            message: 'CRM权限保存成功！',
                            type: 'success',
                            duration: 2000
                        });
                    } else {
                        throw new Error('保存失败');
                    }
                })
                .catch(error => {
                    loadingInstance.close();
                    console.error('保存CRM权限失败:', error);
                    ElementPlus.ElMessage({
                        message: `保存CRM权限失败: ${error.message}`,
                        type: 'error',
                        duration: 2000
                    });
                });
                
            } catch (error) {
                console.error('保存CRM权限出错:', error);
                ElementPlus.ElMessage({
                    message: `保存CRM权限失败: ${error.message}`,
                    type: 'error',
                    duration: 2000
                });
            }
        }

        // 页面加载完成后执行的初始化函数
        document.addEventListener('DOMContentLoaded', function () {
            console.log('页面加载完成，开始初始化...');

            // 确保权限数据对象已初始化
            if (!window.permissionsData) {
                window.permissionsData = {
                    modifiedPermissions: {},
                    modifiedFrontendPermissions: {},
                    backend: [],
                    frontend: []
                };
            }

            // 初始化CRM权限临时存储
            window.crmPermissionsTemp = {};

            // 等待Vue应用初始化完成后，确保更新黄色按钮逻辑
            setTimeout(function () {
                console.log('确保应用新的黄色按钮逻辑...');
                if (window.app && window.app._instance) {
                    try {
                        // 尝试重新渲染后端权限表格以应用黄色按钮逻辑
                        window.app._instance.proxy.renderPermissionsTableBackend();
                        console.log('已重新渲染后端权限表格');

                        // 尝试重新渲染前端权限表格
                        window.app._instance.proxy.renderPermissionsTableFrontend();
                        console.log('已重新渲染前端权限表格');

                        // 循环更新所有前端权限的主按钮颜色
                        console.log('更新所有前端权限主按钮颜色...');
                        if (window.app._instance.proxy.userPermissionsFrontend && window.app._instance.proxy.userPermissionsFrontend.length > 0) {
                            window.app._instance.proxy.userPermissionsFrontend.forEach(perm => {
                                // 更新lead_table主按钮颜色
                                updateMainPermissionAllowBtnColorFrontend(perm.id, 'check_all_lead');
                                // 更新recycle_bin主按钮颜色
                                updateMainPermissionAllowBtnColorFrontend(perm.id, 'check_all_bin');
                                // 更新user_messages主按钮颜色
                                updateMainPermissionAllowBtnColorFrontend(perm.id, 'check_all_messages');
                                // 更新lead_crm主按钮颜色
                                updateMainPermissionAllowBtnColorFrontend(perm.id, 'check_person_crm');
                                // 更新check_shenpi主按钮颜色
                                updateMainPermissionAllowBtnColorFrontend(perm.id, 'check_all_shenpi');
                                // 更新gonghai_crm主按钮颜色
                                updateMainPermissionAllowBtnColorFrontend(perm.id, 'gonghai_crm');
                                // 更新gonghai_all_crm主按钮颜色
                                updateMainPermissionAllowBtnColorFrontend(perm.id, 'gonghai_all_crm');
                                // 更新gonghai_group_crm主按钮颜色
                                updateMainPermissionAllowBtnColorFrontend(perm.id, 'gonghai_group_crm');
                                // 更新gonghai_person_crm主按钮颜色
                                updateMainPermissionAllowBtnColorFrontend(perm.id, 'gonghai_person_crm');
                            });
                        }
                    } catch (error) {
                        console.error('页面初始化时重新渲染表格失败:', error);
                    }
                }
            }, 1000); // 给Vue一些时间初始化
        });
    </script>
    {% if no_permission %}
    <script>
        document.addEventListener('DOMContentLoaded', function () {
            // 使用ElementPlus的消息提示
            ElementPlus.ElMessage({
                message: "{{ permission_message }}",
                type: 'warning',
                duration: 1500,
                showClose: true
            });
        });
    </script>
    {% endif %}
</body>

</html>