<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>用户审核</title>
    <link href="/static/css/element-plus.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        body {
            margin: 0;
            padding: 0;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
            background-color: #f5f7fa;
        }

        .container {
            padding: 20px;
        }

        .header {
            margin-bottom: 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            position: relative;
        }

        .header h1 {
            margin: 0;
            color: #303133;
            font-size: 24px;
        }

        .content {
            background: #fff;
            padding: 20px;
            border-radius: 4px;
            box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
        }

        .user-table {
            width: 100%;
            border-collapse: collapse;
        }

        .user-table th,
        .user-table td {
            padding: 12px;
            text-align: center;
            border-bottom: 1px solid #ebeef5;
        }

        .action-buttons {
            display: flex;
            gap: 10px;
            justify-content: center;
            align-items: center;
        }

        .approve-btn,
        .reject-btn {
            padding: 6px 12px;
            border-radius: 4px;
            cursor: pointer;
            border: none;
            font-size: 14px;
            transition: all 0.2s ease;
        }

        .approve-btn {
            background-color: #67c23a;
            color: white;
            box-shadow: 0 2px 4px rgba(103, 194, 58, 0.2);
        }

        .approve-btn:hover {
            background-color: #85ce61;
            transform: translateY(-1px);
            box-shadow: 0 4px 8px rgba(103, 194, 58, 0.3);
        }

        .approve-btn:active {
            transform: translateY(0);
            box-shadow: 0 2px 4px rgba(103, 194, 58, 0.2);
        }

        .reject-btn {
            background-color: #f56c6c;
            color: white;
            box-shadow: 0 2px 4px rgba(245, 108, 108, 0.2);
        }

        .reject-btn:hover {
            background-color: #f78989;
            transform: translateY(-1px);
            box-shadow: 0 4px 8px rgba(245, 108, 108, 0.3);
        }

        .reject-btn:active {
            transform: translateY(0);
            box-shadow: 0 2px 4px rgba(245, 108, 108, 0.2);
        }

        .identity-input {
            padding: 8px 12px;
            font-size: 14px;
            border: 1px solid #dcdfe6;
            border-radius: 4px;
            background-color: #fff;
            color: #606266;
            width: 140px;
            transition: all 0.3s;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            cursor: pointer;
            appearance: none; /* 移除默认外观 */
            -webkit-appearance: none;
            -moz-appearance: none;
            background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="12" height="6" viewBox="0 0 12 6"><path fill="%23606266" d="M6 6L0 0h12z"/></svg>');
            background-repeat: no-repeat;
            background-position: right 10px center;
            padding-right: 30px;
        }

        .identity-input:hover {
            border-color: #409eff;
        }

        .identity-input:focus {
            outline: none;
            border-color: #409eff;
            box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.2);
        }

        /* 下拉框选项样式 */
        .identity-input option {
            padding: 8px 12px;
            font-size: 14px;
        }

        /* 突出显示当前选中的值 */
        .identity-input option:checked {
            background-color: #ecf5ff;
            color: #409eff;
        }

        .back-home-btn {
            padding: 8px 16px;
            background: linear-gradient(45deg, #409eff, #66b1ff);
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            box-shadow: 0 4px 8px rgba(64, 158, 255, 0.3);
            transition: all 0.3s ease;
            letter-spacing: 1px;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .back-home-btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 6px 12px rgba(64, 158, 255, 0.5);
        }

        .back-home-btn:active {
            transform: translateY(0);
            box-shadow: 0 4px 8px rgba(64, 158, 255, 0.3);
        }

        .filter-container {
            display: flex;
            gap: 10px;
            margin-left: auto;
            margin-right: 10px;
        }
        
        .filter-select {
            padding: 8px 12px;
            border: 1px solid #dcdfe6;
            border-radius: 4px;
            background-color: #fff;
            color: #606266;
            min-width: 120px;
            transition: all 0.3s;
        }
        
        .filter-select:hover {
            border-color: #409eff;
        }
        
        .filter-select:focus {
            outline: none;
            border-color: #409eff;
            box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.2);
        }

        /* 虚拟滚动容器样式 */
        .virtual-scroll-container {
            max-height: 700px; /* 容器最大高度 */
            overflow-y: auto;
            position: relative;
        }
        
        /* 固定表头 */
        .user-table thead tr th {
            position: sticky;
            top: 0;
            z-index: 10;
            background-color: #f5f7fa;
            border-bottom: 1px solid #ebeef5;
        }
        
        /* 空行样式 */
        .empty-row {
            padding: 0 !important;
            border: none !important;
            height: 0 !important;
            line-height: 0 !important;
        }
        
        /* 表格行过渡效果 */
        .row-fade-enter-active, .row-fade-leave-active {
            transition: opacity 0.3s;
        }
        
        .row-fade-enter-from, .row-fade-leave-to {
            opacity: 0;
        }

        .disabled-btn {
            background-color: #c0c4cc !important;
            cursor: not-allowed !important;
            opacity: 0.7;
            box-shadow: none !important;
        }

        .disabled-btn:hover {
            transform: none !important;
            box-shadow: none !important;
            background-color: #c0c4cc !important;
        }
    </style>
</head>

<body>
    <!-- 引入Loading组件 -->
    {% include 'components/loading.html' %}

    <div id="user-info" style="display: none;" data-user-info='{{ user_info | tojson }}'></div>
    <div class="container">
        <div class="header">
            <h1>用户审核</h1>
            <div class="filter-container">
                <!-- 部门筛选 -->
                <div class="filter-item" id="departmentFilter" style="display: none;">
                    <select class="filter-select" id="departmentSelect">
                        <option value="">全部部门</option>
                        <option value="总经办">总经办</option>
                        <option value="电商部">电商部</option>
                        <option value="新媒体部">新媒体部</option>
                        <option value="销售部">销售部</option>
                        <option value="技术部">技术部</option>
                        <option value="设计部">设计部</option>
                        <option value="施工部">施工部</option>
                        <option value="访客">访客</option>
                    </select>
                </div>
                <!-- 分组筛选 -->
                <div class="filter-item" id="groupFilter" style="display: none;">
                    <select class="filter-select" id="groupSelect">
                        <option value="">全部分组</option>
                        <!-- 分组选项将通过JavaScript动态添加 -->
                    </select>
                </div>
            </div>
            <button class="back-home-btn" onclick="window.location.href='home'">
                <i class="fas fa-home"></i> 返回首页
            </button>
        </div>
        <div class="content">
            <div class="virtual-scroll-container" id="userTableContainer">
                <table class="user-table">
                    <thead>
                        <tr>
                            <th>序号</th>
                            <th>姓名</th>
                            <th>部门</th>
                            <th>分组</th>
                            <th>账号</th>
                            <th>注册时间</th>
                            <th>身份</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody id="userTableBody">
                        <!-- 用户数据将通过JavaScript动态插入 -->
                    </tbody>
                </table>
            </div>
        </div>
        <div class="permission-modal" v-if="showPermissionModal">
            <div class="permission-content">
                <h2>权限管理</h2>
                <div class="permission-form">
                    <div class="form-group">
                        <label>角色名称:</label>
                        <input type="text" v-model="newRoleName" placeholder="输入角色名称">
                    </div>
                    <div class="form-group">
                        <label>选择部门:</label>
                        <select v-model="selectedDepartment">
                            <option v-for="dept in departments" :value="dept">{{ dept }}</option>
                        </select>
                    </div>
                    <div class="permission-list">
                        <h3>可分配权限</h3>
                        <div class="permission-item" v-for="perm in availablePermissions" :key="perm">
                            <label>
                                <input type="checkbox" :value="perm" v-model="selectedPermissions">
                                {{ perm }}
                            </label>
                        </div>
                    </div>
                    <div class="form-buttons">
                        <button @click="createRole">创建角色</button>
                        <button @click="closePermissionModal">关闭</button>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <script src="/static/js/fetch.js"></script>
    <script src="/static/js/vue.global.js"></script>
    <script src="/static/js/element-plus.js"></script>
    <script>
        const app = Vue.createApp({
            data() {
                return {
                    users: [],
                    allUsers: [], // 存储所有用户，不受筛选影响
                    currentUser: {
                        id: '',
                        name: '',
                        department: '',
                        is_admin: 3, // 默认为普通用户
                        group_name: ''
                    },
                    groupList: [], // 存储不重复的分组列表
                    identityList: [], // 存储不重复的身份列表
                    selectedDepartment: '', // 选中的部门
                    selectedGroup: '', // 选中的分组
                    
                    // 虚拟滚动相关配置
                    virtualScroll: {
                        itemHeight: 54, // 每行高度(px)，根据实际样式调整
                        visibleCount: 20, // 可见行数
                        startIndex: 0, // 开始索引
                        endIndex: 0, // 结束索引
                        bufferCount: 5, // 上下缓冲区行数
                        isScrolling: false, // 是否正在滚动
                        scrollTimer: null // 滚动节流定时器
                    },
                    reviewPermissions: {
                        review_all: 0,
                        review_group: 0
                    },
                    identityOptionsCache: {}, // 添加身份选项缓存
                }
            },
            mounted() {
                // 获取当前登录用户信息
                const userInfoElement = document.getElementById('user-info');
                if (userInfoElement && userInfoElement.dataset.userInfo) {
                    try {
                        this.currentUser = JSON.parse(userInfoElement.dataset.userInfo);
                        console.log('当前用户信息:', this.currentUser);
                    } catch (e) {
                        console.error('解析用户信息失败');
                    }
                }

                // 初始化部门下拉框的事件监听
                const departmentSelect = document.getElementById('departmentSelect');
                if (departmentSelect) {
                    departmentSelect.onchange = () => {
                        this.selectedDepartment = departmentSelect.value;
                        this.applyFilters();
                    };
                }

                // 如果用户是超级管理员或管理员，先获取分组列表和身份列表
                if (this.currentUser.is_admin <= 1) {
                    // 先获取身份列表和分组列表，再获取用户数据
                    Promise.all([
                        this.fetchGroupList(),
                        this.fetchIdentityList()
                    ]).then(() => {
                        this.fetchPendingUsers().then(() => {
                            // 数据加载完成后，初始化虚拟滚动
                            this.$nextTick(() => {
                                this.initVirtualScroll();
                            });
                        });
                    });
                } else {
                    this.fetchPendingUsers().then(() => {
                        // 数据加载完成后，初始化虚拟滚动
                        this.$nextTick(() => {
                            this.initVirtualScroll();
                        });
                    });
                }
                
                // 监听窗口大小变化，重新计算虚拟滚动参数
                window.addEventListener('resize', this.handleResize);

                // 获取审核权限后，预加载所有部门的身份选项
                this.fetchReviewPermissions().then(() => {
                    console.log('审核权限：', this.reviewPermissions);
                    // 预加载所有部门身份选项
                    this.preloadAllDepartmentIdentities();
                });
            },
            beforeUnmount() {
                // 移除窗口大小变化监听
                window.removeEventListener('resize', this.handleResize);
                
                // 移除表格滚动监听
                const container = document.getElementById('userTableContainer');
                if (container) {
                    container.removeEventListener('scroll', this.handleTableScroll);
                }
            },
            methods: {
                async fetchGroupList() {
                    try {
                        const response = await fetch('/api/group-list');
                        if (response.ok) {
                            this.groupList = await response.json();
                            console.log('获取到的分组列表:', this.groupList);
                            this.renderFilterOptions();
                            return this.groupList;
                        } else {
                            console.error('获取分组列表失败: 服务器返回非200状态码');
                            return [];
                        }
                    } catch (error) {
                        console.error('获取分组列表失败:', error);
                        return [];
                    }
                },
                async fetchIdentityList() {
                    try {
                        const response = await fetch('/api/identity-list');
                        if (response.ok) {
                            this.identityList = await response.json();
                            console.log('获取到的身份列表:', this.identityList);
                            return this.identityList;
                        } else {
                            console.error('获取身份列表失败: 服务器返回非200状态码');
                            return [];
                        }
                    } catch (error) {
                        console.error('获取身份列表失败:', error);
                        return [];
                    }
                },
                async fetchPendingUsers() {
                    try {
                        const response = await fetch('/api/pending-users');
                        if (response.ok) {
                            const users = await response.json();
                            // 按照注册时间从早到晚排序
                            this.allUsers = users.sort((a, b) => new Date(a.register_time) - new Date(b.register_time));
                            
                            // 根据权限过滤用户数据
                            this.users = this.allUsers.filter(user => {
                                // review_all=0 表示可以查看所有用户
                                if (this.reviewPermissions.review_all === 0) {
                                    return true;
                                }
                                
                                // review_all=1 && review_group=0 表示只能查看同组用户
                                if (this.reviewPermissions.review_group === 0) {
                                    return user.group_name === this.currentUser.group_name;
                                }
                                
                                // 如果 review_all=1 && review_group=1，则无权查看任何用户
                                return false;
                            });
                            
                            return true;
                        } else {
                            // 请求失败，可能是权限问题
                            this.renderNoPermissionMessage();
                            return false;
                        }
                    } catch (error) {
                        console.error('获取待审核用户失败:', error);
                        this.renderNoPermissionMessage();
                        return false;
                    }
                },
                applyFilters() {
                    // 先应用权限过滤
                    let filteredUsers = this.allUsers.filter(user => {
                        // review_all=0 表示可以查看所有用户
                        if (this.reviewPermissions.review_all === 0) {
                            return true;
                        }
                        
                        // review_all=1 && review_group=0 表示只能查看同组用户
                        if (this.reviewPermissions.review_group === 0) {
                            return user.group_name === this.currentUser.group_name;
                        }
                        
                        // 如果 review_all=1 && review_group=1，则无权查看任何用户
                        return false;
                    });
                    
                    // 再应用用户选择的筛选条件
                    this.users = filteredUsers.filter(user => {
                        // 部门筛选
                        const departmentMatch = !this.selectedDepartment || user.department === this.selectedDepartment;
                        
                        // 分组筛选 - 增加对"empty"特殊值的处理
                        const groupMatch = !this.selectedGroup ? true : 
                            this.selectedGroup === "empty" ? (!user.group_name || user.group_name === '') : 
                            user.group_name === this.selectedGroup;
                        
                        // 对于超级管理员，应用两个筛选条件
                        if (this.currentUser.is_admin === 0) {
                            return departmentMatch && groupMatch;
                        }
                        // 对于管理员，只应用分组筛选
                        else if (this.currentUser.is_admin === 1) {
                            return groupMatch;
                        }
                        // 其他用户不应用筛选
                        return true;
                    });
                    
                    // 重置虚拟滚动
                    this.virtualScroll.startIndex = 0;
                    this.virtualScroll.endIndex = 0;
                    
                    // 更新可见范围并重新渲染
                    this.$nextTick(() => {
                        this.updateVisibleRange();
                        this.renderUserTableVirtual();
                    });
                },
                renderUserTableVirtual() {
                    const tbody = document.getElementById('userTableBody');
                    if (!tbody) return;
                    
                    if (this.users.length === 0) {
                        if (this.currentUser.is_admin <= 1) {
                            tbody.innerHTML = `
                                <tr>
                                    <td colspan="8" style="text-align: center; padding: 20px;">
                                        当前没有待审核的用户
                                    </td>
                                </tr>
                            `;
                        } else {
                            this.renderNoPermissionMessage();
                        }
                        return;
                    }
                    
                    const visibleUsers = this.users.slice(
                        this.virtualScroll.startIndex,
                        this.virtualScroll.endIndex + 1
                    );
                    
                    // 使用固定高度计算，而不是动态计算
                    const totalHeight = this.users.length * this.virtualScroll.itemHeight;
                    const topPadding = this.virtualScroll.startIndex * this.virtualScroll.itemHeight;
                    const visibleHeight = visibleUsers.length * this.virtualScroll.itemHeight;
                    const bottomPadding = totalHeight - topPadding - visibleHeight;
                    
                    // 构建行HTML
                    const html = visibleUsers.map((user, localIndex) => {
                        const globalIndex = this.virtualScroll.startIndex + localIndex;
                        
                        // 替换为始终启用的按钮
                        const approveButtonClass = "approve-btn";
                        const approveButtonAttr = `onclick="app._instance.proxy.approveUser('${user.id}')"`;
                        
                        return `
                        <tr>
                            <td>${globalIndex + 1}</td>
                            <td>${user.name}</td>
                            <td>${user.department}</td>
                            <td>${user.group_name || '-'}</td>
                            <td>${user.username}</td>
                            <td>${new Date(user.register_time).toLocaleString()}</td>
                            <td>
                                <select class="identity-input" id="identity-${user.id}">
                                    <option value="">请选择身份</option>
                                    ${this.getIdentityOptions(user.department, user.identity)}
                                </select>
                            </td>
                            <td>
                                <div class="action-buttons">
                                    <button class="${approveButtonClass}" ${approveButtonAttr}>通过</button>
                                    <button class="reject-btn" onclick="app._instance.proxy.rejectUser('${user.id}')">拒绝</button>
                                </div>
                            </td>
                        </tr>
                        `;
                    }).join('');
                    
                    // 保存滚动位置，使用变量避免多次查询DOM
                    const container = document.getElementById('userTableContainer');
                    const scrollTop = container ? container.scrollTop : 0;
                    const scrollHeight = container ? container.scrollHeight : 0;
                    
                    // 使用单个表格行包含所有内容，避免多行造成的抖动
                    tbody.innerHTML = `
                        <tr class="empty-row" style="height: ${topPadding}px; padding: 0; border: none;"><td colspan="8"></td></tr>
                        ${html}
                        <tr class="empty-row" style="height: ${bottomPadding}px; padding: 0; border: none;"><td colspan="8"></td></tr>
                    `;
                    
                    // 仅在非滚动状态下恢复位置
                    if (container && !this.virtualScroll.isScrolling) {
                        // 使用RAF确保在浏览器重绘之前设置滚动位置
                        requestAnimationFrame(() => {
                            container.scrollTop = scrollTop;
                        });
                    }
                },
                getIdentityOptions(department, currentIdentity) {
                    // 如果没有部门，返回空字符串
                    if (!department) {
                        return '';
                    }

                    // 从缓存获取，避免发送请求
                    if (this.identityOptionsCache[department]) {
                        const cachedOptions = this.identityOptionsCache[department].map(identity => 
                            `<option value="${identity}" ${currentIdentity === identity ? 'selected' : ''}>${identity}</option>`
                        ).join('');
                        return cachedOptions;
                    }
                    
                    // 如果缓存中没有且未在加载中，发起一次性请求
                    if (!this.identityOptionsCache[department + '_loading']) {
                        this.identityOptionsCache[department + '_loading'] = true;
                        
                        // 使用单个请求获取部门身份
                        this.loadDepartmentIdentities(department);
                    }
                    
                    // 返回空选项，等待异步加载完成
                    return '';
                },
                loadDepartmentIdentities(department) {
                    fetch(`/api/identity-list?department=${encodeURIComponent(department)}`)
                        .then(response => {
                            if (!response.ok) throw new Error(`HTTP error! Status: ${response.status}`);
                            return response.json();
                        })
                        .then(identities => {
                            if (!identities || identities.length === 0) {
                                console.log(`部门 ${department} 没有可用的身份选项`);
                                this.identityOptionsCache[department] = [];
                                return;
                            }
                            
                            // 确保身份列表无重复(用Set去重)
                            const uniqueIdentities = [...new Set(identities)];
                            
                            // 缓存去重后的身份列表
                            this.identityOptionsCache[department] = uniqueIdentities;
                            
                            // 更新当前视图中的选择框 - 批量更新以提高性能
                            this.$nextTick(() => {
                                this.updateDepartmentSelects(department, uniqueIdentities);
                            });
                        })
                        .catch(error => {
                            console.error('获取身份列表失败:', error);
                            // 删除加载标记，允许下次重试
                            delete this.identityOptionsCache[department + '_loading'];
                        });
                },
                updateDepartmentSelects(department, identities) {
                    // 只获取一次DOM元素集合，减少DOM查询
                    const selects = document.querySelectorAll('.identity-input');
                    if (!selects.length) return;
                    
                    // 构建选项HTML一次，重复使用
                    const optionsHtml = identities.map(identity => 
                        `<option value="${identity}">${identity}</option>`
                    ).join('');
                    
                    // 更新所有相关部门的下拉框
                    selects.forEach(select => {
                        const userId = select.id.split('-')[1];
                        if (!userId) return;
                        
                        // 获取用户所在行
                        const userRow = select.closest('tr');
                        if (!userRow || !userRow.children || userRow.children.length < 3) return;
                        
                        // 检查部门列单元格(索引2是部门列)
                        const userDept = userRow.children[2].textContent.trim();
                        if (userDept === department) {
                            // 保留当前选择的值
                            const currentValue = select.value;
                            
                            // 更新选项
                            select.innerHTML = `<option value="">请选择身份</option>${optionsHtml}`;
                            
                            // 恢复原来选择的值
                            if (currentValue) select.value = currentValue;
                        }
                    });
                },
                renderNoPermissionMessage() {
                    const tbody = document.getElementById('userTableBody');
                    tbody.innerHTML = `
                        <tr>
                            <td colspan="8" style="text-align: center; padding: 20px;">
                                <div style="display: flex; flex-direction: column; align-items: center; gap: 10px;">
                                    <i class="fas fa-lock" style="font-size: 48px; color: #909399;"></i>
                                    <div style="font-size: 16px; color: #606266;">您没有权限查看用户审核列表</div>
                                    <div style="font-size: 14px; color: #909399;">只有管理员和超级管理员可以进行用户审核操作</div>
                                </div>
                            </td>
                        </tr>
                    `;
                },
                async approveUser(userId) {
                    try {
                        // 获取当前输入框中的身份值
                        const identityInput = document.getElementById(`identity-${userId}`);
                        const identity = identityInput ? identityInput.value.trim() : '';
                        
                        // 验证身份是否已选择
                        if (!identity) {
                            ElementPlus.ElMessage({
                                message: '请选择用户身份',
                                type: 'warning',
                                duration: 2000
                            });
                            return;
                        }
                        
                        // 执行审核通过逻辑
                        const response = await fetch(`/api/approve-user/${userId}`, {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json'
                            },
                            body: JSON.stringify({
                                identity: identity
                            })
                        });
                        if (response.ok) {
                            ElementPlus.ElMessage({
                                message: '用户审核通过！',
                                type: 'success',
                                duration: 2000
                            });
                            await this.fetchPendingUsers();
                            
                            // 用户列表更新后，重新计算虚拟滚动
                            this.$nextTick(() => {
                                this.updateVisibleRange();
                                this.renderUserTableVirtual();
                            });
                        } else {
                            // 尝试解析错误信息
                            let errorMessage = '审核操作失败';
                            try {
                                const errorData = await response.json();
                                errorMessage = errorData.detail || errorMessage;
                            } catch (e) {
                                // 如果无法解析JSON，使用默认错误信息
                                console.error('无法解析错误响应:', e);
                            }
                            
                            ElementPlus.ElMessage({
                                message: errorMessage,
                                type: 'error',
                                duration: 2000
                            });
                        }
                    } catch (error) {
                        ElementPlus.ElMessage({
                            message: '审核操作失败',
                            type: 'error',
                            duration: 2000
                        });
                        console.error('审核操作失败:', error);
                    }
                },
                async rejectUser(userId) {
                    try {
                        const response = await fetch(`/api/reject-user/${userId}`, {
                            method: 'POST'
                        });
                        if (response.ok) {
                            ElementPlus.ElMessage({
                                message: '已拒绝用户注册！',
                                type: 'warning',
                                duration: 2000
                            });
                            await this.fetchPendingUsers();
                            
                            // 用户列表更新后，重新计算虚拟滚动
                            this.$nextTick(() => {
                                this.updateVisibleRange();
                                this.renderUserTableVirtual();
                            });
                        }
                    } catch (error) {
                        ElementPlus.ElMessage({
                            message: '拒绝操作失败',
                            type: 'error',
                            duration: 2000
                        });
                        console.error('拒绝操作失败:', error);
                    }
                },
                renderFilterOptions() {
                    // 渲染部门过滤器
                    const departmentFilter = document.getElementById('departmentFilter');
                    if (departmentFilter && this.currentUser.is_admin === 0) {
                        departmentFilter.style.display = 'block';
                    } else if (departmentFilter) {
                        departmentFilter.style.display = 'none';
                    }
                    
                    // 渲染分组过滤器
                    const groupFilter = document.getElementById('groupFilter');
                    if (groupFilter && this.currentUser.is_admin === 0) {
                        groupFilter.style.display = 'block';
                        
                        // 获取分组选择器并清空现有选项
                        const groupSelect = groupFilter.querySelector('select');
                        groupSelect.innerHTML = '<option value="">全部分组</option>';
                        
                        // 添加特殊选项用于筛选空分组
                        const emptyOption = document.createElement('option');
                        emptyOption.value = "empty";
                        emptyOption.textContent = "-";
                        groupSelect.appendChild(emptyOption);
                        
                        // 对分组列表排序后添加选项
                        this.groupList.sort().forEach(group => {
                            const option = document.createElement('option');
                            option.value = group;
                            option.textContent = group;
                            groupSelect.appendChild(option);
                        });
                        
                        // 绑定变更事件
                        groupSelect.onchange = () => {
                            this.selectedGroup = groupSelect.value;
                            this.applyFilters();
                        };
                    } else if (groupFilter) {
                        groupFilter.style.display = 'none';
                    }
                },
                // 初始化虚拟滚动
                initVirtualScroll() {
                    const container = document.getElementById('userTableContainer');
                    if (!container) return;
                    
                    // 计算可见数量
                    this.virtualScroll.visibleCount = Math.ceil(container.clientHeight / this.virtualScroll.itemHeight);
                    
                    // 更新可见范围
                    this.updateVisibleRange();
                    
                    // 添加滚动监听
                    container.addEventListener('scroll', this.handleTableScroll);
                    
                    // 渲染表格
                    this.renderUserTableVirtual();
                    
                    console.log('虚拟滚动初始化完成', this.virtualScroll);
                },
                
                // 处理表格滚动
                handleTableScroll(e) {
                    if (this.virtualScroll.scrollTimer) {
                        clearTimeout(this.virtualScroll.scrollTimer);
                    }
                    
                    // 跳过频繁滚动事件
                    if (this.virtualScroll.isScrolling) return;
                    
                    this.virtualScroll.isScrolling = true;
                    
                    // 使用RAF而不是setTimeout
                    requestAnimationFrame(() => {
                        const container = e.target;
                        // 增大滚动区间检测阈值，减少渲染次数
                        const isNearBottom = container.scrollHeight - container.scrollTop - container.clientHeight < 40;
                        const isNearTop = container.scrollTop < 40;
                        
                        // 增大滚动节流时间，减少渲染频率
                        const delay = (isNearBottom || isNearTop) ? 200 : 100;
                        
                        this.virtualScroll.scrollTimer = setTimeout(() => {
                            this.updateVisibleRange();
                            this.renderUserTableVirtual();
                            this.virtualScroll.isScrolling = false;
                        }, delay);
                    });
                },
                
                // 处理窗口大小变化
                handleResize() {
                    const container = document.getElementById('userTableContainer');
                    if (!container) return;
                    
                    // 重新计算可见数量
                    this.virtualScroll.visibleCount = Math.ceil(container.clientHeight / this.virtualScroll.itemHeight);
                    
                    // 更新可见范围
                    this.updateVisibleRange();
                    
                    // 重新渲染表格
                    this.renderUserTableVirtual();
                },
                
                // 更新可见范围
                updateVisibleRange() {
                    const container = document.getElementById('userTableContainer');
                    if (!container) return;
                    
                    const scrollTop = container.scrollTop;
                    
                    // 计算开始索引
                    const startIndex = Math.max(0, Math.floor(scrollTop / this.virtualScroll.itemHeight) - this.virtualScroll.bufferCount);
                    
                    // 计算结束索引
                    const endIndex = Math.min(
                        this.users.length - 1,
                        startIndex + this.virtualScroll.visibleCount + this.virtualScroll.bufferCount * 2
                    );
                    
                    this.virtualScroll.startIndex = startIndex;
                    this.virtualScroll.endIndex = endIndex;
                },
                async fetchReviewPermissions() {
                    try {
                        const response = await fetch('/api/user-review-permissions');
                        if (response.ok) {
                            this.reviewPermissions = await response.json();
                            return this.reviewPermissions;
                        } else {
                            console.error('获取审核权限失败: 服务器返回非200状态码');
                            return { review_all: 0, review_group: 0 };
                        }
                    } catch (error) {
                        console.error('获取审核权限失败:', error);
                        return { review_all: 0, review_group: 0 };
                    }
                },
                showPermissionAlert() {
                    ElementPlus.ElMessage({
                        message: '暂无更多权限',
                        type: 'warning',
                        duration: 2000
                    });
                },
                // 添加预加载所有部门身份的方法
                preloadAllDepartmentIdentities() {
                    // 获取不重复的部门列表
                    const departments = [...new Set(this.allUsers.map(user => user.department))];
                    
                    // 批量加载所有部门的身份选项
                    console.log('预加载身份选项，部门列表:', departments);
                    
                    if (departments.length > 0) {
                        // 创建一个批量请求的Promise数组
                        const promises = departments.map(dept => {
                            if (!dept || this.identityOptionsCache[dept]) return Promise.resolve();
                            
                            console.log(`预加载部门 ${dept} 的身份选项`);
                            return fetch(`/api/identity-list?department=${encodeURIComponent(dept)}`)
                                .then(response => {
                                    if (!response.ok) throw new Error(`HTTP error! Status: ${response.status}`);
                                    return response.json();
                                })
                                .then(identities => {
                                    // 缓存返回的身份选项
                                    if (identities && identities.length > 0) {
                                        this.identityOptionsCache[dept] = [...new Set(identities)];
                                    } else {
                                        this.identityOptionsCache[dept] = [];
                                    }
                                })
                                .catch(error => {
                                    console.error(`预加载部门 ${dept} 身份选项失败:`, error);
                                });
                        });
                        
                        // 并行执行所有请求
                        Promise.all(promises).then(() => {
                            console.log('所有部门身份选项预加载完成');
                        });
                    }
                }
            }
        });

        app.mount('.container');
    </script>
    {% if no_permission %}
    <script>
        document.addEventListener('DOMContentLoaded', function () {
            // 使用ElementPlus的消息提示
            ElementPlus.ElMessage({
                message: "{{ permission_message }}",
                type: 'warning',
                duration: 1500,
                showClose: true
            });
        });
    </script>
    {% endif %}

    <!-- 引入Loading初始化脚本 -->
    <script src="/static/js/loading-init.js"></script>
    <script>
        // 初始化用户审核页面的Loading效果
        initPageLoading('user_review.html');
    </script>
</body>

</html>